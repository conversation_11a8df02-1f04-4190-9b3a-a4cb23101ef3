<template>
  <div class="app-status-bar__container app-theme__bg">
    <v-layout row justify-end align-center style="height: 100%">
      <img
        class="godesta-logo"
        alt="GoDesta"
        src="@/static/images/godesta_logo.png"
        @click="navigateHome"
      />

      <span class="ma-0 pa-0 portal-name" v-if="isAuthenticationRoute"
        ><b>USER PORTAL</b></span
      >
      <div class="ma-0 pa-0 portal-name" v-if="!isAuthenticationRoute">
        {{ activeUser }}
      </div>

      <v-spacer />
      <v-btn
        class="ma-0 logout-btn"
        v-if="!isAuthenticationRoute"
        flat
        dark
        @click="logout"
      >
        <v-icon color="#FFFFFF" size="12" class="pr-1 ma-0 pl-0"
          >fad fa-sign-out</v-icon
        >Logout</v-btn
      >

      <div class="right-app-bar" v-if="!isAuthenticationRoute">
        <div class="time-display-container">
          <date-time-display></date-time-display>
        </div>
        <v-divider vertical dark class="mx-2 divider-last" v-if="activeUser" />
        <div class="transparent">
          <ConnectionStatus></ConnectionStatus>
        </div>
      </div>

      <v-btn
        flat
        icon
        @click="setMenuOpen(!menuIsOpen)"
        class="right-menu-btn ma-0"
        v-if="!isAuthenticationRoute"
      >
        <v-icon size="20" v-if="!menuIsOpen">fas fa-bars</v-icon>
        <v-icon size="20" v-if="menuIsOpen">fas fa-times</v-icon>
      </v-btn>
    </v-layout>

    <UserNavBar
      class="menu-nav-bar"
      style="z-index: 0"
      :menuIsOpen="menuIsOpen"
      @closeMenu="setMenuOpen(false)"
    />
  </div>
</template>

<script setup lang="ts">
import UserNavBar from '@/App/layouts/user_portal_layout/components/user_nav_bar/user_nav_bar.vue';
import ConnectionStatus from '@/components/common/connection-status/connection_status.vue';
import DateTimeDisplay from '@/components/common/date-time-display/index.vue';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import * as jwt from 'jose';
import { computed, Ref, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

const router = useRouter();
const route = useRoute();

const menuIsOpen: Ref<boolean> = ref(true);
const authStore = useAuthenticationStore();

const emit = defineEmits<{
  (e: 'closeMenu', payload: boolean): void;
}>();

// Whether the current view is authentication related route.
const isAuthenticationRoute = computed(() => {
  return !(route.meta?.requiresAuth ?? false);
});

function navigateHome(): void {
  useAppNavigationStore().setCurrentRouteTitle('Home');
  router.push('/');
}
function logout(): void {
  emit('closeMenu', true);
  useOperationsStore().closeAllPopoutWindows();
  useAuthenticationStore().disconnectWebsocket();
}

function setMenuOpen(isOpen: boolean) {
  menuIsOpen.value = isOpen;
}

const activeUser = computed(() => {
  const token = authStore.authToken.accessToken;
  if (token !== '') {
    const decodedDivision = jwt.decodeJwt(token);
    return (
      decodedDivision.firstName +
      ' ' +
      decodedDivision.lastName
    ).toUpperCase();
  } else {
    return '';
  }
});
</script>

<style scoped lang="scss">
.app-status-bar__container {
  position: relative;
  height: $app-bar-height;
  color: var(--text-color);
  padding: 0 8px;
  z-index: 200;
  width: 100%;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.12);
  -webkit-transition: width 0.05s linear;
  transition: width 0.05s linear;
  transform: translateZ(0) scale(1, 1);
  -webkit-transform: translateZ(0) scale(1, 1);
  .app-status-bar__section {
    position: relative;
    height: 36px;
    background-color: #1d1d2c;
  }
}

.user-container {
  display: flex;
  align-items: center;
  color: #ffffff;
  font-size: 1rem;
  height: 100%;
  font-weight: 400;
}

.client-portal-nav-text {
  color: #ffffff;
  display: flex;
  align-items: center;
}

.godesta-logo {
  display: flex;
  height: 39px;
  align-items: center;
  padding: 0 12px 0 0;
}

.right-app-bar {
  display: none;
}

@media (min-width: $user-portal-desktop-breakpoint) {
  .portal-name {
    font-size: $font-size-13;
  }
  .user-container {
    font-size: $font-size-11;
  }
  .app-status-bar__section {
    display: none;
  }
  .divider-last {
    display: none;
  }

  .time-display-container {
    display: none;
  }

  .right-app-bar {
    display: flex;
    align-items: center;
  }

  .menu-nav-bar {
    display: none;
  }

  .right-menu-btn {
    display: none;
  }

  .right-app-bar {
    display: flex;
  }
  .logout-btn {
    display: none;
  }
}
</style>
