import ClientBreadCrumbs from '@/components/admin/SubcontractorIndex/components/client_bread_crumbs/index.vue';
import SubcontractorBreadCrumbs from '@/components/admin/SubcontractorIndex/components/subcontractor_bread_crumbs/index.vue';
import AppMenu from '@/components/common/app_menu/app_menu.vue';
import ConnectionStatus from '@/components/common/connection-status/connection_status.vue';
import DateTimeDisplay from '@/components/common/date-time-display/index.vue';
import DivisionSelect from '@/components/common/division-select/division_select.vue';
import OperationsActionButtons from '@/components/common/operations_action_buttons/operations_action_buttons.vue';
import DivisionFuelSurchargeSummary from '@/components/home/<USER>/division_fuel_surcharge_summary/division_fuel_surcharge_summary.vue';
import DivisionRatesSummary from '@/components/home/<USER>/division_rates_summary/index.vue';
import Environment from '@/configuration/environment';
import { BroadcastChannelType } from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useNetworkingStore } from '@/store/modules/NetworkingStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import * as jwt from 'jose';
import { Component, Vue, Watch } from 'vue-property-decorator';

@Component({
  components: {
    DateTimeDisplay,
    ConnectionStatus,
    DivisionSelect,
    OperationsActionButtons,
    SubcontractorBreadCrumbs,
    ClientBreadCrumbs,
    DivisionFuelSurchargeSummary,
    DivisionRatesSummary,
    AppMenu,
  },
})
export default class OperationsAppBar extends Vue {
  public companyDetailsStore = useCompanyDetailsStore();
  public operationsStore = useOperationsStore();
  public openMenu() {
    // do something when the main menu is opened
  }
  get isOperationsRoute() {
    if (this.$route.name === undefined) {
      return false;
    }
    return ['operations_index'].includes(this.$route.name as any);
  }

  get isOperationsDashboard() {
    if (this.$route.name === undefined) {
      return false;
    }
    return (
      this.$route.name === 'operations_index' &&
      useAppNavigationStore().currentComponentId === '#dashboard-main'
    );
  }

  get isLocalDevelopment(): boolean {
    return Environment.value('environment') === 'local';
  }

  get sectionTitle() {
    if (this.$route.name === undefined) {
      return '';
    }
    if (this.isOperationsRoute) {
      return useAppNavigationStore().subRouteTitle;
    } else {
      return this.$route.name;
    }
  }

  get dialogIsOpen() {
    return useNetworkingStore().showConnectionErrorDialog;
  }

  // whether the current route is a subcontractor route.
  public isSubcontractorRoute(): boolean {
    return (
      this.$route.name === 'Subcontractor' ||
      this.$route.name === 'Owner' ||
      this.$route.name === 'Driver' ||
      this.$route.name === 'Truck' ||
      this.$route.name === 'Trailer'
    );
  }
  // whether the current route is a client maintenance route.
  public isClientMaintenceRoute() {
    return (
      this.$route.name === 'Client' || this.$route.name === 'Client Details'
    );
  }

  get activeUser() {
    const token = useAuthenticationStore().authToken.accessToken;
    if (token !== '') {
      const decodedDivision = jwt.decodeJwt(token);
      return decodedDivision.firstName + ' ' + decodedDivision.lastName;
    } else {
      return '';
    }
  }
  get companyPhoneNumber() {
    if (!this.companyDetailsStore.companyDetails) {
      return null;
    }
    return this.companyDetailsStore.companyDetails.phone;
  }

  get companyMainWebsite() {
    if (!this.companyDetailsStore.companyDetails) {
      return null;
    }
    return;
  }

  get companyWebsiteUrl() {
    if (
      !this.companyDetailsStore.companyDetails ||
      !this.companyDetailsStore.companyDetails.companyWebsiteUrls ||
      !this.companyDetailsStore.companyDetails.companyWebsiteUrls[1]
    ) {
      return null;
    }
    return this.companyDetailsStore.companyDetails.companyWebsiteUrls[1];
  }

  get mainWebsiteUrl() {
    if (
      !this.companyDetailsStore.companyDetails ||
      !this.companyDetailsStore.companyDetails.companyWebsiteUrls ||
      !this.companyDetailsStore.companyDetails.companyWebsiteUrls[1]
    ) {
      return null;
    }
    return (
      'https://www.' +
      this.companyDetailsStore.companyDetails.companyWebsiteUrls[1]
    );
  }

  public logout() {
    this.operationsStore.closeAllPopoutWindows();
    useAuthenticationStore().disconnectWebsocket();
  }

  get companyLogo() {
    if (
      !this.companyDetailsStore.companyDetails ||
      !this.companyDetailsStore.companyDetails.brandIdentity ||
      !this.companyDetailsStore.companyDetails.brandIdentity.logo
    ) {
      return '';
    }
    return this.companyDetailsStore.companyDetails.brandIdentity.logo;
  }

  @Watch('dialogIsOpen')
  public errorDialogChanged(value: boolean) {
    if (value) {
      const updatedJob = new BroadcastMessage('closeWindow', true);
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.JOB_LIST,
        updatedJob,
      );
      useBroadcastChannelStore().closeChannel(BroadcastChannelType.JOB_LIST);

      // Close the fleet tracking broadcast channel if it is open
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.FLEET_TRACKING,
        updatedJob,
      );
      useBroadcastChannelStore().closeChannel(
        BroadcastChannelType.FLEET_TRACKING,
      );
    }
  }

  // Get the accent color defined in division details for the current logged in
  // division. If this value is not provided, the app bar will just be the
  // default color.
  get appBarColor(): string | undefined {
    // Return value from division theme config
    if (
      this.companyDetailsStore.divisionDetails &&
      this.companyDetailsStore.divisionDetails.customConfig &&
      this.companyDetailsStore.divisionDetails.customConfig.theme &&
      this.companyDetailsStore.divisionDetails.customConfig.theme.accentColor
    ) {
      return this.companyDetailsStore.divisionDetails.customConfig.theme
        .accentColor;
    }
  }
}
