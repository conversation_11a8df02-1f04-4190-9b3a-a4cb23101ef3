.app-status-bar__container {
  position: fixed;
  z-index: 200;
  top: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  height: $app-bar-height;
  padding-left: $app-nav-bar-width;
  -webkit-transition: width 0.05s linear;
  transition: width 0.05s linear;
  -webkit-transform: translateZ(0) scale(1, 1);
  border-radius: 10px !important;

  .app-status-bar__container--header {
    font-size: $font-size-16;
    font-weight: 600;
    line-height: 1;
    color: var(--light-text-color);
    text-transform: uppercase;
  }

  .app-status-bar__section {
    position: relative;
    border: 2px solid $border-color;
    border-radius: 6px;
    border-top: 0px;
    height: 36px;
    margin: 0px 4px;
    padding: 0px 4px;
    background-color: $light-theme-text;

    .transparent {
      border: 0px;
      background-color: transparent;
    }

    &:last-child {
      margin: 0px 0px 0px 4px;
    }
  }
}

.disableClickEvents {
  pointer-events: none;
}

.company-logo {
  width: auto;
  height: 30px;
  display: block;
}

.user-container {
  display: flex;
  align-items: center;
  color: var(--text-color);
  font-size: $font-size-large;
  height: 100%;

  font-weight: 400;
}

.client-portal-nav-bar {
  padding-left: 25px !important;
}

.client-portal-nav-text {
  color: var(--text-color);
  display: flex;
  align-items: center;
}

.client-portal-nav-btn {
  color: var(--text-color);
}

.keyboardIcon {
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
  margin: 4px;

  &:hover {
    transform: scale(1.2);
  }
}

.key-list__legend {
  font-weight: bold;
  font-size: 1.2em;
  color: var(--text-color);
}

.key-list__legend--head {
  font-weight: bold;
  font-size: 1.1em;
  color: var(--bg-light);
}

.key-list__legend--key {
  font-family: monospace;
  background-color: var(--background-color-600);
  padding: 2px 4px;
  border-radius: 4px;
  border: 2.5px solid $translucent;
  margin: 3px;
  color: var(--light-text-color);
  box-shadow: $shadow-primary;
}

.key-list__legend--label {
  color: var(--light-text-color);
  padding-left: 8px;
}

.indicator-icon-container {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}
