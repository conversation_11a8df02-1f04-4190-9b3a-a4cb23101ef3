<div
  class="app-status-bar__container app-theme__bg"
  :class="{disableClickEvents: dialogIsOpen}"
>
  <v-layout row justify-end align-center style="height: 100%">
    <AppMenu v-if="!dialogIsOpen"></AppMenu>

    <span
      class="app-status-bar__container--header"
      v-if="!dialogIsOpen && !isSubcontractorRoute() && !isClientMaintenceRoute()"
      >{{sectionTitle}}</span
    >

    <SubcontractorBreadCrumbs
      class="app-status-bar__container--header"
      v-if="isSubcontractorRoute()"
    />
    <ClientBreadCrumbs
      class="app-status-bar__container--header"
      v-if="isClientMaintenceRoute()"
    />
    <v-spacer />

    <div class="app-status-bar__section">
      <v-layout align-center fill-height>
        <OperationsActionButtons v-if="isOperationsRoute" />
        <v-divider vertical class="mx-2" v-if="isOperationsRoute" />
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <v-icon size="24" v-on="on" color="white" class="keyboardIcon"
              >keyboard</v-icon
            >
          </template>
          <div>
            <v-layout justify-center class="key-list__legend">
              <span>Keyboard Shortcuts</span></v-layout
            >
            <v-divider class="pb-1"></v-divider>
            <v-layout align-center class="pb-1">
              <v-icon size="10" color="#"> arrow_right </v-icon>
              <span class="key-list__legend--head pl-2">Navigation</span>
            </v-layout>
            <v-layout align-center class="pb-1">
              <span class="key-list__legend--key"> Alt + C</span>
              <span class="key-list__legend--label pl-2">Client Details</span>
            </v-layout>
            <v-layout align-center class="pb-1">
              <span class="key-list__legend--key"> Alt + V</span>
              <span class="key-list__legend--label pl-2">Fleet Details</span>
            </v-layout>
            <v-layout align-center class="pb-1">
              <span class="key-list__legend--key"> Alt + A</span>
              <span class="key-list__legend--label pl-2">Accounting</span>
            </v-layout>
            <v-layout align-center class="pb-1">
              <span class="key-list__legend--key"> Alt + I</span>
              <span class="key-list__legend--label pl-2">Division</span>
            </v-layout>
            <v-layout align-center class="pb-1">
              <span class="key-list__legend--key"> Alt + S</span>
              <span class="key-list__legend--label pl-2">Search Job</span>
            </v-layout>
            <v-layout align-center class="pb-1">
              <span class="key-list__legend--key"> Alt + J</span>
              <span class="key-list__legend--label pl-2">Job Lists</span>
            </v-layout>
            <v-layout align-center class="pb-1">
              <span class="key-list__legend--key"> Alt + B</span>
              <span class="key-list__legend--label pl-2">Book Job</span>
            </v-layout>
            <v-layout align-center class="pb-1">
              <span class="key-list__legend--key"> Alt + T</span>
              <span class="key-list__legend--label pl-2">Fleet Tracking</span>
            </v-layout>
          </div>
        </v-tooltip>
        <v-divider vertical class="mx-2" />
        <DivisionFuelSurchargeSummary
          :isAppStatusBar="true"
        ></DivisionFuelSurchargeSummary>
        <v-divider vertical class="mx-2" />
        <DivisionRatesSummary :isAppStatusBar="true"></DivisionRatesSummary>
      </v-layout>
    </div>
    <div
      class="app-status-bar__section"
      :style="{ backgroundColor: appBarColor }"
    >
      <v-layout align-center fill-height>
        <DivisionSelect />

        <span class="px-1">
          <date-time-display></date-time-display>
        </span>
        <span style="position: relative">
          <ConnectionStatus></ConnectionStatus
        ></span>
      </v-layout>
    </div>
  </v-layout>
</div>
