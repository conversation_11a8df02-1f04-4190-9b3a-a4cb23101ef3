export enum BroadcastChannelType {
  FLEET_TRACKING = 'FLEET_TRACKING',
  JOB_LIST = 'JOB_LIST',
}

// Grouped by channel type and direction
export const BroadcastIds = {
  JOB_LIST: {
    TO_EXTERNAL: {
      FULL_JOB_LIST: 'fullJobList',
      UNASSIGNED_PUD_ITEM_LIST: 'unassignedPudItemList',
      FLEET_ASSET_LIST: 'fleetAssetList',
      ALL_DRIVERS: 'allDrivers',
      STATUS_TYPE_LIST: 'statusTypeList',
      UPDATED_JOB_DETAILS: 'updatedJobDetails',
      PREALLOCATE_FROM_WINDOW_RESPONSE: 'sendAllocateRequestFromWindowResponse',
      GET_FLEET_RATES_FROM_WINDOW_RESPONSE:
        'getActiveFleetAssetServiceRatesFromWindowResponse',
      // USER_TIME_ZONE: 'userTimeZone',
      ACTIVE_USER_NAME: 'activeUserName',
      ROUTE_CHANGED: 'routeChanged',
      INITIAL_DATE_FILTER: 'initialDateFilter',
      CLOSE_WINDOW: 'closeWindow',
      SELECTED_JOB_DETAILS: 'selectedJobDetails',
      SERVICE_TYPES_LIST: 'serviceTypesList',
      CLIENT_LIST: 'clientSummaryList',
      DIVISION_DETAILS: 'divisionDetails',
      OWNER_LIST: 'fleetAssetOwnerList',
      SEND_ADD_NOTE_RESPONSE: 'sendAddNoteToJobResponse',
    },
    TO_MAIN: {
      JOB_LIST_WINDOW_OPEN: 'jobListWindowOpen',
      SELECTED_JOB_ID: 'selectedJobId',
      PREALLOCATE_FROM_WINDOW: 'sendAllocateRequestFromWindow',
      GET_FLEET_RATES_FROM_WINDOW: 'getActiveFleetAssetServiceRatesFromWindow',
      ALLOCATE_PREALLOCATED_JOB: 'allocatePreallocatedJob',
      DEALLOCATE_JOB: 'deallocateJob',
      ADD_NOTE_TO_JOB: 'addNoteToJob',
      SEND_MESSAGE_TO_DRIVER: 'sendMessageToDriver',
      VIEW_SELECTED_JOB_IN_REVIEW: 'viewSelectedJobInReview',
      VIEW_JOB_DETAILS_DIALOG: 'viewJobDetailsDialog',
      EDIT_JOB_IN_BOOKING_SCREEN: 'editJobInBookingScreen',
      SEND_ADD_NOTE_REQUEST: 'sendAddNoteToJob',
      // VIEW_PUD_MAINTENANCE_DIALOG: 'viewPudMaintenanceDialog', // Uncomment if used
    },
  },
  FLEET_TRACKING: {
    TO_EXTERNAL: {
      INITIAL_MAP_DATA: 'initialMapData',
      DRIVER_GPS_DATA: 'driverGpsData',
      RESPONSE_JUMP_TO_LOCATION: 'responseJumpToLocation',
      CLOSE_WINDOW: 'closeWindow',
      VEHICLE_FILTER_UPDATED: 'vehicleFilterUpdated',
      CLIENT_FILTER_UPDATED: 'clientFilterUpdated',
      FLEET_FILTER_UPDATED: 'fleetFilterUpdated',
      DRIVER_FATIGUE_SNAPSHOTS: 'driverFatigueSnapshots',
      UPDATED_DRIVER_FATIGUE_SNAPSHOT: 'updatedDriverFatigueSnapshot',
      REVERSE_GEOCODE_LIST_RESPONSE: 'reverseGeocodeListResponse',
    },
    TO_MAIN: {
      FLEET_TRACKING_WINDOW_OPEN: 'fleetTrackingWindowOpen',
      VEHICLE_FILTER_UPDATED_FROM_WINDOW: 'vehicleFilterUpdatedFromWindow',
      CLIENT_FILTER_UPDATED_FROM_WINDOW: 'clientFilterUpdatedFromWindow',
      FLEET_FILTER_UPDATED_FROM_WINDOW: 'fleetFilterUpdatedFromWindow',
      MARKER_SELECTED_ON_MAP: 'markerSelectedOnMap',
      REQUEST_REVERSE_GEOCODE_LIST: 'requestReverseGeocodeList',
      WINDOW_CLOSED: 'windowClosed',
    },
  },
};

export const FLEET_TRACKING_CHANNEL_ID = 'fleetTracking';
export const JOB_LIST_CHANNEL_ID = 'operations';

/**
 * Returns a string which will be used as the channel id for the job list popout
 * window. This helps to enforce consistency across the app when subscribing to
 * a broadcast channel topic.
 * @param userName - the user name of the logged in user, from
 * sessionManager.getUserName()
 * @param jtiSessionId - the JTI session ID of the logged in user, from
 * sessionManager.getJtiSessionId()
 * @returns - a string which will be used as the channel id for the job list
 * popout window broadcast channel
 * @throws - an error if the userName or jtiSessionId is not provided
 */
export function returnOperationsBroadcastChannelId(
  userName: string,
  jtiSessionId: string,
) {
  if (!userName || !jtiSessionId) {
    throw new Error(
      'User name and JTI session ID are required to create a channel ID.',
    );
  }
  return `${JOB_LIST_CHANNEL_ID}-${userName}-${jtiSessionId}`;
}
