import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { returnServiceTypeLongNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import { ValidityStatus } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ValidityStatus';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import RateRequest from '@/interface-models/ServiceRates/RateRequest';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import { defineStore } from 'pinia';

export const useFuelLevyStore = defineStore('fuelLevyStore', () => {
  /**
   * Saves a full ClientFuelSurchargeRate over websocket and listens for the
   * response.
   *
   * @param fuelSurcharge - The fuel surcharge rate to be saved.
   * @returns A promise that resolves to the saved fuel surcharge rate, or null
   * if an error occurs.
   */
  async function saveClientFuelSurchargeRate(
    fuelSurcharge: ClientFuelSurchargeRate,
  ): Promise<ClientFuelSurchargeRate | null> {
    if (!fuelSurcharge.clientIds.length) {
      showNotification(GENERIC_ERROR_MESSAGE);
      logConsoleError(
        'Client ID is required to save Client Fuel Surcharge Rate',
      );
      return null;
    }

    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientFuelSurchargeRate/save',
          fuelSurcharge,
          true,
        ),
        'savedClientFuelSurchargeRates',
        {
          mapResponse: (response) =>
            !response ||
            (!!fuelSurcharge.id && fuelSurcharge.id === response.id) ||
            response.uuid === fuelSurcharge.uuid,
        },
      );
      // Return a new instance of ClientFuelSurchargeRate if result is valid,
      // else return null
      if (result) {
        return new ClientFuelSurchargeRate(result);
      } else {
        return null;
      }
    } catch (error) {
      logConsoleError('Error saving Client Fuel Surcharge Rate', error);
      return null;
    }
  }

  /**
   * Requests a list of all ClientFuelSurchargeRates for a given clientId.
   * Backend uses COMPANY and DIVISION from the token to query for a list of all
   * matching ClientFuelSurchargeRate documents.
   * @param clientId clientId to match against.
   * @returns A promise that resolves to a list of ClientFuelSurchargeRates, or
   * null if an error occurs.
   */
  async function getClientFuelSurchargeList(
    clientId: string,
  ): Promise<ClientFuelSurchargeRate[] | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientFuelSurchargeRate/getAll',
          clientId,
          false,
        ),
        'selectedAllClientFuelSurchargeRatesList',
      );
      return result?.map((item) => new ClientFuelSurchargeRate(item)) ?? null;
    } catch (error) {
      logConsoleError('Error getting Client Fuel Surcharge List', error);
      return null;
    }
  }

  /**
   * Requests and response for getting all current client fuel surcharge
   * rates for the division. This is for use in the division fuel administration
   * screens
   * @returns A promise that resolves to a list of ClientFuelSurchargeRates, or
   * null if an error occurs.
   */
  async function getAllClientDivisionFuelSurcharges(
    status: ValidityStatus,
  ): Promise<ClientFuelSurchargeRate[] | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/clientFuelSurchargeRate/listAll', status, true),
        'latestClientFuelSurchargeRateList',
      );
      return result?.map((item) => new ClientFuelSurchargeRate(item)) ?? null;
    } catch (error) {
      logConsoleError('Error getting all Fuel Surcharges for division', error);
      return null;
    }
  }

  /**
   * Sends request to query for a single ClientFuelSurchargeRate document which
   * matches the provided clientId, and is valid for the provided epoch time
   * searchDate. The backend logic follows the following process:
   * 1. Search for ClientFuelSurchargeRate objects for the provided clientId
   *    that are valid for the searchDate
   * 2. If none are found, find division-level ClientFuelSurchargeRate that is
   *    valid for the searchDate.
   * 3. If no valid division-level ClientFuelSurchargeRate is found, then we
   *    check ClientDetails.expiredFuelSurchargeDefaultsToDivisionRate to see if
   *    we should default to their expired Fuel Surcharge, or default to
   *    division-level fuel surcharge
   * 4. If we default to their expired Fuel Surcharge, then find all of their
   *    Fuel Surcharges and grab the most recent one. If that can't be found
   *    return null.
   * 5. If we default to division-level Fuel Surcharge, then query for Fuel
   *    Surcharges with clientId of "0", which indicates division-level fuel
   *    surcharge, for the searchDate.
   * 6. Return null if no division-level are found for searchDate.
   * @param clientId clientId to match the document again
   * @param searchDate epoch time to search for valid fuel surcharge rate
   * @returns A promise that resolves to a ClientFuelSurchargeRate, or null if
   * none is found or an error occurred
   */
  async function getCurrentClientFuelSurcharges(
    clientId: string | undefined,
    searchDate: number,
  ): Promise<ClientFuelSurchargeRate[] | null> {
    try {
      if (!clientId) {
        logConsoleError(
          'Client ID is required to get Current Client Fuel Surcharge',
        );
        return null;
      }
      let result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientFuelSurchargeRate/getFuelSurchargeRate',
          { clientId, searchDate },
          true,
        ),
        'selectedCurrentClientFuelSurchargeRates',
        {
          mapResponse: (response) => {
            if (!response?.length) {
              return true;
            }
            // Check that all of the responses clientIds field either contains
            // the clientId or contains "0" for division-level rates
            return response.every((item) => {
              return (
                item.clientIds.includes(clientId) ||
                item.clientIds.includes('0')
              );
            });
          },
        },
      );

      // Return a new instance of ClientFuelSurchargeRate if result is valid,
      // else return null
      if (result) {
        result = result.map((item) => new ClientFuelSurchargeRate(item));
        // TODO: Debugging only - remove in production
        console.table(
          result.map((item) => ({
            ...item,
            validFrom: returnFormattedDate(item.validFromDate!),
            validTo: returnFormattedDate(item.validToDate!),
            services:
              item.serviceTypes
                ?.map((s) => returnServiceTypeLongNameFromId(s))
                .join(', ') ?? 'All',
          })),
        );

        return result;
      } else {
        return null;
      }
    } catch (error) {
      logConsoleError('Error getting Current Client Fuel Surcharge', error);
      return null;
    }
  }

  /**
   * Saves a full FleetAssetFuelSurchargeRate over websocket and listens for the
   * response.
   *
   * @param fuelSurcharge - The fuel surcharge rate to be saved.
   * @returns A promise that resolves to the saved fuel surcharge rate, or null
   * if an error occurs.
   */
  async function saveFleetAssetFuelSurchargeRate(
    fuelSurcharge: FleetAssetFuelSurchargeRate,
  ): Promise<FleetAssetFuelSurchargeRate | null> {
    if (!fuelSurcharge.fleetAssetIds.length) {
      showNotification(GENERIC_ERROR_MESSAGE);
      logConsoleError(
        'Fleet Asset ID is required to save Fleet Asset Fuel Surcharge Rate',
      );
      return null;
    }
    // delete fuelSurcharge.clientId;
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetFuelSurchargeRate/save',
          fuelSurcharge,
          true,
        ),
        'savedFleetAssetFuelSurchargeRates',
      );
      return result ? new FleetAssetFuelSurchargeRate(result) : null;
    } catch (error) {
      logConsoleError('Error saving Fleet Asset Fuel Surcharge Rate', error);
      return null;
    }
  }

  /**
   * Requests a list of all FleetAssetFuelSurchargeRate for a given fleetAssetId.
   * Backend uses COMPANY and DIVISION from the token to query for a list of all
   * matching FleetAssetFuelSurchargeRate documents.
   * @param fleetAssetId fleetAssetId (from FleetAsset.fleetAssetId) to match against.
   * @returns A promise that resolves to a list of FleetAssetFuelSurchargeRate, or
   * null if an error occurs.
   */
  async function getAllFleetAssetFuelSurchargeRates(
    fleetAssetId: string,
  ): Promise<FleetAssetFuelSurchargeRate[] | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetFuelSurchargeRate/getAll',
          fleetAssetId,
          false,
        ),
        'selectedAllFleetAssetFuelSurchargeRatesList',
      );
      return result
        ? result.map((item) => new FleetAssetFuelSurchargeRate(item))
        : null;
    } catch (error) {
      logConsoleError('Error getting Fleet Asset Fuel Surcharge Rates', error);
      return null;
    }
  }

  /**
   * Sends request to query for a single FleetAssetFuelSurchargeRate document which
   * matches the provided fleetAssetId, and is valid for the provided epoch time
   * searchDate.
   * @param fleetAssetId fleetAssetId (from FleetAsset.fleetAssetId) to match against.
   * @param searchDate epoch time to search for valid fuel surcharge rate
   * @returns A promise that resolves to a FleetAssetFuelSurchargeRate, or null if
   * none is found or an error occurred
   */
  async function getCurrentFleetAssetFuelSurchargeRates(
    fleetAssetId: string,
    searchDate: number,
  ): Promise<FleetAssetFuelSurchargeRate[] | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetFuelSurchargeRate/getFuelSurchargeRates',
          new RateRequest(fleetAssetId, searchDate),
          true,
        ),
        'selectedCurrentFleetAssetFuelSurchargeRates',
        {
          mapResponse: (response) => {
            if (!response) {
              return true;
            } else if (
              response.every((item) => {
                return (
                  item.fleetAssetIds.includes(fleetAssetId) ||
                  item.fleetAssetIds.includes('0')
                );
              })
            ) {
              return true;
            } else {
              return false;
            }
          },
        },
      );
      return result
        ? result.map((item) => new FleetAssetFuelSurchargeRate(item))
        : null;
    } catch (error) {
      logConsoleError(
        'Error getting Current Fleet Asset Fuel Surcharge Rate',
        error,
      );
      return null;
    }
  }
  /**
   * Requests and response for getting all current fleet asset fuel surcharge
   * rates for the division. This is for use in the division fuel administration
   * screens.
   * @returns A promise that resolves to a list of FleetAssetFuelSurchargeRate, or
   * null if an error occurs.
   */
  async function getAllDivisionFleetAssetFuelSurchargeRates(status): Promise<
    FleetAssetFuelSurchargeRate[] | null
  > {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetFuelSurchargeRate/listAll',
          null,
          false,
        ),
        'latestFleetAssetFuelSurchargeRateList',
      );
      return (
        result?.map((item) => new FleetAssetFuelSurchargeRate(item)) ?? null
      );
    } catch (error) {
      logConsoleError(
        'Error getting all Fleet Asset Fuel Surcharges for division',
        error,
      );
      return null;
    }
  }

  return {
    saveClientFuelSurchargeRate,
    getClientFuelSurchargeList,
    getCurrentClientFuelSurcharges,
    saveFleetAssetFuelSurchargeRate,
    getAllFleetAssetFuelSurchargeRates,
    getCurrentFleetAssetFuelSurchargeRates,
    getAllClientDivisionFuelSurcharges,
    getAllDivisionFleetAssetFuelSurchargeRates,
  };
});
