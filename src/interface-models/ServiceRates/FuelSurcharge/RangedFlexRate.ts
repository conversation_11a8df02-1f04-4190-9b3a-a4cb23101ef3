import { FuelLevyChargeBasis } from '@/interface-models/ServiceRates/FuelSurcharge/FuelLevyChargeBasis';

export interface RangedFlexRate {
  /**
   * The unique identifier (uuid) for the rate bracket. Referenced by
   * appliedRateBracketId in ClientFuelSurchargeRate to identify which rate
   * bracket is applied to a job.
   */
  bracketId: string;
  /**
   * The minimum value (km) of the range. For a list of ranges, the first bracketMin should always be zero.
   */
  bracketMin: number;

  /**
   * The maximum value (km) of the range. For a list of ranges, the last bracketMax should always be -1, which
   * indicates that it extends to infinity.
   */
  bracketMax: number;

  /**
   * Charge type for calculation. Can be PERCENTAGE or FIXED_CHARGE.
   */
  chargeType: FuelLevyChargeBasis;

  /**
   * The dollar rate (per km) that applies to the range.
   */
  rate: number;
}
