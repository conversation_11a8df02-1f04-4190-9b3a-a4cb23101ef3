import JobDistanceTravelled from '@/interface-models/Jobs/JobDistanceTravelled';

/**
 * Contains details that are required to calculate the fuel surcharge for a job.
 * This includes the distance travelled, estimated distance, suburb centres
 * distance, and actual time taken for the job.
 */
export interface JobRangeDeterminantValues extends JobDistanceTravelled {
  /**
   * The actual time taken for the job in milliseconds. This is the time
   * captured throughout the course of a job, depending on the epochTime on the
   * puds or from actual event times, depending on the status of the job.
   */
  timeTaken: number;
}
