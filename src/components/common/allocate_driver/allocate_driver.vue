<template>
  <v-layout
    class="allocate-driver"
    :class="{
      'error-state': props.small && vehicleInputErrorMessages.length > 0,
    }"
  >
    <v-flex
      v-for="(input, index) in inputOrder"
      :key="input"
      md6
      class="input-container"
      :class="{ 'input-container--compact': props.small }"
    >
      <template v-if="input === InputType.VEHICLE">
        <v-autocomplete
          v-model="selectedFleetAssetId"
          :key="input"
          ref="vehiclePreAllocation"
          :items="availableFleetAssets"
          item-value="fleetAssetId"
          item-text="displayName"
          :autofocus="
            !selectedFleetAssetId &&
            allocationOrder === JobAllocationOrder.VEHICLE_FIRST
          "
          :auto-select-first="index === 0"
          :error-messages="vehicleInputErrorMessages"
          color="orange"
          class="v-solo-custom"
          :class="{ 'v-solo-custom--compact': props.small }"
          solo
          flat
          dense
          clearable
          prefix="Truck"
          no-data-text="No vehicles found."
          :loading="isValidatingVehicleSelection"
          :disabled="isFleetAssetInputDisabled"
          @click:clear="clearInputs"
          @keypress="handleEnterEvent"
        >
        </v-autocomplete>
      </template>
      <template v-else-if="input === InputType.DRIVER">
        <v-autocomplete
          v-model="selectedDriverId"
          :key="input"
          ref="driverPreAllocation"
          :items="availableDrivers"
          item-value="driverId"
          item-text="displayName"
          :autofocus="
            !selectedDriverId &&
            allocationOrder === JobAllocationOrder.DRIVER_FIRST
          "
          :auto-select-first="index === 0"
          :id="driverTextfieldId"
          solo
          flat
          clearable
          color="orange"
          prefix="Driver"
          :disabled="isDriverInputDisabled"
          no-data-text="No drivers found."
          validate-on-blur
          class="v-solo-custom"
          :class="{ 'v-solo-custom--compact': props.small }"
          @click:clear="clearInputs"
          @keypress="handleEnterEvent"
        >
        </v-autocomplete>
      </template>
    </v-flex>

    <input
      type="text"
      tabindex="0"
      class="submit-invisible-input"
      @focus="handleSubmitButtonFocus"
    />
    <ContentDialog
      :showDialog.sync="outsideHireDialogController"
      title="Outside Hire - Driver Details"
      width="40%"
      contentPadding="pa-0"
      @cancel="outsideHireDialogController = false"
      @confirm="sendOutsideHireNote"
      :showActions="true"
      :isConfirmUnsaved="false"
      :isDisabled="false"
      :isLoading="isAwaitingSaveNoteResponse"
      confirmBtnText="Confirm"
    >
      <v-form ref="outsideHireForm">
        <v-flex md12 class="body-scrollable--75 pa-3" v-if="outsideHireDetails">
          <v-layout wrap>
            <FormFieldRow label="Driver Name" :required="true">
              <v-text-field
                class="v-solo-custom"
                solo
                flat
                label="Driver Name"
                v-model.trim="outsideHireDetails.driverName"
                :rules="[validationRules.required]"
                ref="outsideHireNameTextfield"
              ></v-text-field>
            </FormFieldRow>
            <FormFieldRow label="Contact Number" :required="true">
              <v-text-field
                class="v-solo-custom"
                solo
                flat
                label="Contact Number"
                v-model.trim="outsideHireDetails.contactNumber"
                :rules="[validationRules.required, validationRules.AUSMobile]"
                v-mask="'0### ### ###'"
              ></v-text-field>
            </FormFieldRow>
            <FormFieldRow label="Registration Number" :required="true">
              <v-text-field
                class="v-solo-custom"
                solo
                flat
                label="Registration Number"
                v-model.trim="outsideHireDetails.registrationNumber"
                :rules="[validationRules.required]"
              ></v-text-field>
            </FormFieldRow>
          </v-layout>
        </v-flex>
      </v-form>
    </ContentDialog>
  </v-layout>
</template>

<script setup lang="ts">
interface FleetAssetOption {
  fleetAssetId: string;
  displayName: string;
  defaultDriver?: string | null;
}
interface FleetAssetEntityOption {
  entityId: string;
  displayName: string;
}

enum InputType {
  VEHICLE,
  DRIVER,
}

interface PrimaryRateWrapper {
  fleetAssetId: string;
  searchDate: number;
  primaryRate: JobPrimaryRate;
}

import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import FormFieldRow from '@/components/common/ui-elements/form_field_row.vue';
import { getActiveVehicleForDriver } from '@/components/operations/OperationDashboard/components/DriverList/Helpers/FleetListHelpers';
import {
  addOutsideHireNoteToJob,
  ObjectToAllocate,
  OnValidAllocationTarget,
  OutsideHireDetails,
  returnJobPrimaryRateForAllocation,
  sendPreallocationRequest,
} from '@/helpers/AllocationHelpers.ts/AllocationHelpers';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { JobAllocationOrder } from '@/interface-models/Company/DivisionCustomConfig/Operations/JobAllocationOrder';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { AllocationSummary } from '@/interface-models/Jobs/Allocation/AllocateJobRequest';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  nextTick,
  onBeforeMount,
  Ref,
  ref,
  WritableComputedRef,
} from 'vue';

/**
 * Props for the driver allocation component.
 */
const props = withDefaults(
  defineProps<{
    type: ObjectToAllocate;
    onValidSelection: OnValidAllocationTarget;
    fleetAssetId: string;
    driverId: string;
    serviceTypeId: number;
    rateTypeId: number;
    fleetAssetRates: JobPrimaryRate[];
    clientId: string;
    searchDate: number;
    isFormDisabled?: boolean;
    isExternalWindow?: boolean;
    jobId?: number;
    small?: boolean; // For compact mode
  }>(),
  {
    isFormDisabled: false,
    isExternalWindow: false,
    jobId: undefined,
    small: false,
  },
);

const emit = defineEmits<{
  (event: 'update:fleetAssetId', value: string): void;
  (event: 'update:driverId', value: string): void;
  (event: 'submitPreallocation', value: AllocationSummary | null): void;
}>();

const fleetAssetStore = useFleetAssetStore();
const driverDetailsStore = useDriverDetailsStore();

const primaryRateToApply: Ref<PrimaryRateWrapper | null> = ref(null);

const availableFleetAssets: Ref<FleetAssetOption[]> = ref([]);
const availableDrivers: Ref<DriverDetailsSummary[]> = ref([]);

// Refs to template elements
const vehiclePreAllocation: Ref<any> = ref(null);
const driverPreAllocation: Ref<any> = ref(null);
const driverTextfieldId: Ref<string> = ref('');

const isValidatingVehicleSelection: Ref<boolean> = ref(false);
const vehicleInputErrorMessages: Ref<string[]> = ref([]);

const _showOutsideHireDialog: Ref<boolean> = ref(false);
const outsideHireForm: Ref<any> = ref(null);
const outsideHireNameTextfield: Ref<any> = ref(null);
const isAwaitingSaveNoteResponse: Ref<boolean> = ref(false);
const outsideHireDetails: Ref<OutsideHireDetails | null> = ref(null);

const startOfDay = returnStartOfDayFromEpoch();
const endOfDay = returnEndOfDayFromEpoch();

// let truckInputEl: HTMLInputElement | null = null;

const allocationOrder: ComputedRef<JobAllocationOrder> = computed(() => {
  return (
    useCompanyDetailsStore().divisionCustomConfig?.operations
      ?.jobAllocationOrder ?? JobAllocationOrder.VEHICLE_FIRST
  );
});

const inputOrder: ComputedRef<InputType[]> = computed(() => {
  return allocationOrder.value === JobAllocationOrder.VEHICLE_FIRST
    ? [InputType.VEHICLE, InputType.DRIVER]
    : [InputType.DRIVER, InputType.VEHICLE];
});

const selectedFleetAssetId: WritableComputedRef<string> = computed({
  get(): string {
    return props.fleetAssetId;
  },
  set(value: string): void {
    if (!value || value === '') {
      // If no vehicle is selected, reset the vehicle selection and clear errors
      resetVehicleSelections(false);
    } else {
      validateSelectedVehicle(value);
    }
  },
});

const selectedDriverId: WritableComputedRef<string> = computed({
  get(): string {
    const result = props.driverId;
    return result;
  },
  set(value: string): void {
    if (props.isFormDisabled) {
      return;
    }
    if (!value || value === '') {
      // If no driver is selected, reset the driver selection and clear errors
      resetDriverSelections(false);
    } else {
      validateSelectedDriver(value);
    }
  },
});

const outsideHireDialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return _showOutsideHireDialog.value;
  },
  set(value: boolean): void {
    _showOutsideHireDialog.value = value;
    if (!props.isExternalWindow) {
      useOperationsStore().setViewingOutsideHireDetailsDialog(value);
    }
    if (!value) {
      outsideHireDetails.value = null;
      clearInputs();
    }
  },
});

defineExpose({
  selectedFleetAssetId,
  selectedDriverId,
  validateSelectedVehicle,
  validateSelectedDriver,
  clearInputs,
});

/**
 * Resets the selected fleet asset and related error states. Also resets driver
 * selection if allocating by vehicle first.
 */
function resetVehicleSelections(clearLists: boolean): void {
  // selectedFleetAssetId.value = '';
  vehicleInputErrorMessages.value = [];
  primaryRateToApply.value = null;
  isValidatingVehicleSelection.value = false;

  if (props.fleetAssetId) {
    emit('update:fleetAssetId', '');
  }

  // Reset driver selection if we're allocating by vehicle first
  if (allocationOrder.value === JobAllocationOrder.VEHICLE_FIRST) {
    resetDriverSelections(true);
  } else {
    if (clearLists) {
      // If we're allocating by driver first, we need to clear the available fleet assets
      availableFleetAssets.value = [];
    }
  }
}

/**
 * Resets the selected driver and clears the available drivers list.
 */
function resetDriverSelections(clearLists: boolean): void {
  if (props.driverId) {
    emit('update:driverId', '');
  }

  // Reset vehicle selection if we're allocating by driver first
  if (allocationOrder.value === JobAllocationOrder.DRIVER_FIRST) {
    resetVehicleSelections(true);
  } else {
    if (clearLists) {
      // If we're allocating by vehicle first, we need to clear the available drivers
      availableDrivers.value = [];
    }
  }
}

/**
 * Handles the enter key event on the rate type selection input. If the key is
 * not the enter key, the function returns early. If the key is the enter key,
 * the function prevents the default action and sets the selected service type
 * id to the first service type id in the rate summary.
 */
function handleEnterEvent(e: KeyboardEvent) {
  if (e.key !== 'Enter') {
    return;
  }
  handleSubmitButtonFocus();
}

/**
 * This is called from the invisible input that is focused when the user tabs
 * away from the second autocomplete input in the template. If allocationOrder
 * is VEHICLE FIRST, it means the vehicle has already been selected and we then
 * need to validate the driver, and vice versa.
 * Also closes the menu on both autocompletes.
 */
function handleSubmitButtonFocus() {
  if (allocationOrder.value === JobAllocationOrder.VEHICLE_FIRST) {
    // Select the driver autocomplete component and close its menu if it's open
    const driverRef = Array.isArray(driverPreAllocation.value)
      ? driverPreAllocation.value[0]
      : driverPreAllocation.value;
    if (driverRef) {
      if (driverRef.isMenuActive !== undefined) {
        driverRef.isMenuActive = false;
      } else if (driverRef.$refs && driverRef.$refs.menu) {
        driverRef.$refs.menu.isActive = false;
      }
    }
    // If we're allocating by vehicle first, validate the selected driver
    validateSelectedDriver(selectedDriverId.value, true);
  } else {
    // Select the vehicle autocomplete component and close its menu if it's open
    const vehicleRef = Array.isArray(vehiclePreAllocation.value)
      ? vehiclePreAllocation.value[0]
      : vehiclePreAllocation.value;
    if (vehicleRef) {
      if (vehicleRef.isMenuActive !== undefined) {
        vehicleRef.isMenuActive = false;
      } else if (vehicleRef.$refs && vehicleRef.$refs.menu) {
        vehicleRef.$refs.menu.isActive = false;
      }
    }
    // If we're allocating by driver first, validate the selected vehicle
    validateSelectedVehicle(selectedFleetAssetId.value, true);
  }
}

/**
 * Validates the selected vehicle, fetches rates, and updates error or driver state accordingly.
 * @param fleetAssetId The ID of the selected fleet asset.
 */
async function validateSelectedVehicle(
  fleetAssetId: string,
  isBlurEvent: boolean = false,
): Promise<boolean> {
  if (!fleetAssetId || fleetAssetId === '') {
    // If no vehicle is selected, reset the vehicle selection and clear errors
    resetVehicleSelections(false);
    return false;
  }

  isValidatingVehicleSelection.value = true;

  // Get the summary object to confirm the vehicle exists
  const fleetAssetSummary =
    fleetAssetStore.getFleetAssetFromFleetAssetId(fleetAssetId);
  if (!fleetAssetSummary) {
    vehicleInputErrorMessages.value = [`Fleet asset not found.`];
    isValidatingVehicleSelection.value = false;
    return false;
  }

  // Check if there are any drivers for this vehicle
  const hasValidDrivers = returnAvailableDrivers({
    fleetAssetSummary: fleetAssetSummary,
    allocationOrder: allocationOrder.value,
  });
  if (hasValidDrivers.length === 0) {
    vehicleInputErrorMessages.value = [
      `No active drivers found for this vehicle.`,
    ];
    isValidatingVehicleSelection.value = false;
    return false;
  }

  // Set the date that we will use for rate requests
  const jobDate = props.searchDate ?? moment().valueOf();
  let applicableRate: JobPrimaryRate | string = '';

  // Check if primaryRateToApply already contains a rate, and it's for this
  // fleetAssetId and searchDate. If it is, use that. Otherwise, request it
  if (
    primaryRateToApply.value?.primaryRate &&
    primaryRateToApply.value.fleetAssetId === fleetAssetId &&
    primaryRateToApply.value.searchDate === jobDate
  ) {
    applicableRate = primaryRateToApply.value.primaryRate;
  } else {
    applicableRate = await returnJobPrimaryRateForAllocation({
      fleetAssetId: fleetAssetId,
      serviceTypeId: props.serviceTypeId,
      rateTypeId: props.rateTypeId,
      jobDate: jobDate,
      fleetAssetRates: props.fleetAssetRates ?? [],
      jobId: props.jobId!,
      isWindowMode: props.isExternalWindow,
    });
  }

  // If the applicable rate is a string, it means there was an error fetching
  // the rate
  if (typeof applicableRate === 'string') {
    onInvalidSelectedVehicle({
      errorMessage: applicableRate,
      allocationOrder: allocationOrder.value,
    });
    isValidatingVehicleSelection.value = false;
    return false;
  } else {
    primaryRateToApply.value = {
      fleetAssetId: fleetAssetId,
      searchDate: jobDate,
      primaryRate: applicableRate,
    };
    // jobPrimaryRate.value = applicableRate;
    onValidSelectedVehicle({
      fleetAssetSummary: fleetAssetSummary,
      allocationOrder: allocationOrder.value,
      isBlurEvent: isBlurEvent,
    });
    isValidatingVehicleSelection.value = false;
    return true;
  }
}

/**
 * Handles invalid vehicle selection by logging the error and updating error messages.
 * Also resets driver selection if allocating by vehicle first.
 * @param errorMessage The error message to display.
 * @param allocationOrder The current job allocation order.
 */
function onInvalidSelectedVehicle({
  errorMessage,
  allocationOrder,
}: {
  errorMessage: string;
  allocationOrder: JobAllocationOrder;
}) {
  vehicleInputErrorMessages.value = [
    errorMessage || 'Invalid vehicle selection.',
  ];

  if (allocationOrder === JobAllocationOrder.VEHICLE_FIRST) {
    // If we're allocating by vehicle first, we need to reset the state of the
    // driver selection (clear the available drivers which will disable the
    // input)
    resetDriverSelections(true);
  }
  // If we're syncing the values to the parent, then emit to the parent that the
  // selection is invalid
  if (props.onValidSelection === OnValidAllocationTarget.EMIT) {
    emit('submitPreallocation', null);
  } else {
    emit('update:fleetAssetId', '');
  }
}

/**
 * Handles valid vehicle selection by updating available drivers and focusing
 * the next input.
 * @param fleetAssetSummary The selected fleet asset summary.
 * @param allocationOrder The current job allocation order.
 */
function onValidSelectedVehicle({
  fleetAssetSummary,
  allocationOrder,
  isBlurEvent = false,
}: {
  fleetAssetSummary: FleetAssetSummary;
  allocationOrder: JobAllocationOrder;
  isBlurEvent?: boolean;
}) {
  emit('update:fleetAssetId', fleetAssetSummary.fleetAssetId);
  vehicleInputErrorMessages.value = [];
  if (allocationOrder === JobAllocationOrder.VEHICLE_FIRST) {
    // If we're allocating by vehicle first, then the next step is to set the
    // list of available drivers and focus the driver input
    availableDrivers.value = returnAvailableDrivers({
      fleetAssetSummary: fleetAssetSummary,
      allocationOrder: allocationOrder,
    });

    const defaultDriver = returnDefaultDriverSelection(
      fleetAssetSummary,
      availableDrivers.value,
    );

    emit('update:driverId', defaultDriver);

    // Focus the driver input after vehicle selection
    focusNextTabIndex();
  } else {
    // If we're allocating by driver first, we can send the allocation request
    // after the vehicle is selected.

    // We submit the request in two cases:
    // - if the blur event is what called this function, it means we should
    //   submit the preallocation
    // - if there's only one result in the available fleet assets and it matches
    //   the selected fleet asset.
    const isOnlyResult =
      availableFleetAssets.value.length === 1 &&
      fleetAssetSummary.fleetAssetId ===
        availableFleetAssets.value[0].fleetAssetId;

    // If either condition is met, we validate fields and submit
    if (isBlurEvent || isOnlyResult) {
      validateFieldsAndSubmit();
    }
  }
}

/**
 * Returns a list of available fleet assets to use in the vehicle autocomplete.
 * If we're allocating by driver first, it returns the associated fleet assets for the selected driver.
 * If we're allocating by vehicle first, it returns all active fleet assets.
 * @param driverSummary The summary of the selected driver.
 * @param allocationOrder The job allocation order (vehicle first or driver first).
 * @returns An array of available fleet asset summaries.
 */
function returnAvailableFleetAssets({
  driverSummary,
  allocationOrder,
}: {
  driverSummary?: DriverDetailsSummary;
  allocationOrder: JobAllocationOrder;
}): FleetAssetOption[] {
  let fleetAssets: FleetAssetSummary[] = [];
  if (allocationOrder === JobAllocationOrder.DRIVER_FIRST) {
    if (!driverSummary) {
      return [];
    }
    fleetAssets = fleetAssetStore.getAllFleetAssetList
      .filter(
        (fa) =>
          fa.isActiveForAllocation &&
          fa.associatedDrivers.includes(driverSummary.driverId),
      )
      .filter((fa) => fa !== undefined) as FleetAssetSummary[];
  } else {
    // VEHICLE_FIRST: return all active fleet assets
    fleetAssets = fleetAssetStore.getAllFleetAssetList.filter(
      (fa: FleetAssetSummary) => fa.isActiveForAllocation,
    );
  }

  // Map the fleet assets to FleetAssetOption format with display names
  // including truckClass and rego
  const fleetAssetsWithDisplayName: FleetAssetOption[] = fleetAssets
    .filter((fa) => allowOutsideHire.value || !fa.outsideHire)
    .map((fa: FleetAssetSummary) => ({
      fleetAssetId: fa.fleetAssetId,
      defaultDriver: fa.defaultDriver,
      displayName: [
        fa.csrAssignedId,
        fa.truckClass ?? '',
        fa.registrationNumber,
      ]
        .filter((s) => !!s)
        .join(' - '),
    }));

  return fleetAssetsWithDisplayName;
}

/**
 * Returns a list of available drivers to use in the template in the driver
 * autocomplete. If we're allocating by vehicle first, it returns the associated
 * drivers for the selected fleet asset. If we're allocating by driver first, it
 * returns all active drivers.
 * @param fleetAssetSummary The summary of the selected fleet asset.
 * @param allocationOrder The job allocation order (vehicle first or driver
 * first).
 * @returns An array of available driver summaries.
 */
function returnAvailableDrivers({
  fleetAssetSummary,
  allocationOrder,
}: {
  fleetAssetSummary?: FleetAssetSummary;
  allocationOrder: JobAllocationOrder;
}): DriverDetailsSummary[] {
  if (allocationOrder === JobAllocationOrder.VEHICLE_FIRST) {
    if (!fleetAssetSummary) {
      return [];
    }
    const availableDrivers: DriverDetailsSummary[] =
      fleetAssetSummary.associatedDrivers
        .map((fid) => driverDetailsStore.getDriverFromDriverId(fid))
        .filter((d) => d && d.isActive)
        .filter((d) => d !== undefined) as DriverDetailsSummary[];
    // Find the associated drivers for the selected fleet asset
    if (!fleetAssetSummary.outsideHire) {
      return availableDrivers;
    } else {
      const placeholder = new DriverDetailsSummary();
      if (availableDrivers.length > 0) {
        const first = availableDrivers[0];
        placeholder.driverId = first.driverId;
        placeholder.name = 'OUTSIDE HIRE - Details Required';
        placeholder.displayName = 'OUTSIDE HIRE - Details Required';
        return [placeholder];
      } else {
        return [];
      }
    }
  } else {
    // Allocating by driver first - return all active drivers
    return driverDetailsStore.getDriverList.filter(
      (d: DriverDetailsSummary) => d.isActive,
    );
  }
}

/**
 * Called when the user selects a driver from the autocomplete, or via
 * defineExpose from a parent.
 *
 * Validates the selected driverId and updates the state accordingly.
 *
 * @param driverId The ID of the selected driver.
 * @param isBlurEvent Whether the validation was triggered by a blur event.
 */
function validateSelectedDriver(
  driverId: string | null,
  isBlurEvent: boolean = false,
): boolean {
  if (!driverId || driverId === '') {
    // If no driver is selected, reset the driver selection and clear errors
    resetDriverSelections(false);
    return false;
  }

  const driverSummary: DriverDetailsSummary | undefined =
    driverDetailsStore.getDriverFromDriverId(driverId);

  if (!driverSummary || !driverSummary.isActive) {
    return false;
  }
  onValidSelectedDriver({
    driverSummary,
    allocationOrder: allocationOrder.value,
    isBlurEvent,
  });

  return true;
}

/**
 * Called by validateSelectedDriver when a selection is deemed valid.
 *
 * Updates the state with the selected driver. Performs different logic
 * depending on the allocation order.
 *
 * If allocation order is vehicle first, then it the driver selection is the
 * last input so we should submit the allocation
 */
function onValidSelectedDriver({
  driverSummary,
  allocationOrder,
  isBlurEvent = false,
}: {
  driverSummary: DriverDetailsSummary;
  allocationOrder: JobAllocationOrder;
  isBlurEvent?: boolean;
}) {
  emit('update:driverId', driverSummary.driverId);

  // If we're allocating by vehicle first and the driver is valid, then it means
  // it's time to submit the preallocation
  if (allocationOrder === JobAllocationOrder.VEHICLE_FIRST) {
    // If we're allocating by vehicle first, we can send the allocation request
    // after the driver is selected.

    // We submit the request in two cases:
    // - if the blur event is what called this function, it means we should
    //   submit the preallocation
    // - if there's only one result in the available drivers and it matches
    //   the selected driver.
    const isOnlyResult =
      availableDrivers.value.length === 1 &&
      driverSummary.driverId === availableDrivers.value[0].driverId;

    // If either condition is met, we validate fields and submit
    if (isBlurEvent || isOnlyResult) {
      validateFieldsAndSubmit();
    }
  } else if (allocationOrder === JobAllocationOrder.DRIVER_FIRST) {
    // If we're allocating by vehicle first, then the next step is to set the
    // list of available drivers and focus the driver input
    availableFleetAssets.value = returnAvailableFleetAssets({
      driverSummary: driverSummary,
      allocationOrder: allocationOrder,
    });

    const defaultVehicle = returnDefaultVehicleSelection(
      driverSummary,
      availableFleetAssets.value,
    );
    emit('update:fleetAssetId', defaultVehicle);

    // Focus the vehicle input after driver selection
    focusNextTabIndex();
  }
}
/**
 * Returns the default driver selection for a given fleet asset and its associated drivers.
 * If a default driver is set and exists in the list of available drivers, it returns that driver.
 * @param fleetAssetSummary The summary of the selected fleet asset.
 * @param driverList The list of available drivers.
 * @returns The driverId of the default driver, or the first available driver, or null.
 */
function returnDefaultDriverSelection(
  fleetAssetSummary: FleetAssetSummary,
  driverList: DriverDetailsSummary[],
): string {
  const driverIds = driverList.map((d) => d.driverId).filter((id) => !!id);
  if (driverIds.length === 0) {
    return '';
  }
  if (
    !!fleetAssetSummary.defaultDriver &&
    driverIds.includes(fleetAssetSummary.defaultDriver)
  ) {
    return fleetAssetSummary.defaultDriver;
  } else {
    // If no default driver, return the first available driver
    return driverIds[0];
  }
}

/**
 * Returns the default vehicle selection for a given driver and list of fleet assets.
 *
 * This function determines which vehicle (fleet asset) should be pre-selected for a driver
 * in the allocation UI. It uses the same prioritization logic as `getActiveVehicleForDriver`:
 *
 * 1. Vehicle assigned to an in-progress job for the driver today.
 * 2. Vehicle assigned to an upcoming (not completed or cancelled) job for the driver today.
 * 3. Vehicle assigned to any job for the driver tomorrow.
 * 4. Vehicle assigned to any job for the driver yesterday.
 * 5. Vehicle where the driver is set as the default driver in the fleet asset summary.
 * 6. The first available vehicle as a fallback.
 *
 * @param driverSummary - The summary object for the driver.
 * @param fleetAssetList - The list of available fleet assets for selection.
 * @returns The `fleetAssetId` of the default vehicle to select, or an empty string if none found.
 */
function returnDefaultVehicleSelection(
  driverSummary: DriverDetailsSummary,
  fleetAssetList: FleetAssetOption[],
): string {
  const fleetAssetIds = fleetAssetList.map((f) => f.fleetAssetId);
  const entityOptions = fleetAssetList.map(
    (fa: FleetAssetOption): FleetAssetEntityOption => ({
      entityId: fa.fleetAssetId,
      displayName: fa.displayName,
    }),
  );

  const jobStore = useJobStore();

  // Get all in-progress jobs and allocated work for the drier
  const inProgressJobs = jobStore.operationJobsList.filter(
    (job) =>
      job.workStatus === WorkStatus.IN_PROGRESS &&
      job.driverId === driverSummary.driverId &&
      fleetAssetIds.includes(job.fleetAssetId),
  );
  const allocatedWork = jobStore.operationJobsList.filter(
    (job) =>
      job.workStatus >= WorkStatus.PREALLOCATED &&
      job.workStatus <= WorkStatus.ACCEPTED &&
      job.driverId === driverSummary.driverId &&
      fleetAssetIds.includes(job.fleetAssetId) &&
      job.date &&
      job.date >= startOfDay &&
      job.date < endOfDay,
  );

  // Use the same logic as the
  const activeDriver = getActiveVehicleForDriver<
    FleetAssetEntityOption,
    FleetAssetOption
  >({
    driverId: driverSummary.driverId,
    inProgressJobs,
    allocatedWork,
    associatedFleetAssets: entityOptions,
    fleetAssetSummaries: fleetAssetList,
  });

  if (activeDriver) {
    // If the driver has an active vehicle, return that
    return activeDriver.entityId;
  } else if (fleetAssetList.length > 0) {
    // Otherwise, return the first available vehicle
    return fleetAssetList[0].fleetAssetId;
  }
  return '';
}

const isDriverInputDisabled: ComputedRef<boolean> = computed(() => {
  if (props.isFormDisabled) {
    return true;
  }
  if (allocationOrder.value === JobAllocationOrder.VEHICLE_FIRST) {
    return !availableDrivers.value.length;
  } else {
    return false; // Always disabled if allocating by driver first
  }
});

const isFleetAssetInputDisabled: ComputedRef<boolean> = computed(() => {
  if (props.isFormDisabled) {
    return true;
  }
  if (allocationOrder.value === JobAllocationOrder.DRIVER_FIRST) {
    return !availableFleetAssets.value.length;
  } else {
    return false; // Always disabled if allocating by vehicle first
  }
});

/**
 * Only allow allocation to an outside hire if we're not allocating a permanent
 * job, as outside hire aren't meant to used as regular vehicles.
 */
const allowOutsideHire: ComputedRef<boolean> = computed(() => {
  return props.type !== ObjectToAllocate.PERMANENT_JOB;
});

const selectedVehicleIsOutsideHire: ComputedRef<boolean> = computed(() => {
  if (!selectedFleetAssetId.value) {
    return false;
  }
  const fleetAssetSummary = fleetAssetStore.getFleetAssetFromFleetAssetId(
    selectedFleetAssetId.value,
  );
  return fleetAssetSummary?.outsideHire ?? false;
});

/**
 * Focuses the next tabbable input in the DOM, after blurring the current
 * vehicle input. Also programmatically opens the menu on the next input
 * so Enter works after tabbing in.
 */
function focusNextTabIndex(): void {
  nextTick(() => {
    if (allocationOrder.value === JobAllocationOrder.VEHICLE_FIRST) {
      // Blur vehicle autocomplete if it's focused
      const vehicleRef = Array.isArray(vehiclePreAllocation.value)
        ? vehiclePreAllocation.value[0]
        : vehiclePreAllocation.value;
      vehicleRef.blur();
      const vehicleInput = vehicleRef.$el?.querySelector('input');
      vehicleInput?.blur();
      // Focus the driver input and open its menu
      const driverRef = Array.isArray(driverPreAllocation.value)
        ? driverPreAllocation.value[0]
        : driverPreAllocation.value;
      driverRef.focus();

      // Open the menu programmatically
      if (driverRef.isMenuActive !== undefined) {
        driverRef.isMenuActive = true;
      } else if (driverRef.$refs && driverRef.$refs.menu) {
        driverRef.$refs.menu.isActive = true;
      }
    } else {
      // If we're allocating by driver first, blur the driver input
      const driverRef = Array.isArray(driverPreAllocation.value)
        ? driverPreAllocation.value[0]
        : driverPreAllocation.value;
      driverRef.blur();
      const driverInput = driverRef.$el?.querySelector('input');
      driverInput?.blur();

      // Focus the vehicle input and open its menu
      const vehicleRef = Array.isArray(vehiclePreAllocation.value)
        ? vehiclePreAllocation.value[0]
        : vehiclePreAllocation.value;
      vehicleRef.focus();

      // Open the menu programmatically
      if (vehicleRef.isMenuActive !== undefined) {
        vehicleRef.isMenuActive = true;
      } else if (vehicleRef.$refs && vehicleRef.$refs.menu) {
        vehicleRef.$refs.menu.isActive = true;
      }
    }
  });
}

/**
 * Clears both vehicle and driver selections and resets error state.
 */
function clearInputs(): void {
  resetVehicleSelections(true);
  resetDriverSelections(true);

  // Emit to parent that the selections have been cleared
  emit('submitPreallocation', null);
}

/**
 * Validates if the client is eligible for allocation based on their credit status.
 * If the client has a 'See Accounts' status, it shows a dialog notification and returns false.
 * @param clientId The ID of the client to validate.
 * @returns True if the client is valid for allocation, false otherwise.
 */
function isClientValidForAllocation(clientId: string | undefined): boolean {
  try {
    if (!clientId) {
      return false;
    }

    // If clientId is CS (cash sale) then we allow allocation
    if (clientId === 'CS') {
      return true;
    }

    const clientSummaryDetails: ClientSearchSummary | undefined =
      useClientDetailsStore().clientSummaryList.find(
        (client: any) => client.clientId === clientId,
      );
    if (!clientSummaryDetails) {
      throw new Error(
        `Client with ID ${clientId} not found in client summary list.`,
      );
    }
    const clientHasSeeAccountsStatus =
      clientSummaryDetails &&
      clientSummaryDetails.creditStatus === 2 &&
      !clientSummaryDetails.statusList.includes(3);

    if (clientHasSeeAccountsStatus) {
      const dialogNotificationMessage =
        'Allocation unavailable for ' +
        (clientSummaryDetails
          ? clientSummaryDetails.tradingName
            ? clientSummaryDetails.tradingName
            : clientSummaryDetails.clientName
          : '') +
        '. Please see Accounts.';
      useRootStore().setDialogNotification([dialogNotificationMessage]);
      return false;
    }
    return true;
  } catch (error) {
    logConsoleError(`Error validating client for allocation`, error);
    return false;
  }
}

/**
 * Called when the second input has a valid selected from it (ie when the
 * vehicle is selected in DRIVER_FIRST allocation, or the driver is selected in
 * VEHICLE_FIRST allocation). Opens the outside hire dialog if the selected
 * vehicle is an outside hire.
 *
 * If the vehicle is not an outside hire, it calls sendPreallocation to submit
 * the allocation.
 */
async function validateFieldsAndSubmit() {
  if (!isClientValidForAllocation(props.clientId)) {
    return;
  }
  // If the fleet asset is an outside hire, we need to show the dialog to
  // collect driver details
  if (selectedVehicleIsOutsideHire.value) {
    outsideHireDetails.value = {
      driverName: '',
      contactNumber: '',
      registrationNumber: '',
    };
    outsideHireDialogController.value = true;
    // Focus `outsideHireNameTextfield` on nextTick
    setTimeout(() => {
      outsideHireNameTextfield.value?.focus();
    }, 100);

    return;
  }
  sendPreallocation();
}

/**
 * Handles the preallocation process for a driver and fleet asset.
 *
 * This function validates the selected primary rate, search date, and fleet
 * asset ID. Depending on the `onValidSelection` prop, it either emits events to
 * the parent component or sends a preallocation request to the backend. If the
 * request is successful, it emits update events; otherwise, it shows a
 * notification and clears the inputs. In case of validation or request errors,
 * it emits a `submitPreallocation` event with `null`.
 *
 * @throws {Error} If the primary rate is null or if the search date or fleet
 * asset ID do not match the selected values.
 */
async function sendPreallocation() {
  try {
    // Validate that primary rate is not null
    if (!primaryRateToApply.value?.primaryRate) {
      throw new Error(
        'Job primary rate is null. Cannot send preallocation request.',
      );
    }

    // Validate that the searchDate and selectedFleetAssetId match the primaryRateToApply
    if (
      primaryRateToApply.value.searchDate !== props.searchDate ||
      primaryRateToApply.value.fleetAssetId !== selectedFleetAssetId.value
    ) {
      throw new Error(
        'Primary rate search date or fleet asset ID does not match the selected values.',
      );
    }

    // If we're syncing the values to the parent, then emit to the parent that the
    // selection is invalid
    if (props.onValidSelection === OnValidAllocationTarget.EMIT) {
      emit('update:fleetAssetId', selectedFleetAssetId.value);
      emit('update:driverId', selectedDriverId.value);
      emit('submitPreallocation', {
        fleetAssetId: selectedFleetAssetId.value,
        driverId: selectedDriverId.value,
        fleetAssetRate: [primaryRateToApply.value.primaryRate],
      });
      return;
    }

    // If we're not syncing the values to the parent (onValidSelection is
    // OnValidAllocationTarget.SEND), we need to send the preallocation request
    const isSuccess = await sendPreallocationRequest({
      jobId: props.jobId,
      fleetAssetId: selectedFleetAssetId.value,
      driverId: selectedDriverId.value ?? '',
      windowMode: props.isExternalWindow ?? false,
      fleetAssetRates: [primaryRateToApply.value.primaryRate],
    });
    // If successful, emit the update events
    if (isSuccess) {
      emit('update:fleetAssetId', selectedFleetAssetId.value);
      emit('update:driverId', selectedDriverId.value);
    } else {
      showNotification('Something went wrong. Please try again.', {
        title: 'Preallocation Failed',
      });
      clearInputs();
    }
  } catch (error) {
    emit('submitPreallocation', null);
  }
}

function setFormDisabledState(fleetAssetId: string): void {
  try {
    const driverSummary = driverDetailsStore.getDriverFromDriverId(
      selectedDriverId.value,
    );
    if (!driverSummary) {
      throw new Error(
        `Driver with ID ${selectedDriverId.value} not found in store.`,
      );
    }

    const fleetAssetSummary =
      fleetAssetStore.getFleetAssetFromFleetAssetId(fleetAssetId);
    if (!fleetAssetSummary) {
      throw new Error(
        `Fleet asset with ID ${fleetAssetId} not found in store.`,
      );
    }
    // // TODO: Is this check necessary? Do we even need the rates if the component is disabled?
    // if (!props.fleetAssetRates?.length) {
    //   throw new Error(
    //     `No fleet asset rates provided for fleet asset ${fleetAssetId}.`,
    //   );
    // }

    if (allocationOrder.value === JobAllocationOrder.VEHICLE_FIRST) {
      // If we're allocating by vehicle first, need to set the list of available
      // drivers so that the input can display the currently selected driver
      availableDrivers.value = returnAvailableDrivers({
        fleetAssetSummary: fleetAssetSummary,
        allocationOrder: allocationOrder.value,
      });
    } else {
      // If we're allocating by driver first, we need to set the list of
      // available fleet assets so that the input can display the currently
      // selected fleet asset
      availableFleetAssets.value = returnAvailableFleetAssets({
        driverSummary: driverSummary,
        allocationOrder: allocationOrder.value,
      });
    }
  } catch (error) {
    logConsoleError(`Error initializing AllocateDriver component: ${error}`);
  }
}

/**
 * Constructs the outside hire note and sends it to the server.
 * Validates the form and shows a notification if validation fails.
 * If the jobId is not provided, logs an error to the console.
 */
async function sendOutsideHireNote() {
  if (!outsideHireDetails.value || !outsideHireForm.value?.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  // Throw error if jobId is not provided as a prop. This shouldn't be possible.
  if (!props.jobId) {
    console.error('Job ID is required to add an outside hire note.');
    return;
  }
  isAwaitingSaveNoteResponse.value = true;
  const isSuccess = await addOutsideHireNoteToJob(
    outsideHireDetails.value,
    props.isExternalWindow,
    props.jobId,
  );

  // Do nothing if request failed
  if (!isSuccess) {
    isAwaitingSaveNoteResponse.value = false;
    return;
  }

  // If successful, send the preallocation request and send the
  await sendPreallocation();
  isAwaitingSaveNoteResponse.value = false;
  outsideHireDialogController.value = false;
}

onBeforeMount(() => {
  if (allocationOrder.value === JobAllocationOrder.VEHICLE_FIRST) {
    // If we're allocating by vehicle first, we set the full list of available fleet assets
    availableFleetAssets.value = returnAvailableFleetAssets({
      allocationOrder: allocationOrder.value,
    });
  } else {
    // If we're allocating by driver first, we set the available drivers
    availableDrivers.value = returnAvailableDrivers({
      allocationOrder: allocationOrder.value,
    });
  }
  if (
    props.isFormDisabled &&
    (selectedDriverId.value || selectedFleetAssetId.value)
  ) {
    setFormDisabledState(selectedFleetAssetId.value);
  }
});
</script>
<style scoped lang="scss">
.allocate-driver {
  // background-color: blue;
  .input-container {
    padding: 0px 4px;

    &.input-container--compact {
      min-width: 260px;
      max-width: 260px;
    }
  }

  &.error-state {
    padding-bottom: 34px;
  }

  .submit-invisible-input {
    opacity: 0;
    width: 1px;
    height: 1px;
    position: absolute;
    pointer-events: none;
  }
}
</style>
