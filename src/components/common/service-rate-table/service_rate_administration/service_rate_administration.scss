.fleet-asset-service-rate-index {
  $cell-width: 84px;
  $cell-height: 50px;
  $cell-padding: 11px;

  $icon-size: 18px;

  $swatch-dark: $app-dark-primary-200;

  $swatch-light: $app-dark-primary-400;

  $border-color: #787889;

  .headings-container {
    display: flex;
    flex-shrink: 0;
    justify-content: flex-start;
    border-bottom: 2px solid $border-color;
    .heading {
      flex-shrink: 0;
      color: white;
      &:nth-child(1) {
        width: 177px;
      }
      &:nth-child(2) {
        .rate-value {
          &:nth-child(1) {
            border-right: 1px solid $swatch-dark;
          }
          &:nth-child(2) {
            border-left: 1px solid $swatch-dark;
          }
        }
      }

      .rate-name {
        border-left: 1px solid hsla(0, 77%, 40%, 0.12);
        border-right: 1px solid hsla(0, 0%, 100%, 0.12);
        border-bottom: 1px solid hsla(0, 0%, 100%, 0.12);
        border-top: 1px solid hsla(0, 0%, 100%, 0.12);
        padding: 10px 0;
        background-color: $swatch-light;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .rate-values-container {
      display: flex;
      justify-content: flex-start;

      .rate-value {
        width: $cell-width;
        text-align: center;
        border-left: 1px solid $swatch-dark;
        border-right: 1px solid $swatch-dark;
        padding: 10px 5px;
        color: white;
        font-weight: 600;

        &:nth-child(1) {
          background-color: #0277bd;
        }

        &:nth-child(2) {
          background-color: #0188ff;
          border-left: 1px solid $swatch-dark;
          border-right: 1px solid $swatch-dark;
        }

        &:nth-child(3) {
          background-color: #d32f2f;
          border-left: 1px solid $swatch-dark;
        }
      }
    }
  }

  .values-container {
    display: flex;
    justify-content: flex-start;

    .service-name {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      width: 177px;
      height: $cell-height;
      flex-shrink: 0;
      // background-color: #303030;
      // background-color: $swatch-light;
      font-weight: 600;
      color: white;
      // border: 1px solid hsla(0, 0%, 100%, 0.12);
      border: 1px solid hsla(0, 0%, 100%, 0.12);
    }
  }

  .rates {
    width: 100%;
    height: $cell-height;

    .radios {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .radio-btn {
        border: 0px;
        width: $cell-width;
        height: 100%;
        cursor: pointer;
      }

      .radio-btn:before {
        // content: "\f05e";
        content: ' ';
        background-color: var(--table-bg-100);
        // color: $swatch-light;
        // font-family: "Font Awesome 5 Pro";
        // font-size: $icon-size;
        pointer-events: none;
        z-index: 3;
        position: absolute;
        top: 0px;
        text-align: center;
        left: 0px;
        width: $cell-width;
        height: $cell-height;
        padding: $cell-padding 0 0 0;
        border: 1px solid hsla(0, 0%, 100%, 0.12);
      }

      .radio-btn:checked:before {
        content: '\f00c';
        font-family: $font-awesome-family;
        color: #00c853;
        font-size: $icon-size;
        pointer-events: none;
        height: $cell-height;
        width: $cell-width;
        z-index: 3;
        text-align: center;
        position: absolute;
        top: 0px;
        left: 0px;
        padding: $cell-padding 0 0 0;
        border: 1px solid hsla(0, 0%, 100%, 0.12);
        border-left: 1px solid hsla(0, 0%, 100%, 0.12);
      }

      // Default color
      .radio-btn-0:checked:before {
        background-color: #0277bd;
        border-left: 1px solid $swatch-dark;
      }

      // Client color
      .radio-btn-1:checked:before {
        background-color: #7b1fa2;
      }

      // N/A color
      .radio-btn-2:checked:before {
        background-color: #d32f2f;
        border-right: 1px solid $swatch-dark;
      }

      .radio-btn-2::before {
        border-right: 1px solid $swatch-dark;
      }

      .radio-btn-1::before {
        border-left: 1px solid $swatch-dark;
      }

      .unselectableRates:before {
        content: '\f05e';
        background-color: $swatch-light;
        border: 1px solid hsla(0, 0%, 100%, 0.12);
        color: grey;
        font-family: $font-awesome-family;
        font-size: $icon-size;
        pointer-events: none;
        border-left: 1px solid $swatch-dark;
      }

      .unselectableRates:checked:before {
        content: '\f05e';
        background-color: $swatch-light;
        border: 1px solid hsla(0, 0%, 100%, 0.12);

        color: grey;
        font-family: $font-awesome-family;
        font-size: $icon-size;
        pointer-events: none;
      }

      .radio-btn-2.unselectableRates:checked:before {
        border-right: 1px solid $swatch-dark;
      }
    }

    &:nth-child(2) {
      .radio-btn-1::before {
        border-left: 1px solid $swatch-dark;
      }
    }

    &:nth-child(4) {
      .radio-btn-1::before {
        border-left: 1px solid $swatch-dark;
      }
    }

    &:nth-child(1) {
      .radio-btn-1::before {
        border-left: 1px solid $swatch-dark;
      }

      .radio-btn-2.unselectableRates:checked:before {
        border-right: 1px solid $swatch-dark;
      }
    }
  }

  // Hide Default rates where no default rate exists
  .disableWidth {
    display: none;
  }

  .disabled {
    pointer-events: none;

    * {
      color: #757575 !important;
    }
  }

  .tableInfo {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $swatch-light;
    border: 1px solid hsla(0, 0%, 100%, 0.12);
  }

  .top-panel {
    position: fixed;
    transition: 0.2s;
    background-color: $swatch-light;
    top: 39px;
    left: calc(25% + 37px);
    width: calc(75% - 37px);
    z-index: 199;
  }

  .table-container {
    border: 2px solid $border-color;
  }
}
