<section
  class="service-rate-table-time"
  :class="[{adminPage: adminPage }, {formDisabled: !isEdited}, {fleetAssetPage: isFleetAsset}, {clientPage: isClient}, {time: serviceRate.rateTableItems.length > 0}]"
>
  <div
    class="table-headers"
    :class="fixedTopPanel ? 'table-headers-fixed' : ''"
    v-if="serviceRate.rateTableItems.length > 0"
  >
    <div
      fill-height
      class="thick-border-right thick-border-left small-width"
      style="width: 7% !important"
    >
      <v-layout fill-height align-center justify-center>
        <span class="">Service</span>
      </v-layout>
    </div>
    <div class="thick-border-right big-width">
      <v-layout justify-center align-center class="rate-heading">
        <span class="rate-title">Rate</span>
      </v-layout>

      <v-layout align-center class="rate-sections">
        <v-flex fill-height class="rate-name" md5>
          <v-layout fill-height align-center> $ </v-layout>
        </v-flex>
        <v-flex md7 fill-height>
          <v-layout align-center fill-height class="border-left">
            <span class="rate-name">Multiplier</span>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
    <div class="thick-border-right big-width">
      <v-layout justify-center align-center class="rate-heading">
        <span class="rate-title">Min Charge</span>
      </v-layout>
      <v-layout align-center class="rate-sections">
        <v-flex fill-height class="rate-name border-right" md5>
          <v-layout fill-height align-center> # </v-layout>
        </v-flex>
        <v-flex md7>
          <span class="rate-name">Multiplier</span>
        </v-flex>
      </v-layout>
    </div>
    <div class="thick-border-right big-width">
      <v-layout justify-center align-center class="rate-heading">
        <span class="rate-title">Charge Increment</span>
      </v-layout>
      <v-layout align-center class="rate-sections">
        <v-flex fill-height class="rate-name border-right" md5>
          <v-layout fill-height align-center> # </v-layout>
        </v-flex>
        <v-flex md7>
          <span class="rate-name">Multiplier</span>
        </v-flex>
      </v-layout>
    </div>
    <div fill-height class="thick-border-right" style="width: 7% !important">
      <v-layout fill-height align-center justify-center>
        <span class="">Grace</span>
      </v-layout>
    </div>
    <div class="thick-border-right big-width">
      <v-layout justify-center align-center class="rate-heading">
        <span class="rate-title">Standby Rate</span>
      </v-layout>
      <v-layout align-center class="rate-sections">
        <v-flex fill-height class="rate-name" md5>
          <v-layout fill-height align-center> $ </v-layout>
        </v-flex>
        <v-flex md7 fill-height>
          <v-layout align-center fill-height class="border-left">
            <span class="rate-name">Multiplier</span>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
    <div class="thick-border-right small-width" style="width: 8% !important">
      <v-layout fill-height align-center justify-center>
        <span class="">First Leg</span>
      </v-layout>
    </div>
    <div class="thick-border-right small-width" style="width: 8% !important">
      <v-layout fill-height align-center justify-center>
        <span class="">Last Leg</span>
      </v-layout>
    </div>
    <div fill-height class="thick-border-right" style="width: 13% !important">
      <v-layout fill-height align-center justify-center>
        <span class="rate-name pl-0">Fuel Surcharge</span>
      </v-layout>
    </div>
    <div fill-height class="thick-border-right" style="width: 7% !important">
      <v-layout fill-height align-center justify-center>
        <span class="rate-name pl-0" style="text-align: center"
          >Fuel On Standby</span
        >
      </v-layout>
    </div>
    <div
      fill-height
      class="small-width thick-border-right"
      style="width: 5% !important"
    >
      <v-layout fill-height align-center justify-center>
        <span class="rate-name pl-0">Copy</span>
      </v-layout>
    </div>
  </div>
  <div class="services-table">
    <v-layout v-for="(rateItem, index) in timeRateItems" :key="index">
      <div
        class="form-container"
        @click="setSelectedEdit(index)"
        style="cursor: pointer"
      >
        <div
          fill-height
          class="thick-border-right thick-border-left"
          @click="setSelectedEdit(index)"
          style="cursor: pointer; width: 7% !important"
        >
          <v-layout fill-height align-center justify-center>
            <span class="service-name">{{
              returnServiceName(rateItem.serviceTypeId)
            }}</span>
          </v-layout>
        </div>
        <div class="thick-border-right big-width">
          <v-layout>
            <v-flex md5>
              <v-text-field
                v-if="selectedEdit === index"
                class="small-input"
                box
                type="number"
                v-model.number="rateItem.rateTypeObject.rate"
                hide-details
                :rules="[validate.required, validate.number]"
                @focus="$event.target.select()"
              >
              </v-text-field>
              <v-layout
                v-else
                align-center
                fill-height
                class="row-height pl-2"
                :class="{'error--text' : validationError(rateItem.rateTypeObject.rate, [validate.number, validate.required])}"
              >
                <span class="subheading grey--text">
                  {{ displayCurrencyValue(rateItem.rateTypeObject.rate) }}
                </span>
              </v-layout>
            </v-flex>
            <v-flex md7 class="border-left">
              <v-select
                v-if="selectedEdit === index"
                class="multi-box"
                box
                :items="rateMultipliers"
                item-text="longName"
                item-value="id"
                v-model="rateItem.rateTypeObject.rateMultiplier"
                hide-details
                :rules="[validate.required, validate.number]"
              ></v-select>
              <v-layout
                v-else
                align-center
                class="row-height pl-2"
                :class="{'error--text' : validationError(rateItem.rateTypeObject.rateMultiplier, [validate.number, validate.required])}"
              >
                <span class="subheading grey--text">
                  {{
                    returnMultiplierValue(
                      rateItem.rateTypeObject.rateMultiplier
                    )
                  }}
                </span>
              </v-layout>
            </v-flex>
          </v-layout>
        </div>
        <div class="thick-border-right big-width">
          <v-layout>
            <v-flex md5 class="border-right">
              <v-text-field
                v-if="selectedEdit === index"
                box
                class="small-input"
                v-model.number="rateItem.rateTypeObject.minCharge"
                hide-details
                :rules="[validate.required, validate.number]"
                @focus="$event.target.select()"
              >
              </v-text-field>
              <v-layout
                v-else
                align-center
                class="row-height pl-2"
                :class="{'error--text' : validationError(rateItem.rateTypeObject.minCharge, [validate.number, validate.required])}"
              >
                <span class="subheading grey--text">
                  {{ rateItem.rateTypeObject.minCharge }}
                </span>
              </v-layout>
            </v-flex>
            <v-flex md7>
              <v-select
                box
                class="multi-box"
                v-if="selectedEdit === index"
                :items="rateMultipliers"
                item-text="longName"
                item-value="id"
                v-model="rateItem.rateTypeObject.minChargeMultiplier"
                hide-details
                :rules="[validate.required, validate.number]"
              ></v-select>
              <v-layout
                v-else
                align-center
                class="row-height pl-2"
                :class="{'error--text' : validationError(rateItem.rateTypeObject.minChargeMultiplier, [validate.number, validate.required])}"
              >
                <span class="subheading grey--text">
                  {{
                    returnMultiplierValue(
                      rateItem.rateTypeObject.minChargeMultiplier
                    )
                  }}
                </span>
              </v-layout>
            </v-flex>
          </v-layout>
        </div>
        <div class="thick-border-right big-width">
          <v-layout>
            <v-flex md5 class="border-right">
              <v-text-field
                v-if="selectedEdit === index"
                box
                type="number"
                class="small-input"
                v-model.number="rateItem.rateTypeObject.chargeIncrement"
                hide-details
                :rules="[validate.required, validate.number]"
                @focus="$event.target.select()"
              >
              </v-text-field>
              <v-layout
                v-else
                fill-height
                align-center
                class="pl-2 row-height"
                :class="{'error--text' : validationError(rateItem.rateTypeObject.chargeIncrement, [validate.number, validate.required])}"
              >
                <span class="subheading grey--text">
                  {{ rateItem.rateTypeObject.chargeIncrement }}</span
                >
              </v-layout>
            </v-flex>
            <v-flex md7 class="border-right">
              <v-select
                v-if="selectedEdit === index"
                box
                class="multi-box"
                :items="rateMultipliers"
                item-text="longName"
                item-value="id"
                v-model="rateItem.rateTypeObject.chargeIncrementMultiplier"
                hide-details
                :rules="[validate.required, validate.number]"
              ></v-select>
              <v-layout
                v-else
                fill-height
                align-center
                class="pl-2 row-height multi-box"
                :class="{'error--text' : validationError(rateItem.rateTypeObject.chargeIncrementMultiplier, [validate.number, validate.required])}"
              >
                <span class="subheading grey--text">
                  {{
                    returnMultiplierValue(
                      rateItem.rateTypeObject.chargeIncrementMultiplier
                    )
                  }}
                </span>
              </v-layout>
            </v-flex>
          </v-layout>
        </div>
        <div
          class="thick-border-right"
          @click="setSelectedEdit(index)"
          style="cursor: pointer; width: 7% !important"
        >
          <v-select
            v-if="selectedEdit === index"
            box
            :items="graceTypes"
            class="grace-type-box"
            item-text="shortName"
            item-value="id"
            v-model="rateItem.rateTypeObject.graceType"
            hide-details
            :rules="[validate.required, validate.number]"
          ></v-select>
          <v-layout
            v-else
            fill-height
            align-center
            class="pl-2 row-height multi-box"
            :class="{'error--text' : validationError(rateItem.rateTypeObject.graceType, [validate.number, validate.required])}"
          >
            <span class="subheading grey--text">
              {{ returnGraceType(rateItem.rateTypeObject.graceType) }}
            </span>
          </v-layout>
        </div>

        <div class="thick-border-right big-width">
          <v-layout v-if="selectedEdit === index">
            <v-flex md5 class="border-right">
              <v-text-field
                box
                type="number"
                v-model.number="rateItem.rateTypeObject.standbyRate"
                hide-details
                :rules="[validate.required, validate.number]"
                @focus="$event.target.select()"
              >
              </v-text-field>
            </v-flex>
            <v-flex md7 class="border-right">
              <v-select
                class="multi-box"
                box
                :items="rateMultipliers"
                item-text="longName"
                item-value="id"
                v-model="rateItem.rateTypeObject.standbyMultiplier"
                hide-details
                :rules="[validate.required, validate.number]"
              ></v-select>
            </v-flex>
          </v-layout>

          <v-layout v-else fill-height>
            <v-flex md5>
              <v-layout
                align-center
                fill-height
                justify-center
                :class="{'error--text' : validationError(rateItem.rateTypeObject.standbyRate, [validate.number, validate.required])}"
              >
                <span class="subheading grey--text">
                  {{
                    displayCurrencyValue(rateItem.rateTypeObject.standbyRate)
                  }}
                </span>
              </v-layout>
            </v-flex>
            <v-flex md7 class="border-left">
              <v-layout
                align-center
                fill-height
                justify-center
                :class="{'error--text' : validationError(rateItem.rateTypeObject.standbyMultiplier, [validate.number, validate.required])}"
              >
                <span class="subheading grey--text">
                  {{
                    returnMultiplierValue(
                      rateItem.rateTypeObject.standbyMultiplier
                    )
                  }}
                </span>
              </v-layout>
            </v-flex>
          </v-layout>
        </div>
        <div
          class="thick-border-right small-width"
          @click="setSelectedEdit(index)"
          style="cursor: pointer; width: 8% !important"
        >
          <v-select
            v-if="selectedEdit === index"
            box
            :items="startAndReturnLegTypes.filter(t => [2,3].includes(t.id))"
            class="grace-type-box"
            item-text="type"
            item-value="id"
            v-model="rateItem.rateTypeObject.firstLegTypeId"
            hide-details
            :rules="[validate.required, validate.number]"
          ></v-select>
          <v-layout
            v-else
            fill-height
            align-center
            class="pl-2 row-height multi-box"
            :class="{'error--text' : validationError(rateItem.rateTypeObject.firstLegTypeId, [validate.number, validate.required])}"
          >
            <span class="subheading grey--text">
              {{ returnLegTypeName(rateItem.rateTypeObject.firstLegTypeId) }}
            </span>
          </v-layout>
        </div>

        <div
          class="thick-border-right"
          @click="setSelectedEdit(index)"
          style="cursor: pointer; width: 8% !important"
        >
          <v-select
            v-if="selectedEdit === index"
            box
            :items="startAndReturnLegTypes"
            class="grace-type-box"
            item-text="type"
            item-value="id"
            v-model="rateItem.rateTypeObject.lastLegTypeId"
            hide-details
            :rules="[validate.required, validate.number]"
          ></v-select>
          <v-layout
            v-else
            fill-height
            align-center
            class="pl-2 row-height multi-box"
            :class="{'error--text' : validationError(rateItem.rateTypeObject.lastLegTypeId, [validate.number, validate.required])}"
          >
            <span class="subheading grey--text">
              {{ returnLegTypeName(rateItem.rateTypeObject.lastLegTypeId) }}
            </span>
          </v-layout>
        </div>

        <div
          class="thick-border-right small-width"
          style="width: 13% !important"
        >
          <v-layout fill-height justify-center align-center>
            <v-select
              v-if="selectedEdit === index"
              box
              :items="applicableFuelSurcharges"
              class="grace-type-box"
              item-text="shortName"
              item-value="id"
              v-model="rateItem.rateTypeObject.appliedFuelSurchargeId"
              hide-details
              :rules="[validate.required, validate.number]"
            ></v-select>

            <v-layout
              v-else
              fill-height
              align-center
              class="pl-2 row-height multi-box"
              :class="{'error--text' : validationError(rateItem.rateTypeObject.appliedFuelSurchargeId, [validate.number, validate.required])}"
            >
              <span class="subheading grey--text">
                {{
                  returnApplicableFuelSurchargeShortName(
                    rateItem.rateTypeObject.appliedFuelSurchargeId
                  )
                }}
              </span>
            </v-layout>
          </v-layout>
        </div>

        <div
          class="thick-border-right small-width"
          style="width: 7% !important"
        >
          <v-layout fill-height justify-center align-center>
            <v-checkbox
              :disabled="selectedEdit !== index"
              class="fuel-checkbox mt-0"
              v-model="rateItem.rateTypeObject.standbyFuelSurchargeApplies"
              hide-details
              :rules="[validate.boolean]"
            ></v-checkbox>
          </v-layout>
        </div>

        <div
          class="small-width thick-border-right"
          style="width: 5% !important"
        >
          <v-layout
            v-if="timeRateItems.length > 1"
            justify-center
            fill-height
            align-center
          >
            <v-icon v-if="!copyActive" fab small @click="copyValue(index)"
              >fad fa-copy
            </v-icon>
            <v-checkbox
              v-if="copyActive && pasteIconLocation !== index"
              v-model="selectedBulkChanges"
              :value="index"
              class="fuel-checkbox mt-0"
              hide-details
            >
            </v-checkbox>
            <v-icon
              fab
              @click="bulkChanges(index)"
              small
              v-if="copyActive && pasteIconLocation === index"
            >
              fad fa-paste
            </v-icon>
          </v-layout>
        </div>
      </div>
    </v-layout>
  </div>
</section>
