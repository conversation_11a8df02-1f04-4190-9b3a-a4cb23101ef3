<template>
  <section class="service-rate-table-distance">
    <v-data-table
      class="gd-dark-theme"
      :headers="tableHeaders"
      :items="distanceRateItems"
      :rows-per-page-items="[10, 20]"
    >
      <template v-slot:items="data">
        <tr
          style="cursor: pointer; position: relative"
          @click="editDistanceRateItem(data.item)"
        >
          <td class="text-xs-center">{{ data.item.serviceLongName }}</td>
          <td class="text-xs-center">
            {{
              `${returnReadableChargeBasisName(
                data.item.rateTypeObject.chargeBasis,
              )} (${returnReadableRateBracketTypeName(
                data.item.rateTypeObject.rateBracketType,
              )})`
            }}
          </td>
          <td class="text-xs-center">
            ${{
              DisplayCurrencyValue(
                data.item.rateTypeObject.baseFreightCharge ?? 0,
              )
            }}
          </td>
          <td class="text-xs-center">
            {{ returnChargeIncrementDescription(data.item.rateTypeObject) }}
          </td>
          <td class="text-xs-center">
            {{
              `${
                returnStartAndReturnLegsLongNameById(
                  data.item.rateTypeObject.firstLegTypeId,
                ) || 'N/A'
              } / ${
                returnStartAndReturnLegsLongNameById(
                  data.item.rateTypeObject.lastLegTypeId,
                ) || 'N/A'
              }`
            }}
          </td>
          <td class="text-xs-center">
            {{ returnMinimumChargeDescription(data.item.rateTypeObject) }}
          </td>
          <td class="text-xs-center">
            {{
              demurrageAppliesLabel(
                data.item.rateTypeObject.demurrage?.appliedDemurrageId,
              )
            }}
          </td>
          <td class="text-xs-center">
            {{ returnRangeRateListSummary(data.item.rateTypeObject.rates, 0) }}
          </td>
        </tr>
      </template>
    </v-data-table>

    <ContentDialog
      :showDialog.sync="isViewingDialog"
      :title="dialogTitle"
      width="60%"
      contentPadding="pa-0"
      @cancel="isViewingDialog = false"
      @confirm="applyChanges"
      :showActions="isEdited"
      :isConfirmUnsaved="false"
      :isDisabled="false"
      :isLoading="false"
      confirmBtnText="Confirm"
    >
      <v-form ref="distanceForm">
        <v-flex
          id="distanceFormScrollContainer"
          class="body-scrollable--75 body-min-height--65 pa-3"
          v-if="
            editedRateItem?.rateTypeObject &&
            isDistanceRateTypeObject(
              editedRateItem.rateTypeId,
              editedRateItem.rateTypeObject,
            )
          "
        >
          <v-layout row wrap style="padding: 0px 50px">
            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">
                  Rate Calculation Method
                </h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">Charge Basis:</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    class="v-solo-custom"
                    solo
                    flat
                    :items="getChargeBasisList()"
                    color="light-blue"
                    label="Charge Basis"
                    item-value="id"
                    item-text="longName"
                    v-model="editedRateItem.rateTypeObject.chargeBasis"
                    :disabled="!isEdited"
                    :hint="
                      returnReadableChargeBasisDescription(
                        editedRateItem.rateTypeObject.chargeBasis,
                      )
                    "
                    persistent-hint
                  ></v-select>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">Minimum Charge:</h6>
                  </v-layout>
                </v-flex>
                <v-flex md4>
                  <v-text-field
                    class="v-solo-custom"
                    type="number"
                    solo
                    flat
                    :prefix="
                      editedRateItem.rateTypeObject.minChargeBasis ===
                      MinChargeBasis.AMOUNT
                        ? '$'
                        : ''
                    "
                    :suffix="
                      editedRateItem.rateTypeObject.minChargeBasis ===
                      MinChargeBasis.DISTANCE
                        ? 'KM'
                        : ''
                    "
                    color="light-blue"
                    label="Minimum Charge"
                    @focus="$event.target.select()"
                    v-model.number="editedRateItem.rateTypeObject.minCharge"
                    :disabled="!isEdited"
                  ></v-text-field>
                </v-flex>
                <v-flex md4 pl-2>
                  <v-select
                    class="v-solo-custom"
                    color="light-blue"
                    solo
                    flat
                    :items="getMinChargeBasisList()"
                    label="Min Charge Basis"
                    item-value="id"
                    item-text="longName"
                    v-model="editedRateItem.rateTypeObject.minChargeBasis"
                    :disabled="!isEdited"
                  ></v-select>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Increment Charge Every:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md4>
                  <v-text-field
                    class="v-solo-custom"
                    type="number"
                    solo
                    flat
                    suffix="KM"
                    color="light-blue"
                    label="Charge Increment"
                    :rules="[validate.required, validate.positiveInteger]"
                    v-model.number="
                      editedRateItem.rateTypeObject.chargeIncrement
                    "
                    :disabled="!isEdited"
                    @focus="$event.target.select()"
                  ></v-text-field>
                </v-flex>
                <v-flex md4 pl-2>
                  <v-select
                    class="v-solo-custom"
                    color="light-blue"
                    solo
                    flat
                    :items="graceTypes"
                    label="Grace Type"
                    item-value="id"
                    item-text="longName"
                    v-model="editedRateItem.rateTypeObject.graceType"
                    :disabled="!isEdited"
                  ></v-select>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">Fuel Levy:</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    class="v-solo-custom"
                    color="light-blue"
                    solo
                    flat
                    :items="filteredFuelSurchargeOptions"
                    item-text="shortName"
                    item-value="id"
                    v-model="
                      editedRateItem.rateTypeObject.appliedFuelSurchargeId
                    "
                    :rules="[validate.required, validate.number]"
                    :disabled="!isEdited"
                  ></v-select>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">
                  Additional Travel Time
                </h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">Pre-job:</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    class="v-solo-custom"
                    color="light-blue"
                    solo
                    flat
                    :items="
                      startAndReturnLegs.filter((t) => [2, 3].includes(t.id))
                    "
                    item-text="type"
                    item-value="id"
                    v-model="editedRateItem.rateTypeObject.firstLegTypeId"
                    :rules="[validate.required, validate.number]"
                    :disabled="!isEdited"
                  ></v-select>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">Post-job:</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    class="v-solo-custom"
                    color="light-blue"
                    solo
                    flat
                    :items="startAndReturnLegs"
                    item-text="type"
                    item-value="id"
                    v-model="editedRateItem.rateTypeObject.lastLegTypeId"
                    :rules="[validate.required, validate.number]"
                    :disabled="!isEdited"
                  ></v-select>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout align-center>
                <h5 class="subheader--bold pr-3">Demurrage</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Demurrage Rate Application:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    class="v-solo-custom"
                    color="light-blue"
                    solo
                    flat
                    :items="applyDemurrageOptions"
                    :disabled="!isEdited"
                    item-text="shortName"
                    label="Demurrage Rate Application"
                    item-value="id"
                    v-model="
                      editedRateItem.rateTypeObject.demurrage.appliedDemurrageId
                    "
                    :rules="[validate.required, validate.number]"
                  ></v-select>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Demurrage Rate (per hour):
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-text-field
                    class="v-solo-custom"
                    color="light-blue"
                    solo
                    flat
                    label="Demurrage Rate (per hour)"
                    :prefix="'$'"
                    type="number"
                    :disabled="
                      editedRateItem.rateTypeObject.demurrage
                        .appliedDemurrageId ===
                        DemurrageAppliesEnum.DONT_APPLY || !isEdited
                    "
                    v-model.number="
                      editedRateItem.rateTypeObject.demurrage.rate
                    "
                    :rules="[validate.required, validate.number]"
                    @focus="$event.target.select()"
                  ></v-text-field>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Demurrage Applies After:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-text-field
                    class="v-solo-custom"
                    color="light-blue"
                    solo
                    flat
                    label="Demurrage Grace Time (mins)"
                    type="number"
                    suffix="Min"
                    v-model.number="
                      editedRateItem.rateTypeObject.demurrageGraceTimeMins
                    "
                    :disabled="
                      editedRateItem.rateTypeObject.demurrage
                        .appliedDemurrageId ===
                        DemurrageAppliesEnum.DONT_APPLY || !isEdited
                    "
                    :rules="[validate.required, validate.number]"
                    @focus="$event.target.select()"
                  ></v-text-field>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Fuel Surcharge Applicable:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-checkbox
                    v-model="
                      editedRateItem.rateTypeObject.demurrage
                        .demurrageFuelSurchargeApplies
                    "
                    :label="
                      editedRateItem.rateTypeObject.demurrage
                        .demurrageFuelSurchargeApplies
                        ? 'Fuel Surcharge applies to Demurrage'
                        : 'Fuel Surcharge does not apply to Demurrage'
                    "
                    :disabled="
                      editedRateItem.rateTypeObject.demurrage
                        .appliedDemurrageId ===
                        DemurrageAppliesEnum.DONT_APPLY || !isEdited
                    "
                    color="light-blue"
                    class="mt-2"
                  />
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">Charge Calculation</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Base Freight Charge:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-text-field
                    class="v-solo-custom"
                    type="number"
                    solo
                    flat
                    prefix="$"
                    color="light-blue"
                    label="Base Freight Charge"
                    @focus="$event.target.select()"
                    v-model.number="
                      editedRateItem.rateTypeObject.baseFreightCharge
                    "
                    :rules="[
                      validate.required,
                      validate.number,
                      validate.nonNegative,
                    ]"
                    :disabled="!isEdited"
                  ></v-text-field>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Charge Calculation Method:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    class="v-solo-custom"
                    solo
                    flat
                    :items="getRateBracketTypeList()"
                    color="light-blue"
                    label="Bracket Type"
                    item-value="id"
                    item-text="longName"
                    v-model="bracketTypeController"
                    :hint="
                      returnReadableRateBracketDescription(
                        bracketTypeController,
                      )
                    "
                    persistent-hint
                    :disabled="!isEdited"
                  ></v-select>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <table class="simple-data-table">
                <thead>
                  <tr>
                    <th>Start (KM)</th>
                    <th>End (KM)</th>
                    <th>Summary</th>
                    <th>Rate ($)</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(range, index) in editedRateItem.rateTypeObject
                      .rates"
                    :key="range.id"
                    style="position: relative"
                  >
                    <td class="text-xs-center">
                      {{ `${range.bracketMin} km` }}
                    </td>
                    <!-- <td class="text-xs-center">
                      <v-checkbox
                        v-if="
                          index ===
                          editedRateItem.rateTypeObject.rates.length - 1
                        "
                        :value="range.bracketMax !== -1"
                        color="light-blue"
                        @change="
                          toggleBracketMax(
                            $event,
                            editedRateItem.rateTypeObject.rates,
                            index,
                          )
                        "
                        :disabled="!isEdited"
                      ></v-checkbox>
                    </td> -->
                    <td class="text-xs-center td-max-width">
                      <v-text-field
                        v-if="
                          index !==
                          editedRateItem.rateTypeObject.rates.length - 1
                        "
                        :id="range.id"
                        class="v-solo-custom"
                        color="light-blue"
                        solo
                        flat
                        label="Range End"
                        suffix="KM"
                        type="number"
                        validate-on-blur
                        v-model.number="range.bracketMax"
                        :disabled="
                          index !==
                            editedRateItem.rateTypeObject.rates.length - 1 &&
                          !isEdited
                        "
                        :rules="[
                          validate.required,
                          validate.number,
                          validate.nonNegative,
                          validateBracketMax(
                            editedRateItem.rateTypeObject.rates,
                            index,
                          ),
                        ]"
                        @change="
                          updateNextBracketMin(
                            editedRateItem.rateTypeObject.rates,
                            index,
                          )
                        "
                        @focus="$event.target.select()"
                      ></v-text-field>
                      <!-- <v-text-field
                        v-else
                        class="v-solo-custom"
                        color="light-blue"
                        solo
                        flat
                        label="Range End"
                        suffix="KM"
                        disabled
                        :value="'No Limit'"
                      ></v-text-field> -->
                      <span v-else> No Limit </span>
                    </td>
                    <td class="text-xs-center">
                      {{ returnRangeRateSummary(range, index) }}
                    </td>
                    <td class="text-xs-center td-max-width">
                      <v-text-field
                        class="v-solo-custom"
                        color="light-blue"
                        solo
                        flat
                        label="Rate ($)"
                        prefix="$"
                        type="number"
                        :disabled="!isEdited"
                        v-model.number="range.rate"
                        :rules="[
                          validate.required,
                          validate.number,
                          validate.nonNegative,
                        ]"
                        @focus="$event.target.select()"
                      ></v-text-field>
                    </td>
                    <td class="text-xs-center">
                      <v-tooltip left>
                        <template v-slot:activator="{ on }">
                          <v-btn
                            flat
                            v-on="on"
                            icon
                            @click="removeRangedRate(range.id)"
                            class="mx-0"
                            tabindex="-1"
                            :disabled="index === 0 || !isEdited"
                          >
                            <v-icon size="20" color="grey lighten-1"
                              >fal fa-times</v-icon
                            >
                          </v-btn>
                        </template>
                        Remove Row
                      </v-tooltip>
                    </td>
                  </tr>
                  <tr class="last-row">
                    <td colspan="6">
                      <v-layout justify-center align-center pt-1>
                        <button
                          type="button"
                          class="add-item-button"
                          :disabled="!isEdited"
                          @click="addNewRangeRate"
                        >
                          <v-layout align-center>
                            <span class="pr-2">ADD NEW</span>
                            <v-icon class="add-item-icon">fal fa-plus</v-icon>
                          </v-layout>
                        </button>
                      </v-layout>
                    </td>
                  </tr>
                </tbody>
              </table>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-form>
    </ContentDialog>
  </section>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  returnChargeIncrementDescription,
  returnMinimumChargeDescription,
  returnRangeRateListSummary,
  returnRangeRateSummary,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import { isDistanceRateTypeObject } from '@/helpers/RateHelpers/RateTableItemHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { initialiseRateTableItems } from '@/helpers/classInitialisers/InitialiseRateTableItems';
import { graceTypes } from '@/interface-models/Generic/ServiceTypes/GraceTypes';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import {
  returnStartAndReturnLegsLongNameById,
  startAndReturnLegs,
} from '@/interface-models/Generic/StartAndReturnLegs/StartAndReturnLegs';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import applicableDemurrages, {
  DemurrageAppliesEnum,
  demurrageAppliesLabel,
} from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import {
  getChargeBasisList,
  returnReadableChargeBasisDescription,
  returnReadableChargeBasisName,
} from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import {
  getMinChargeBasisList,
  MinChargeBasis,
} from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/MinChargeBasis';
import RangedRate from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RangedRate';
import {
  getRateBracketTypeList,
  RateBracketType,
  returnReadableRateBracketDescription,
  returnReadableRateBracketTypeName,
} from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import { v4 as uuidv4 } from 'uuid';
import {
  computed,
  ComputedRef,
  nextTick,
  Ref,
  ref,
  WritableComputedRef,
} from 'vue';

const props = defineProps<{
  type?: RateEntityType;
  isEdited: boolean;
  serviceRate: ClientServiceRate | FleetAssetServiceRate | null;
}>();

const distanceForm: Ref<any> = ref(null);
const validate: Ref<Validation> = ref(validationRules);

const editedRateItem: Ref<RateTableItems | null> = ref(null);

/**
 * Returns a list of all of the distance type RateTableItems in the service rate
 * prop
 */
const distanceRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (props.serviceRate) {
    return props.serviceRate.rateTableItems.filter((item) =>
      isDistanceRateTypeObject(item.rateTypeId, item.rateTypeObject),
    );
  } else {
    return [];
  }
});

/**
 * Returns the dialog title containing the service type name of the
 * editedRateItem.
 */
const dialogTitle: ComputedRef<string> = computed(() => {
  if (!editedRateItem.value) {
    return '';
  }
  return `Edit Distance Rate (${editedRateItem.value.serviceLongName})`;
});

/**
 * Returns the applicable fuel surcharge options based on whether the component
 * is for clients or fleet assets. If type is CLIENT, the fuel surcharge option
 * with id 2 (Apply (Client > 0%)) is excluded.
 */
const filteredFuelSurchargeOptions: ComputedRef<ShortLongName[]> = computed(
  () => {
    if (props.type === RateEntityType.CLIENT) {
      return applicableFuelSurcharges.filter((x) => x.id !== 2);
    } else {
      return applicableFuelSurcharges;
    }
  },
);

/**
 * Returns true if the dialog is open, false otherwise. If the dialog is closed,
 * the editedRateItem is set to null.
 */
const isViewingDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return editedRateItem.value !== null;
  },
  set(value: boolean): void {
    if (!value) {
      editedRateItem.value = null;
    }
  },
});

/**
 * Returns the applicable demurrage options based on whether the component is
 * for clients or fleet assets.
 */
const applyDemurrageOptions: ComputedRef<ShortLongName[]> = computed(() => {
  return applicableDemurrages.filter((x: ShortLongName) => {
    if (x.id !== 4 && x.id !== 2) {
      return true;
    }
    if (props.type === RateEntityType.CLIENT && x.id === 4) {
      return false;
    }
    if (props.type === RateEntityType.FLEET_ASSET && x.id === 2) {
      return false;
    }

    return true;
  });
});

/**
 * Table header for v-data-table in template
 */
const tableHeaders: ComputedRef<TableHeader[]> = computed(() => {
  const headers: TableHeader[] = [
    {
      text: 'Service',
      align: 'left',
      value: 'serviceLongName',
      sortable: false,
    },
    {
      text: 'Calculation',
      align: 'center',
      value: '',
      sortable: false,
    },
    {
      text: 'Base Freight',
      align: 'center',
      value: '',
      sortable: false,
    },
    {
      text: 'Rate Increment',
      align: 'center',
      value: '',
      sortable: false,
    },
    {
      text: 'First Leg / Last Leg',
      align: 'center',
      value: '',
      sortable: false,
    },
    {
      text: 'Minimum Charge',
      align: 'center',
      value: '',
      sortable: false,
    },
    {
      text: 'Demurrage',
      align: 'center',
      value: '',
      sortable: false,
    },
    {
      text: 'Ranges',
      align: 'center',
      value: '',
      sortable: false,
    },
  ];

  return headers;
});

/**
 * Modelled to the bracket type input in the template. Gets and sets
 * rateBracketType in the editedRateItem, and sets the bracketMin for each rate
 * based on the bracketType.
 */
const bracketTypeController: WritableComputedRef<RateBracketType | null> =
  computed({
    get(): RateBracketType | null {
      if (
        editedRateItem.value &&
        isDistanceRateTypeObject(
          editedRateItem.value.rateTypeId,
          editedRateItem.value.rateTypeObject,
        )
      ) {
        return editedRateItem.value.rateTypeObject.rateBracketType;
      }
      return null;
    },
    set(value: RateBracketType | null): void {
      if (
        value &&
        editedRateItem.value &&
        isDistanceRateTypeObject(
          editedRateItem.value.rateTypeId,
          editedRateItem.value.rateTypeObject,
        )
      ) {
        editedRateItem.value.rateTypeObject.rateBracketType = value;
        setBracketMinForBracketType(
          editedRateItem.value.rateTypeObject.rateBracketType,
          editedRateItem.value.rateTypeObject.rates,
        );
      }
    },
  });

/**
 * Called when the bracket type value is changed, or when a range is removed.
 * Iterates over the rangeRates and sets the bracketMin value for each rate
 * based on the bracketType.
 *
 * @param bracketType - The bracket type to set the bracketMin values for.
 * @param rangeRates - The array of rate objects.
 */
function setBracketMinForBracketType(
  bracketType: RateBracketType,
  rangeRates: RangedRate[],
) {
  if (bracketType === RateBracketType.ABSOLUTE) {
    // Set all bracketMin values to 0
    rangeRates.forEach((rate) => {
      rate.bracketMin = 0;
    });
  } else {
    // Iterate over rangeRates and set the bracketMin to the bracketMax of the previous rate
    rangeRates.forEach((rate, index) => {
      if (index === 0) {
        rate.bracketMin = 0;
      } else {
        rate.bracketMin = rangeRates[index - 1].bracketMax;
      }
    });
  }
}

/**
 * For the provided id, finds the associated RangedRate entry in editedRateItem,
 * and removes it.
 * @param id - The id of the RangedRate to remove.
 */
function removeRangedRate(id: string) {
  if (
    editedRateItem.value &&
    isDistanceRateTypeObject(
      editedRateItem.value.rateTypeId,
      editedRateItem.value.rateTypeObject,
    )
  ) {
    editedRateItem.value.rateTypeObject.rates =
      editedRateItem.value.rateTypeObject.rates.filter((x) => x.id !== id);

    // Find the new last rate and set its bracketMax to -1
    const lastRate =
      editedRateItem.value.rateTypeObject.rates[
        editedRateItem.value.rateTypeObject.rates.length - 1
      ];
    if (lastRate) {
      lastRate.bracketMax = -1;
    }

    // Now iterate over the rates and set the bracketMin of each rate to the
    // bracketMax of the previous rate
    setBracketMinForBracketType(
      editedRateItem.value.rateTypeObject.rateBracketType,
      editedRateItem.value.rateTypeObject.rates,
    );
  }
}

/**
 * Adds a new range rate to the editedRateItem's rateTypeObject.
 *
 * Steps:
 * 1. Validate `editedRateItem.value` and its type.
 * 2. Retrieve `rateBracketType`.
 * 3. Adjust `bracketMax` of the last rate based on `rateBracketType`.
 * 4. Create a new rate entry with `bracketMin` and `bracketMax`.
 * 5. Focus the old last row's text field and scroll to the bottom.
 */
function addNewRangeRate() {
  if (
    editedRateItem.value &&
    isDistanceRateTypeObject(
      editedRateItem.value.rateTypeId,
      editedRateItem.value.rateTypeObject,
    )
  ) {
    const rateBracketType = editedRateItem.value.rateTypeObject.rateBracketType;
    // Find the bracketMax of the last rate entry, and set it's bracket max to its bracket min + 1
    const lastRate =
      editedRateItem.value.rateTypeObject.rates[
        editedRateItem.value.rateTypeObject.rates.length - 1
      ];

    // Set the previous rates bracketMax based on the rateBracketType. If
    // bracket type is PROGRESSIVE, then we set it to the bracketMin + 1. If
    // it's ABSOLUTE, we find the second last rate and set the lastRate's
    // bracketMax to the second last one's bracketMax + 1. If there is no second
    // last rate, we set the last rate's bracketMax to 1
    if (lastRate) {
      if (rateBracketType === RateBracketType.PROGRESSIVE) {
        lastRate.bracketMax = lastRate.bracketMin + 1;
      } else {
        const secondLastRate =
          editedRateItem.value.rateTypeObject.rates[
            editedRateItem.value.rateTypeObject.rates.length - 2
          ];
        if (secondLastRate) {
          lastRate.bracketMax = secondLastRate.bracketMax + 1;
        } else {
          lastRate.bracketMax = 1;
        }
      }
    }

    // Create a new rate entry with a bracketMin based on the rateBracketType, and a bracketMax of -1
    const newBracketMin =
      rateBracketType === RateBracketType.ABSOLUTE ? 0 : lastRate.bracketMax;
    editedRateItem.value.rateTypeObject.rates.push({
      id: uuidv4().replace(/-/g, ''),
      bracketMin: newBracketMin,
      bracketMax: -1,
      rate: 0,
    });
    // Focus textfield in new row
    nextTick().then(() => {
      const textField = document.getElementById(lastRate.id);
      if (textField instanceof HTMLInputElement) {
        textField.focus();
      }
      // Scroll the distanceFormScrollContainer div to the bottom
      const scrollContainer = document.getElementById(
        'distanceFormScrollContainer',
      );
      if (scrollContainer instanceof HTMLElement) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    });
  }
}

/**
 * Sets the provided rateTableItem as the editedRateItem, which opens it in the
 * dialog for editing.
 * @param rateTableItem - The rateTableItem to edit.
 */
function editDistanceRateItem(rateTableItem: RateTableItems) {
  editedRateItem.value = initialiseRateTableItems(rateTableItem);
}

/**
 * Called when the bracketMax value is changed. Syncs the updated bracketMax value with the bracketMin
 * value of the next rate entry.
 * @param rates - The array of rate objects.
 * @param index - The index of the current rate being updated.
 */
function updateNextBracketMin(rates: RangedRate[], index: number): void {
  if (!bracketTypeController.value) {
    return;
  }
  const currentRate = rates[index];
  const nextRate = rates[index + 1];

  if (nextRate) {
    if (bracketTypeController.value === RateBracketType.ABSOLUTE) {
      nextRate.bracketMin = 0;
    } else {
      nextRate.bracketMin = currentRate.bracketMax;
    }
  }
}

/**
 * Validates the bracketMax value for a given rate in the rates array.
 * Ensures that the bracketMax value is greater than the bracketMin value,
 * and that it follows the rules for ABSOLUTE and PROGRESSIVE bracket types.
 *
 * @param {RangedRate[]} rates - The array of rate objects.
 * @param {number} index - The index of the current rate being validated.
 * @returns {true | string} - Returns true if validation passes, otherwise returns an error message.
 */
function validateBracketMax(rates: RangedRate[], index: number): true | string {
  if (!bracketTypeController.value) {
    return 'Please set the Charge Calculation Method';
  }
  const currentRate = rates[index];
  const nextRate = rates[index + 1];

  if (index === rates.length - 1 && currentRate.bracketMax !== -1) {
    return 'The last Range End should not have a limit set. Try removing and re-adding this row.';
  }

  if (currentRate.bracketMax <= currentRate.bracketMin) {
    return 'Range End should be greater than Range Start';
  }

  if (bracketTypeController.value === RateBracketType.ABSOLUTE) {
    // Validate that this Range End value is greater than all previous
    // Range End values, and less than all subsequent Range End values (unless
    // they are -1)
    for (let i = 0; i < rates.length; i++) {
      if (i === index) {
        continue;
      }
      const rate = rates[i];
      if (i < index && currentRate.bracketMax <= rate.bracketMax) {
        return "Range End should be greater than the previous row's Range End";
      }
      if (
        i > index &&
        rate.bracketMax !== -1 &&
        currentRate.bracketMax >= rate.bracketMax
      ) {
        return "Range End should be less than the next row's Range End";
      }
    }
  } else {
    // For PROGRESSIVE bracket type, the Range End should be less than the next
    // row's Range Start so that the ranges do not overlap
    if (nextRate && currentRate.bracketMax > nextRate.bracketMin) {
      return "Range End should be less than the next row's Range Start";
    }
  }

  return true;
}

/**
 * Validates the form, then applies the changes to the rateTableItems in the
 * serviceRate prop. If the rateTableItem does not exist in the serviceRate
 * prop, it is added. If it does exist, it is replaced. Closes the dialog after
 * applying changes.
 */
function applyChanges() {
  if (
    !editedRateItem.value?.rateTypeObject ||
    !editedRateItem.value?.serviceTypeId
  ) {
    showNotification(GENERIC_ERROR_MESSAGE);
    return;
  }
  if (!distanceForm.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  const foundIndex = props.serviceRate?.rateTableItems.findIndex(
    (x) =>
      x.serviceTypeId === editedRateItem.value!.serviceTypeId &&
      x.rateTypeId === editedRateItem.value!.rateTypeId,
  );
  if (foundIndex === -1) {
    props.serviceRate?.rateTableItems.push(editedRateItem.value);
  } else {
    props.serviceRate?.rateTableItems.splice(
      foundIndex ?? 0,
      1,
      editedRateItem.value,
    );
  }
  isViewingDialog.value = false;
}
</script>

<style scoped lang="scss">
.service-rate-table-distance {
  position: relative;
}
.last-row {
  .add-item-button {
    padding: 6px 18px;
    border-radius: 12px;
    background-color: var(--primary);
    color: white;
    font-weight: 700;
    opacity: 0.8;
    transition:
      opacity 0.1s ease-in-out,
      background-color 0.1s ease-in-out;
    font-family: $sub-font-family;
    color: white;

    .add-item-icon {
      color: white;
    }
    &:focus {
      opacity: 1;
      scale: 1.05;
    }
    &:disabled {
      background-color: var(--background-color-600);
      color: white;
    }
  }
}

table.simple-data-table {
  tr {
    td {
      font-size: $font-size-16;
      &.td-max-width {
        width: 25%;
        max-width: 25%;
      }
    }

    &:last-child {
      background-color: transparent;
    }
  }
}
</style>
