import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { getServiceTypeById } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import rateMultipliers, {
  RateMultipliers,
} from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import applicableDemurrages from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';
interface ZoneRateTableData {
  index: number;
  serviceTypeName: string;
  serviceTypeId: number;
  zoneName: string;
  zoneRate: string;
  pickupFlagfall: number;
  dropoffFlagfall: number;
  demurrageGrace: string;
  demurrageRate: string;
  appliedFuelSurcharge: string;
  demurrageFuelSurchargeApplies: string;
  percentage: string;
  isClient: boolean;
}
@Component({
  components: {
    ConfirmationDialog,
  },
})
export default class ServiceRateTableZone extends Vue {
  @Prop() public serviceRate: ClientServiceRate | FleetAssetServiceRate;
  @Prop() public fuelSurchargeRate:
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate;
  @Prop({ default: false }) public isFleetAsset: boolean;
  @Prop({ default: true }) public isEdited: boolean;
  public rateId: number = 2;
  public services: ServiceTypes[] = [];
  public selectedBulkChanges: number[] = [];
  public bulkChange: number = -1;

  public applicableDemurrages: ShortLongName[] = applicableDemurrages.filter(
    (x: ShortLongName) => {
      if (x.id !== 4 && x.id !== 2) {
        return true;
      }

      if (!this.isFleetAsset && x.id === 4) {
        return false;
      }

      if (this.isFleetAsset && x.id === 2) {
        return false;
      }

      return true;
    },
  );
  public applicableFuelSurcharges: ShortLongName[] =
    applicableFuelSurcharges.filter((x: ShortLongName) =>
      !this.isFleetAsset && x.id === 2 ? false : true,
    );
  public displayCurrencyValue: any = DisplayCurrencyValue;

  public isNewRate: boolean = false;
  public editedServiceTypeId: number = -1;
  public editedZoneIndex: number = -1;
  public selectedServiceTypeId: number = -1;
  public zoneRate: ZoneRateType | null = null;

  public $refs!: {
    zoneForm: VForm;
  };

  get dialogIsOpen(): boolean {
    return this.zoneRate !== null;
  }

  set dialogIsOpen(value: boolean) {
    if (!value) {
      this.zoneRate = null;
    }
  }
  get headers(): TableHeader[] {
    const headers: TableHeader[] = [
      {
        text: 'Service',
        align: 'left',
        value: 'serviceTypeName',
        sortable: false,
      },
    ];
    if (!this.isFleetAsset) {
      headers.push(
        ...([
          {
            text: 'Zone',
            align: 'left',
            value: 'zoneName',
            sortable: false,
          },
          {
            text: 'Rate',
            align: 'left',
            value: 'zoneRate',
            sortable: false,
          },
          {
            text: 'Pickup Flagfall',
            align: 'left',
            value: 'pickupFlagfall',
            sortable: false,
          },
          {
            text: 'Dropoff Flagfall',
            align: 'left',
            value: 'dropoffFlagfall',
            sortable: false,
          },
        ] as TableHeader[]),
      );
    } else {
      headers.push(
        ...([
          {
            text: 'Percentage',
            align: 'left',
            value: 'percent',
            sortable: false,
          },
        ] as TableHeader[]),
      );
    }

    headers.push(
      ...([
        {
          text: 'Fuel Surcharge',
          align: 'left',
          value: 'appliedFuelSurcharge',
          sortable: false,
        },
        {
          text: 'Demurrage',
          align: 'left',
          value: 'appliedFuelSurcharge',
          sortable: false,
        },
        {
          text: 'Demurrage Rate',
          align: 'left',
          value: 'demurrageRate',
          sortable: false,
        },
      ] as TableHeader[]),
    );

    if (!this.isFleetAsset) {
      headers.push(
        ...([
          {
            text: 'Demurrage Grace',
            align: 'left',
            value: 'demurrageGrace',
            sortable: false,
          },
        ] as TableHeader[]),
      );
    }

    headers.push(
      ...([
        {
          text: 'Demurrage Fuel Surcharge',
          align: 'left',
          value: 'demurrageFuelSurchargeApplies',
          sortable: false,
        },
      ] as TableHeader[]),
    );

    return headers;
  }
  public serviceTypeName(serviceTypeId: number): string {
    const serviceType = getServiceTypeById(serviceTypeId);
    return serviceType ? serviceType.optionSelectName : '-';
  }
  get zoneRateItems(): RateTableItems[] {
    return this.serviceRate.rateTableItems.filter(
      (item: RateTableItems) => item.rateTypeId === this.rateId,
    );
  }

  get graceTimeInMinutes(): number {
    if (!this.zoneRate) {
      return 0;
    }
    return moment
      .duration(this.zoneRate.demurrage.graceTimeInMilliseconds)
      .asMinutes();
  }

  set graceTimeInMinutes(value: number) {
    if (!this.zoneRate) {
      return;
    }
    this.zoneRate.demurrage.graceTimeInMilliseconds = moment
      .duration(value, 'minutes')
      .asMilliseconds();
  }
  public saveZoneRate() {
    if (!this.$refs.zoneForm.validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }

    if (
      ((this.editedServiceTypeId === -1 || this.editedZoneIndex === -1) &&
        !this.isNewRate) ||
      !this.zoneRate
    ) {
      showNotification(GENERIC_ERROR_MESSAGE);
      return;
    }
    // when selectedServiceTypeId === -1 it means we need to update an existing zone rate else we push in a the new zone rate
    const serviceTypeId = !this.isNewRate
      ? this.editedServiceTypeId
      : this.selectedServiceTypeId;
    const zoneByService: RateTableItems | undefined =
      this.serviceRate.rateTableItems.find(
        (x: RateTableItems) =>
          x.serviceTypeId === serviceTypeId && x.rateTypeId === this.rateId,
      );

    if (!zoneByService) {
      return;
    }

    if (!this.isNewRate) {
      (zoneByService.rateTypeObject as ZoneRateType[]).splice(
        this.editedZoneIndex,
        1,
        this.zoneRate,
      );
    } else {
      // we should find the a new id to add to this.zoneRate.zone
      const zoneIds = (zoneByService.rateTypeObject as ZoneRateType[]).map(
        (x: ZoneRateType) => x.zone,
      );
      this.zoneRate.zone = zoneIds.length > 0 ? Math.max(...zoneIds) + 1 : 1;

      (zoneByService.rateTypeObject as ZoneRateType[]).push(this.zoneRate);
    }

    showNotification('Please remember to save your changes.', {
      type: HealthLevel.INFO,
    });
    this.cancelZoneEdit();
  }

  public cancelZoneEdit() {
    this.selectedServiceTypeId = -1;
    this.editedServiceTypeId = -1;
    this.editedZoneIndex = -1;
    this.isNewRate = false;
    this.zoneRate = null;
  }

  get zoneRateTableItems(): ZoneRateTableData[] {
    const tableData: ZoneRateTableData[] = [];

    for (const service of this.zoneRateItems) {
      if (!service.serviceTypeId) {
        continue;
      }
      const serviceTypeName = this.serviceTypeName(service.serviceTypeId);
      const p2pRates: ZoneRateType[] = service.rateTypeObject as ZoneRateType[];
      const zoneTableRate: ZoneRateTableData[] = p2pRates.map(
        (zone: ZoneRateType, index: number) => {
          const appliedFuelSurcharge = applicableFuelSurcharges.find(
            (x: ShortLongName) => zone.appliedFuelSurchargeId === x.id,
          );
          const multiplier = rateMultipliers.find(
            (rateMultiplier: RateMultipliers) =>
              rateMultiplier.id === zone.demurrage.incrementMultiplier,
          );

          const demurrageGraceAsMinutes = moment
            .duration(zone.demurrage.graceTimeInMilliseconds)
            .asMinutes();

          const appliedDemurrage = this.applicableDemurrages.find(
            (x: ShortLongName) => x.id === zone.demurrage.appliedDemurrageId,
          );
          return {
            index,
            serviceTypeName,
            serviceTypeId: service.serviceTypeId!,
            zoneName: zone.zoneName,
            zoneRate: '$' + this.displayCurrencyValue(zone.rate),
            pickupFlagfall: this.displayCurrencyValue(
              zone.additionalPickUpFlagFall,
            ),
            dropoffFlagfall: this.displayCurrencyValue(
              zone.additionalDropOffFlagFall,
            ),
            appliedFuelSurcharge: appliedFuelSurcharge
              ? appliedFuelSurcharge.shortName
              : '-',
            appliedDemurrageCharge: appliedDemurrage
              ? appliedDemurrage.shortName
              : '-',
            demurrageGrace: !demurrageGraceAsMinutes
              ? 'None'
              : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
            demurrageRate:
              '$' +
              this.displayCurrencyValue(zone.demurrage.rate) +
              ' per ' +
              zone.demurrage.increment +
              ' ' +
              (multiplier ? multiplier.longName : '-'),
            percentage: zone.percentage + '%',
            isClient: !this.isFleetAsset,
            demurrageFuelSurchargeApplies: zone.demurrage
              .demurrageFuelSurchargeApplies
              ? 'Apply'
              : `Don't Apply`,
          };
        },
      );
      tableData.push(...zoneTableRate);
    }

    return tableData;
  }

  public editZone(serviceTypeId: number, index: number) {
    this.editedServiceTypeId = serviceTypeId;
    this.editedZoneIndex = index;
    const p2pByService: RateTableItems | undefined = this.zoneRateItems.find(
      (x: RateTableItems) => x.serviceTypeId === this.editedServiceTypeId,
    );
    if (!p2pByService) {
      return;
    }
    this.zoneRate = JSON.parse(
      JSON.stringify(
        (p2pByService.rateTypeObject as ZoneRateType[])[this.editedZoneIndex],
      ),
    );
  }
  public generateNewZoneRate() {
    this.isNewRate = true;
    this.zoneRate = new ZoneRateType();
    this.selectedServiceTypeId = this.existingServiceTypes[0].serviceTypeId;
  }

  get existingServiceTypes() {
    const existingServiceTypeIds = this.zoneRateItems.map(
      (x: RateTableItems) => x.serviceTypeId,
    );
    return useCompanyDetailsStore().getServiceTypesList.filter(
      (service: ServiceTypes) =>
        existingServiceTypeIds.includes(service.serviceTypeId),
    );
  }

  get validate(): Validation {
    return validationRules;
  }

  public removeRate(): void {
    const zoneByService: RateTableItems | undefined =
      this.serviceRate.rateTableItems.find(
        (x: RateTableItems) =>
          x.serviceTypeId === this.editedServiceTypeId &&
          x.rateTypeId === this.rateId,
      );

    const serviceTypeName = this.serviceTypeName(this.editedServiceTypeId);

    if (!zoneByService) {
      return;
    }
    (zoneByService.rateTypeObject as ZoneRateType[]).splice(
      this.editedZoneIndex,
      1,
    );

    this.zoneRate = null;

    showNotification(
      serviceTypeName +
        ' zone rate successfully removed. Please Remember to save.',
      { type: HealthLevel.INFO },
    );
  }
}
