<section class="zone">
  <v-layout justify-end>
    <!-- <v-btn @click="generateNewZoneRate" color="blue" small depressed>Create Zone Rate</v-btn> -->
    <v-menu right>
      <template v-slot:activator="{ on }">
        <v-icon size="18" class="pl-3 pr-1 pb-3" v-on.stop="on"
          >fas fa-ellipsis-v
        </v-icon>
      </template>
      <v-list class="v-list-custom" dense>
        <v-list-tile @click="generateNewZoneRate">
          <v-list-tile-title class="pr-2 ma-0">
            <span>Create Zone Rate</span>
          </v-list-tile-title>
        </v-list-tile>
      </v-list>
    </v-menu>
  </v-layout>
  <v-data-table
    class="gd-dark-theme"
    :headers="headers"
    :items="zoneRateTableItems"
    :rows-per-page-items="[10, 20]"
  >
    <template v-slot:items="props">
      <tr
        style="cursor: pointer; position: relative"
        @click="editZone(props.item.serviceTypeId, props.item.index)"
      >
        <td>{{ props.item.serviceTypeName }}</td>
        <td v-if="props.item.isClient">{{ props.item.zoneName }}</td>
        <td v-if="props.item.isClient">{{ props.item.zoneRate }}</td>
        <td v-if="props.item.isClient">{{ props.item.pickupFlagfall }}</td>
        <td v-if="props.item.isClient">{{ props.item.dropoffFlagfall }}</td>
        <td v-if="!props.item.isClient">{{ props.item.percentage }}</td>
        <td>{{ props.item.appliedFuelSurcharge }}</td>
        <td>{{ props.item.appliedDemurrageCharge }}</td>
        <td>{{ props.item.demurrageRate }}</td>
        <td v-if="props.item.isClient">{{ props.item.demurrageGrace }}</td>
        <td>{{ props.item.demurrageFuelSurchargeApplies }}</td>
      </tr>
    </template>
  </v-data-table>
  <v-dialog
    v-if="zoneRate"
    v-model="dialogIsOpen"
    content-class="v-dialog-custom"
    width="700px"
    persistent
    no-click-animation
  >
    <v-flex md12>
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Zone Rate</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="cancelZoneEdit"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12 class="body-scrollable--70" pa-3>
          <v-layout v-if="isFleetAsset" align-center class="px-2 pt-2">
            <v-flex md12>
              <v-alert
                :value="true"
                type="info"
                outline
                style="background-color: #26263a93"
              >
                <p class="ma-0 alert-text">
                  Please note that a clients fuel surcharge rate above 0% for
                  any zone will satisfy a fuel surcharge application of "Apply
                  (Client > 0%)".
                </p>
              </v-alert>
            </v-flex>
          </v-layout>
          <v-layout align-center class="px-2 pt-2">
            <h5 class="subheader--bold pr-3">Primary rate</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
          <v-form ref="zoneForm">
            <v-layout class="px-2" column>
              <v-layout wrap v-if="!isFleetAsset">
                <v-flex md12>
                  <v-select
                    label="Service Type"
                    :rules="[validate.required]"
                    v-if="isNewRate"
                    v-model="selectedServiceTypeId"
                    :items="existingServiceTypes"
                    item-value="serviceTypeId"
                    box
                    item-text="optionSelectName"
                    color="blue"
                    persistent-hint
                  >
                  </v-select>
                </v-flex>
                <v-flex md12>
                  <v-text-field
                    box
                    label="Zone Name"
                    v-model="zoneRate.zoneName"
                    type="text"
                    :disabled="!isEdited"
                    :rules="[validate.required]"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md12>
                  <v-text-field
                    box
                    label="Zone Rate"
                    type="number"
                    :prefix="'$'"
                    :disabled="!isEdited"
                    v-model.number="zoneRate.rate"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>

                <v-flex md12>
                  <v-text-field
                    box
                    label="Pickup Flagfall"
                    :prefix="'$'"
                    v-model.number="zoneRate.additionalPickUpFlagFall"
                    type="number"
                    :disabled="!isEdited"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md12>
                  <v-text-field
                    box
                    label="Dropoff Flagfall"
                    :prefix="'$'"
                    type="number"
                    :disabled="!isEdited"
                    v-model.number="zoneRate.additionalDropOffFlagFall"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>
              </v-layout>

              <v-layout v-if="isFleetAsset">
                <v-flex md12>
                  <v-text-field
                    box
                    :label="'Percentage'"
                    type="number"
                    :disabled="!isEdited"
                    :rules="[validate.required, validate.percentage, validate.nonNegative]"
                    v-model.number="zoneRate.percentage"
                  >
                  </v-text-field>
                </v-flex>
              </v-layout>
              <v-layout wrap>
                <v-flex md12>
                  <v-select
                    label="Fuel Surcharge Application"
                    box
                    :items="applicableFuelSurcharges"
                    item-text="shortName"
                    item-value="id"
                    v-model="zoneRate.appliedFuelSurchargeId"
                    :disabled="!isEdited"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-select>
                </v-flex>
                <v-flex md12>
                  <v-layout align-center>
                    <h5 class="subheader--bold pr-3">Demurrage</h5>
                    <v-flex>
                      <v-divider></v-divider>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-select
                    box
                    :items="applicableDemurrages"
                    :disabled="!isEdited"
                    item-text="shortName"
                    label="Demurrage Rate Application"
                    item-value="id"
                    v-model="zoneRate.demurrage.appliedDemurrageId"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-select>
                </v-flex>
                <v-flex md12>
                  <v-text-field
                    box
                    label="Demurrage Rate (per hour)"
                    :prefix="'$'"
                    type="number"
                    :disabled="zoneRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                    v-model.number="zoneRate.demurrage.rate"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md12 v-if="!isFleetAsset">
                  <v-text-field
                    box
                    label="Demurrage Grace Time (mins)"
                    type="number"
                    v-model.number="graceTimeInMinutes"
                    :disabled="zoneRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>

                <v-flex md12>
                  <v-layout class="label-container" justify-end>
                    <h6 class="subheader--faded pr-2" style="padding-top: 2px">
                      Apply fuel surcharge to demurrage duration
                    </h6>
                    <div>
                      <v-switch
                        v-model="zoneRate.demurrage.demurrageFuelSurchargeApplies"
                        :disabled="!isEdited"
                        class="ma-0 pa-0"
                        color="#64ffda"
                      >
                      </v-switch>
                    </div>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-layout>
          </v-form>
        </v-flex>
        <v-flex md12>
          <v-divider></v-divider>
          <v-layout justify-space-between>
            <ConfirmationDialog
              message="You are about to remove this zone rate. Do you wish to proceed?"
              buttonText="Remove"
              title="Zone Rate removal confirmation"
              @confirm="removeRate"
              :buttonDisabled="!isEdited"
              buttonColor="error"
              :isFlatButton="true"
              :isSmallButton="false"
              :isDepressedButton="true"
              confirmationButtonText="Confirm and Remove"
              :dialogIsActive="true"
            ></ConfirmationDialog>
            <v-btn
              color="blue"
              :disabled="!isEdited"
              depressed
              @click="saveZoneRate"
            >
              <span v-if="isNewRate">Add Zone Rate</span>
              <span v-else>Update Zone Rate</span>
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-dialog>
  <!-- <v-dialog
    v-if="zoneRate"
    v-model="dialogIsOpen"
    content-class="v-dialog-custom"
    width="600px"
    persistent
  >
  
    <div class="app-theme__center-content--body dialog-content">
 
    </div>
  </v-dialog> -->
</section>
