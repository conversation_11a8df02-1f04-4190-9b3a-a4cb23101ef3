import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import QuickSelectDateRange from '@/components/common/date-picker/quick_select_date_range.vue';
import FormCard from '@/components/common/ui-elements/form_card.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { DateRangeGap } from '@/interface-models/Generic/DateRangeGap';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { ValidFromRequiredEdit } from '@/interface-models/Generic/ValidRequiredEdit';
import { Validation } from '@/interface-models/Generic/Validation';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    FormCard,
    QuickSelectDateRange,
    DateTimeInputs,
  },
})
export default class ServiceRateDateRangeEdit
  extends Vue
  implements IUserAuthority
{
  @Prop() public serviceRate: ClientServiceRate | FleetAssetServiceRate;
  @Prop() public isEdited: boolean;
  @Prop() public isClient: boolean;
  @Prop() public allServiceRates: ClientServiceRate[] | FleetAssetServiceRate[];
  @Prop({ default: false }) public isFleetAsset: boolean;
  @Prop({ default: false }) public isDefault: boolean;
  @Prop() public dateRangeItems: any;
  @Prop() public newServiceRateSettings: boolean;
  @Prop({ default: false }) public hasServiceRateTable: boolean;
  @Prop() public isViewing: boolean;
  @Prop() public isNew: boolean;
  @Prop() public clientFleetId: string;
  @Prop() public allClientServiceRates: any;
  @Prop() public fleetAssetHasActiveRates: boolean;
  @Prop() public hasCurrentServiceRate: boolean;
  @Prop() public isViewingServiceRate: boolean;
  @Prop() public selectedExistingClientServiceRateList: any[];
  @Prop() public allowEditOnDateRange: boolean;
  @Prop() public clientAssetName: string;
  @Prop() public currentActiveServiceRate:
    | ClientServiceRate
    | FleetAssetServiceRate;

  @Prop({ default: false }) public singlePageLayout: boolean;
  // This is set to true for the new layout where the component captures its own
  @Prop({ default: false }) public handleResponsesLocally: boolean;

  public fleetAssetStore = useFleetAssetStore();
  public serviceRateStore = useServiceRateStore();
  public companyDetailsStore = useCompanyDetailsStore();

  public isAwaitingServiceRateListForId: boolean = false;

  // When copying rates from another FA, this contains the response to the API
  // when handling responses locally
  public serviceRateListForSelectedFleetAsset: FleetAssetServiceRate[] = [];
  public serviceRateListForSelectedClient: ClientServiceRate[] = [];

  public copyFromOtherClients: boolean = false;
  public selectedAssociatedClientAsset: string = '';
  public invalidManualEnter: boolean = false;
  public copyRateEntitySearch: string = '';

  public headers: TableHeader[] = [
    {
      text: 'Name',
      align: 'left',
      sortable: false,
      value: 'name',
    },
    {
      text: 'Outside Metro Rate (%)',
      align: 'left',
      sortable: false,
      value: 'outsideMetroRate',
    },
    {
      text: 'Valid From',
      align: 'left',
      sortable: false,
      value: 'make',
    },
    {
      text: 'Valid To',
      align: 'left',
      sortable: false,
      value: 'model',
    },
  ];

  public $refs!: {
    form: VForm;
  };

  get validate(): Validation {
    return validationRules;
  }

  get associatedEntityList() {
    let entities: any[] = [];
    if (this.isClient) {
      entities = useClientDetailsStore().clientSummaryList.map(
        (client: ClientSearchSummary) => {
          return { id: client.clientId, selectName: client.clientDisplayName };
        },
      );
    } else {
      entities = this.fleetAssetStore.getAllFleetAssetList.map(
        (asset: FleetAssetSummary) => {
          return { id: asset.fleetAssetId, selectName: asset.csrAssignedId };
        },
      );
    }
    return entities;
  }

  public stopViewing() {
    this.cancelNewClientServiceRate();
    this.$emit('setViewingServiceRate', false);
  }

  get earliestServiceRate(): ClientServiceRate | undefined {
    if (
      this.latestServiceRate !== undefined &&
      this.latestServiceRate.validFromDate !== null
    ) {
      let date: number = this.latestServiceRate.validFromDate;
      for (const rate of this.allClientServiceRates) {
        if (rate.validFromDate !== null && rate.validToDate !== null) {
          if (rate.validFromDate < date) {
            date = rate.validFromDate;
          }
        }
      }
      return this.allClientServiceRates.find(
        (x: ClientServiceRate) => x.validFromDate === date,
      );
    }
  }

  get latestServiceRate(): ClientServiceRate | undefined {
    let date: number = 0;
    for (const rate of this.allClientServiceRates) {
      if (rate.validFromDate !== null && rate.validToDate !== null) {
        if (rate.validFromDate > date) {
          date = rate.validFromDate;
        }

        if (rate.validToDate > date) {
          date = rate.validToDate;
        }
      }
    }
    return this.allClientServiceRates.find(
      (rate: ClientServiceRate) => rate.validToDate === date,
    );
  }

  get nextServiceRate(): ClientServiceRate | undefined {
    const foundEditedServiceRate = this.allClientServiceRates.find(
      (rate: ClientServiceRate) =>
        this.serviceRate && rate.tableId === this.serviceRate.tableId,
    );
    if (foundEditedServiceRate !== undefined) {
      const serviceRate = this.allClientServiceRates.find(
        (x: ClientServiceRate) =>
          x.validFromDate! > foundEditedServiceRate.validToDate! &&
          x.validFromDate! < foundEditedServiceRate.validToDate! + 80400000,
      );

      return serviceRate;
    }
  }

  get validToEdits() {
    if (this.serviceRate !== null) {
      let foundEditedServiceRate = this.allClientServiceRates.find(
        (x: ClientServiceRate) => x.tableId === this.serviceRate!.tableId,
      );

      if (this.isNew) {
        foundEditedServiceRate = this.serviceRate;
      }
      const requiredEdits: any = {
        edit: false,
        surchargeId: '',
        validFromValue: 0,
      };

      if (
        this.serviceRate.validToDate &&
        this.nextServiceRate &&
        this.nextServiceRate.validFromDate &&
        foundEditedServiceRate &&
        foundEditedServiceRate.validToDate
      ) {
        if (
          this.serviceRate.validToDate >= this.nextServiceRate.validFromDate ||
          this.serviceRate.validToDate < foundEditedServiceRate.validToDate
        ) {
          requiredEdits.edit = true;
          requiredEdits.tableId = this.nextServiceRate.tableId;
          requiredEdits.validFromValue = moment(this.serviceRate.validToDate)
            .tz(this.companyDetailsStore.userLocale)
            .add(1, 'day')
            .startOf('day')
            .valueOf();
        }
      }
      return requiredEdits;
    }
  }

  get validFromEdits(): ValidFromRequiredEdit | undefined {
    if (this.serviceRate !== null) {
      let foundEditedServiceRate = this.allClientServiceRates.find(
        (x: ClientServiceRate) => x.tableId === this.serviceRate!.tableId,
      );

      if (this.isNew) {
        foundEditedServiceRate = this.serviceRate;
      }
      const requiredEdits: ValidFromRequiredEdit = {
        edit: false,
        tableId: -1,
        validToValue: 0,
      };
      if (
        this.serviceRate.validFromDate &&
        this.previousServiceRate &&
        this.previousServiceRate.validToDate &&
        foundEditedServiceRate &&
        foundEditedServiceRate.validFromDate
      ) {
        if (
          this.serviceRate.validFromDate <=
            this.previousServiceRate.validToDate ||
          this.serviceRate.validFromDate > foundEditedServiceRate.validFromDate
        ) {
          requiredEdits.edit = true;
          requiredEdits.tableId = this.previousServiceRate.tableId;

          requiredEdits.validToValue = moment(this.serviceRate.validFromDate)
            .tz(this.companyDetailsStore.userLocale)
            .subtract(1, 'day')
            .endOf('day')
            .valueOf();
        }
      }
      return requiredEdits;
    }
  }

  get allowedDates() {
    const min = moment
      .tz(this.companyDetailsStore.userLocale)
      .subtract(5, 'year');

    let dates: any = {
      from: {
        min: min.valueOf(),
        max:
          !this.serviceRate || !this.serviceRate.validToDate
            ? undefined
            : moment(this.serviceRate.validToDate)
                .tz(this.companyDetailsStore.userLocale)
                .valueOf(),
      },
      to: {
        min:
          !this.serviceRate || this.serviceRate.validFromDate === null
            ? undefined
            : moment(this.serviceRate.validFromDate)
                .tz(this.companyDetailsStore.userLocale)
                .valueOf(),
        max: undefined,
      },
    };

    if (this.allClientServiceRates.length === 0) {
      return dates;
    }

    if (this.isNew) {
      dates = {
        from: {
          min: this.latestServiceRate
            ? moment(this.latestServiceRate.validFromDate)
                .tz(this.companyDetailsStore.userLocale)
                .add(1, 'day')
                .valueOf()
            : undefined,
          max: !this.serviceRate.validToDate
            ? undefined
            : moment(this.serviceRate.validToDate + 1)
                .tz(this.companyDetailsStore.userLocale)
                .subtract(1, 'day')
                .valueOf(),
        },
        to: {
          min:
            this.serviceRate.validFromDate === null
              ? undefined
              : moment(this.serviceRate.validFromDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .valueOf(),
          max: undefined,
        },
      };
      return dates;
    }

    if (this.allClientServiceRates.length > 1) {
      if (
        this.latestServiceRate &&
        this.serviceRate &&
        this.serviceRate.tableId === this.latestServiceRate.tableId
      ) {
        dates = {
          from: {
            min: this.previousServiceRate
              ? moment(this.previousServiceRate.validFromDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .add(1, 'day')
                  .valueOf()
              : undefined,
            max:
              !this.serviceRate || !this.serviceRate.validToDate
                ? undefined
                : moment(this.serviceRate.validToDate)
                    .tz(this.companyDetailsStore.userLocale)

                    .valueOf(),
          },
          to: {
            min:
              !this.serviceRate || this.serviceRate.validFromDate === null
                ? undefined
                : moment(this.serviceRate.validFromDate)
                    .tz(this.companyDetailsStore.userLocale)
                    .valueOf(),
            max: undefined,
          },
        };
      } else if (
        this.earliestServiceRate &&
        this.serviceRate &&
        this.serviceRate.tableId === this.earliestServiceRate.tableId
      ) {
        dates = {
          from: {
            min: min.valueOf(),
            max: !this.serviceRate.validToDate
              ? undefined
              : moment(this.serviceRate.validToDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .subtract(1, 'day')
                  .valueOf(),
          },
          to: {
            min:
              this.serviceRate.validFromDate === null
                ? undefined
                : moment(this.serviceRate.validFromDate)
                    .tz(this.companyDetailsStore.userLocale)
                    .valueOf(),
            max: this.nextServiceRate
              ? moment(this.nextServiceRate.validToDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .subtract(1, 'day')
                  .valueOf()
              : undefined,
          },
        };
      } else {
        dates = {
          from: {
            min: this.previousServiceRate
              ? moment(this.previousServiceRate.validFromDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .add(1, 'day')
                  .valueOf()
              : undefined,
            max:
              !this.serviceRate || !this.serviceRate.validToDate
                ? this.nextServiceRate
                  ? moment(this.nextServiceRate.validFromDate)
                      .tz(this.companyDetailsStore.userLocale)
                      .subtract(1, 'day')
                      .valueOf()
                  : undefined
                : moment(this.serviceRate.validToDate)
                    .tz(this.companyDetailsStore.userLocale)
                    .valueOf(),
          },
          to: {
            min:
              !this.serviceRate || !this.serviceRate.validFromDate
                ? undefined
                : moment(this.serviceRate.validFromDate)
                    .tz(this.companyDetailsStore.userLocale)
                    .valueOf(),
            max: this.nextServiceRate
              ? moment(this.nextServiceRate.validToDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .subtract(1, 'day')
                  .valueOf()
              : undefined,
          },
        };
      }
    }

    return dates;
  }

  get previousServiceRate(): ClientServiceRate | undefined {
    const foundEditedServiceRate = this.allClientServiceRates.find(
      (rate: ClientServiceRate) =>
        this.serviceRate && rate.tableId === this.serviceRate.tableId,
    );

    if (!foundEditedServiceRate) {
      return this.latestServiceRate;
    }

    return this.allClientServiceRates.find(
      (x: ClientServiceRate) =>
        x.validToDate! < foundEditedServiceRate.validFromDate! &&
        x.validToDate! > foundEditedServiceRate.validFromDate! - 80400000,
    );
  }

  get readableExistingClientsDateRangeItems() {
    const dateRanges: any = [];
    let clientFleetId;

    const rateList = this.handleResponsesLocally
      ? this.isFleetAsset
        ? this.serviceRateListForSelectedFleetAsset
        : this.serviceRateListForSelectedClient
      : this.selectedExistingClientServiceRateList;
    if (rateList?.length > 0) {
      if (this.isClient) {
        clientFleetId = rateList[0].clientId;
      } else if (this.isFleetAsset) {
        clientFleetId = rateList[0].fleetAssetId;
      }
    }
    for (const rates of rateList) {
      const dates = {
        tableId: rates.tableId,
        clientId: clientFleetId,
        fleetAssetId: clientFleetId,
        validFromDate: returnFormattedDate(rates.validFromDate),
        validToDate: returnFormattedDate(rates.validToDate),
      };
      dateRanges.push(dates);
    }
    return dateRanges;
  }

  public returnReadableDate(epoch: number): string {
    return returnFormattedDate(epoch, 'DD/MM/YY');
  }

  get associatedServiceRatesLabel(): string {
    return this.isClient
      ? 'Please select an existing Client to view their Service Rates'
      : this.isFleetAsset
        ? 'Please select Fleet Asset to load their Service Rates'
        : '';
  }

  get timezone(): string {
    return this.companyDetailsStore.userLocale;
  }
  // Returns current validFromDate from serviceRate. Used as v-model for
  // DateTimeInput. Perform some additional logic when setting.
  get validFromDate(): number | null {
    return this.serviceRate.validFromDate;
  }
  set validFromDate(epoch: number | null) {
    if (epoch === null) {
      return;
    }
    this.serviceRate.validFromDate = epoch;
    this.setValidFromDate(epoch);
  }
  // Returns current validFromDate from serviceRate. Used as v-model for
  // DateTimeInput. Perform some additional logic when setting.
  get validToDate(): number | null {
    return this.serviceRate.validToDate;
  }
  set validToDate(epoch: number | null) {
    if (epoch === null) {
      return;
    }
    this.serviceRate.validToDate = epoch;
    this.setValidToDate(epoch);
  }
  // Validates the incoming epoch time against the allowedDates, and either
  // update serviceRate.validFromDate with the value or or return some warning
  public setValidFromDate(startOfDay: number): void {
    // If the selected validFromDate is higher than the allowed maximum value,
    // set value to null and show warning
    if (
      this.allowedDates.from.max !== undefined &&
      startOfDay > returnEndOfDayFromEpoch(this.allowedDates.from.max)
    ) {
      this.showWarning();
    }
    if (this.serviceRate.validFromDate) {
      if (startOfDay < returnStartOfDayFromEpoch(this.allowedDates.from.min)) {
        this.showWarning();
      }
    }
  }
  // Validates the incoming epoch time against the allowedDates, and either
  // update serviceRate.validToDate with the value or or return some warning
  // and set serviceRate.validToDate to null
  public setValidToDate(endOfDay: number): void {
    // Check if value is higher than max allowed date. If so, set to null
    // and show warning. Otherwise we set
    if (
      this.allowedDates.to.max !== undefined &&
      endOfDay > returnEndOfDayFromEpoch(this.allowedDates.to.max)
    ) {
      this.showWarning();
    }

    // Check if incoming value is before the min allowed date. If so, set to
    // null and show warning. Otherwise we can set
    if (
      this.serviceRate.validToDate !== null &&
      endOfDay < returnStartOfDayFromEpoch(this.allowedDates.to.min)
    ) {
      this.showWarning();
    }
  }
  // Sets boolean to true which controls v-alert in html. Set a timeout to hide
  // alert again after 3 seconds.
  public showWarning() {
    this.invalidManualEnter = true;
    setTimeout(() => {
      this.invalidManualEnter = false;
    }, 3000);
  }

  public setQuickValidToDate(epoch: number): void {
    this.serviceRate.validToDate = epoch;
  }

  public getServiceRateById(tableId: number) {
    let payload: string[] = [this.clientFleetId, tableId.toString()];

    if (this.isDefault) {
      payload = [this.clientFleetId, tableId.toString()];
    }

    if (this.isFleetAsset) {
      this.$emit('getServiceRateByTableId', payload);
    } else {
      this.$emit('getServiceRateByTableId', payload);
    }
  }

  get copyRatesLabel() {
    if (this.newServiceRateSettings) {
      if (this.isClient) {
        return (
          'All ' +
          this.clientAssetName +
          ' rates. Please select the rates that you wish to copy across.'
        );
      } else if (this.isFleetAsset) {
        return (
          ' Select a Service Rate from ' +
          this.clientAssetName +
          ' to copy rates across to the new Service Rate'
        );
      }
    } else {
      if (this.isClient) {
        return 'Clients Existing Service Rates List';
      }
      if (this.isFleetAsset) {
        return 'Fleet Assets Existing Service Rates List';
      }
    }
  }

  /**
   * Handles selection in autocomplete component in template, when we select a
   * rate table that we want to copy from.
   * @param value - The selected rate table
   */
  public async viewExistingClientSelectedDateRange(value: any) {
    // Initialize serviceRate as null
    let serviceRate: FleetAssetServiceRate | ClientServiceRate | null = null;

    // Prepare payload based on whether it's a client or fleet asset
    const payload = this.isClient
      ? [value.clientId, value.tableId]
      : [value.fleetAssetId, value.tableId];

    // If it's a client or we're handling responses locally for a fleet asset, set flag and fetch service rate
    if (this.isClient || (this.isFleetAsset && this.handleResponsesLocally)) {
      serviceRate = this.isClient
        ? await this.serviceRateStore.getClientServiceRatesByTableId(
            payload[0],
            payload[1],
          )
        : await this.serviceRateStore.getFleetAssetServiceRateByTableId(
            payload[0],
            payload[1],
          );
    } else if (this.isFleetAsset) {
      // If it's a fleet asset and we're not handling responses locally, emit event with payload
      this.$emit('getServiceRateByTableId', payload);
    }

    // If we have a service rate, set rate table items and emit event with service rate
    if (serviceRate) {
      this.serviceRate.rateTableItems = serviceRate.rateTableItems;
      this.$emit('copyFromIncomingServiceRate', serviceRate);
    }
    // If we're handling responses locally and serviceRate is null, then show error
    else if (this.handleResponsesLocally) {
      showNotification(
        'Something went wrong. Please try another Service Rate.',
        { title: 'Service Rate' },
      );
    }
  }

  /**
   * Selects the associated client or fleet asset and fetches the corresponding
   * service rates.
   * @param {string} value - The ID of the selected client or fleet asset.
   */
  public async selectAssociatedClientAsset(value: string) {
    // Initialize serviceRateList as null
    let serviceRateList: FleetAssetServiceRate[] | ClientServiceRate[] | null =
      null;

    // Set the loading state to true
    this.isAwaitingServiceRateListForId = true;

    // If the selected entity is a client
    if (this.isClient) {
      serviceRateList =
        await this.serviceRateStore.getAllServiceRatesForClientId(value);

      // If service rates exist, assign them to the corresponding data property
      if (serviceRateList && serviceRateList.length > 0) {
        this.serviceRateListForSelectedClient = serviceRateList;
      } else {
        // If no service rates found, show a notification
        this.showNotificationForMissingServiceRates();
      }
    }
    // If the selected entity is a fleet asset fetch rates
    else if (this.isFleetAsset && this.handleResponsesLocally) {
      serviceRateList =
        await this.serviceRateStore.getAllServiceRatesForFleetAssetId(value);

      // If service rates exist, assign them to the corresponding data property
      if (serviceRateList && serviceRateList.length > 0) {
        this.serviceRateListForSelectedFleetAsset = serviceRateList;
      } else {
        // If no service rates found, show a notification
        this.showNotificationForMissingServiceRates();
      }
    }
    this.isAwaitingServiceRateListForId = false;
  }

  /**
   * Shows a notification when no service rates are found for the selected client or fleet asset.
   */
  private showNotificationForMissingServiceRates() {
    showNotification(
      `Could not find Service Rates for the selected ${
        this.isFleetAsset ? 'Fleet Asset' : 'Client'
      }`,
      { title: 'Service Rate' },
    );
  }

  public setNewServiceRate(): void {
    this.$emit('setNewClientServiceRate');
  }

  public cancelNewClientServiceRate(): void {
    this.copyFromOtherClients = false;
    this.$emit('cancelNewClientServiceRate');
  }

  public editSettings(): void {
    this.$emit('editSettings');
  }

  get gapExists(): DateRangeGap {
    const filteredRatesList = this.allClientServiceRates.filter(
      (rate: ClientServiceRate) => rate.tableId !== this.serviceRate.tableId,
    );
    const gapExists: DateRangeGap = {
      value: false,
      table: null,
    };
    for (const rate of filteredRatesList) {
      const selectedDate = this.serviceRate.validToDate;

      const difference = rate.validFromDate! - selectedDate!;
      const check = Math.sign(difference);

      if (check === -1) {
        continue;
      } else {
        if (difference > 1000) {
          gapExists.value = true;
          gapExists.table = rate;
        }
      }
    }
    return gapExists;
  }

  get dateOverlaps(): boolean {
    const filteredRatesList = this.allClientServiceRates.filter(
      (rate: any) => rate.tableId !== this.serviceRate.tableId,
    );
    let overlapExists = false;
    for (const rate of filteredRatesList) {
      if (
        this.serviceRate.validToDate! >= rate.validFromDate! &&
        this.serviceRate.validToDate! < rate.validToDate!
      ) {
        overlapExists = true;
      }
    }

    return overlapExists;
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  public mounted(): void {}
}
