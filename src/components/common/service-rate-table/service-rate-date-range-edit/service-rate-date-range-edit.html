<v-layout wrap id="service-rate-date-range">
  <v-flex md12 v-if="!singlePageLayout">
    <v-layout class="mb-1" v-if="!isViewingServiceRate && !isNew && !isEdited">
      <v-flex md12>
        <v-alert
          :value="true"
          color="rgba(52, 228, 122, 0.34)"
          icon="fad fa-briefcase"
        >
          <v-layout align-center>
            <span v-if="isFleetAsset">
              <span v-if="!fleetAssetHasActiveRates">
                Fleet Asset has no current active rates. Please create
                some.</span
              >
              <span v-if="fleetAssetHasActiveRates">
                Fleet Asset is currently on rate table
                <span style="font-weight: 600">
                  {{currentActiveServiceRate.tableId}}</span
                >
                as
                <span style="font-weight: 600">
                  {{currentActiveServiceRate.name}}</span
                >
                valid from
                {{returnReadableDate(currentActiveServiceRate.validFromDate)}}
                to
                {{returnReadableDate(currentActiveServiceRate.validToDate)}}.</span
              >
            </span>
            <span v-if="isClient && !isDefault"
              >Client is currently on
              <span v-if="currentActiveServiceRate === null"
                >Default Rates. Information can be found above in Default Rates
                Configuration</span
              >
              <span v-else>custom rates.</span>
            </span>

            <span
              v-if="isDefault"
              :class="currentActiveServiceRate === null ? 'no-active-rate-txt' : ''"
            >
              <v-icon
                v-if="currentActiveServiceRate === null"
                color="error"
                class="pr-1"
                size="20"
                >warning</v-icon
              >
              <span class="pr-1" v-if="currentActiveServiceRate !== null">
                {{clientAssetName}} currently has active rates.
              </span>
              <span v-else>
                There are no division/global rates set for {{clientAssetName}}.
                Please create some if you would like to use Rate Merging
                feature.</span
              >
            </span>
            <span
              v-if="currentActiveServiceRate !== null && !isNew && isClient"
              :class="!isDefault ? 'pl-1' : ''"
            >
              <span
                style="
                  color: var(--warning);
                  font-weight: 600;
                  text-transform: capitalize;
                "
              >
                {{currentActiveServiceRate.name}}</span
              >
              valid from
              {{returnReadableDate(currentActiveServiceRate.validFromDate)}} to
              {{returnReadableDate(currentActiveServiceRate.validToDate)}}</span
            >
          </v-layout>
        </v-alert>
      </v-flex>
    </v-layout>
    <v-card
      flat
      color="#2e2e2e"
      v-if="!isViewingServiceRate && !isNew && !isEdited"
      :class="{'mb-4': isDefault}"
    >
      <v-data-table
        :headers="headers"
        :items="allServiceRates"
        hide-actions
        class="gd-dark-theme"
        no-data-text="No history of custom Service Rates available."
      >
        <template v-slot:items="props">
          <tr
            @click="getServiceRateById(props.item.tableId)"
            :class="{activeSelectedRate: currentActiveServiceRate !== null && props.item.tableId === currentActiveServiceRate.tableId}"
            style="cursor: pointer"
          >
            <td>{{ props.item.name}}</td>
            <td>{{ props.item.outsideMetroRate}}</td>
            <td>{{ returnReadableDate(props.item.validFromDate)}}</td>
            <td>{{ returnReadableDate(props.item.validToDate)}}</td>
          </tr>
        </template>
      </v-data-table>
    </v-card>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12>
          <v-layout v-if="newServiceRateSettings && !isDefault">
            <v-switch
              v-model="copyFromOtherClients"
              label="Copy from Existing Rates"
            >
            </v-switch>
          </v-layout>
          <v-autocomplete
            v-if="newServiceRateSettings && !copyFromOtherClients"
            :label="copyRatesLabel"
            :value="serviceRate.tableId"
            :items="allClientServiceRates"
            @change="viewExistingClientSelectedDateRange"
            item-value="tableId"
            return-object
            outline
            class="v-solo-custom"
            hint="Please select a Service Rate to View"
            hide-details
          >
            <template slot="selection" slot-scope="data">
              {{ data.item.name ? data.item.name : '' }} - {{
              returnReadableDate(data.item.validFromDate) }} - {{
              returnReadableDate(data.item.validToDate) }}
            </template>
            <template slot="item" slot-scope="data">
              {{ data.item.name ? data.item.name : '' }} - {{
              returnReadableDate(data.item.validFromDate) }} - {{
              returnReadableDate(data.item.validToDate) }}
            </template>
          </v-autocomplete>
          <v-layout v-if="newServiceRateSettings && copyFromOtherClients">
            <v-layout wrap v-if="!isDefault && copyFromOtherClients">
              <v-flex md12>
                <v-autocomplete
                  :label="associatedServiceRatesLabel"
                  :items="associatedEntityList"
                  @change="selectAssociatedClientAsset"
                  :disabled="!isEdited"
                  v-model="selectedAssociatedClientAsset"
                  hint="Rates will appear below if they exist"
                  color="orange"
                  persistent-hint
                  outline
                  item-value="id"
                  item-text="selectName"
                  class="v-solo-custom"
                  :search-input.sync="copyRateEntitySearch"
                >
                </v-autocomplete>
              </v-flex>
              <v-flex md12>
                <v-autocomplete
                  v-if="readableExistingClientsDateRangeItems.length > 0"
                  label="Select Rate To Copy Across"
                  :items="readableExistingClientsDateRangeItems"
                  @change="viewExistingClientSelectedDateRange"
                  outline
                  hint="Please select a Service Rate to View"
                  persistent-hint
                  class="v-solo-custom"
                >
                  <template slot="selection" slot-scope="data">
                    Table ID: {{ data.item.tableId }} - {{
                    data.item.validFromDate }} {{ data.item.validToDate }}
                  </template>
                  <template slot="item" slot-scope="data">
                    Table ID: {{ data.item.tableId }} - {{
                    data.item.validFromDate }} {{ data.item.validToDate }}
                  </template>
                </v-autocomplete>
              </v-flex>
            </v-layout>
          </v-layout>
        </v-flex>

        <v-flex
          md12
          v-if="isEdited || newServiceRateSettings || isViewingServiceRate || isDefault"
        >
          <v-text-field
            box
            label="Rate Table Name: "
            :disabled="!isEdited"
            v-model="serviceRate.name"
            :rules="[validate.required]"
          />
        </v-flex>

        <v-flex
          md12
          v-if="isEdited || newServiceRateSettings || isViewingServiceRate || isDefault"
        >
          <v-text-field
            box
            label="Outside Metro Area Surcharge (%)"
            persistent-hint
            type="number"
            :disabled="!isEdited"
            :rules="[validate.required, validate.number]"
            v-model.number="serviceRate.outsideMetroRate"
          />
        </v-flex>
        <v-flex
          md6
          class="pr"
          v-if="isEdited || newServiceRateSettings || isViewingServiceRate || isDefault"
        >
          <DateTimeInputs
            :epochTime.sync="validFromDate"
            :enableValidation="true"
            type="DATE_START_OF_DAY"
            dateLabel="Valid From"
            hintTextType="FORMATTED_SELECTION"
            :isRequired="true"
            :readOnly="!isEdited"
            :minimumEpochTime="allowedDates.from.min"
            :maximumEpochTime="allowedDates.from.max"
            maxComparisonType="LESS_OR_EQUAL"
          ></DateTimeInputs>
        </v-flex>
        <v-flex
          md6
          class="pl"
          style="position: relative"
          v-if="isEdited || newServiceRateSettings || isViewingServiceRate || isDefault"
        >
          <DateTimeInputs
            :epochTime.sync="validToDate"
            :enableValidation="true"
            type="DATE_END_OF_DAY"
            dateLabel="Valid To"
            hintTextType="FORMATTED_SELECTION"
            :isRequired="true"
            :readOnly="!isEdited"
            :minimumEpochTime="allowedDates.to.min"
            :maximumEpochTime="allowedDates.to.max"
          ></DateTimeInputs>
        </v-flex>
        <v-flex v-if="validFromEdits && validToEdits">
          <v-alert
            :value="validFromEdits.edit || validToEdits.edit"
            type="error"
          >
            Date overlaps with another service rates date range.
            <span v-if="validFromEdits.edit">
              Table id {{validFromEdits.tableId}}</span
            >
            <span v-if="validFromEdits.edit && validToEdits.edit"> & </span>
            <span v-if="validToEdits.edit">
              Table id {{validToEdits.tableId}}</span
            >
            will be automatically updated so no overlaps occur.
          </v-alert>
        </v-flex>

        <v-flex md12 v-if="invalidManualEnter">
          <v-icon size="14" color="error" class="pr">fad fa-exclamation</v-icon>
          The date you entered overlaps with another Fuel Surcharge.
        </v-flex>
      </v-layout>
    </v-form>
  </v-flex>
  <v-flex md12 v-if="singlePageLayout">
    <v-layout v-if="!isDefault" class="mb-3" row wrap>
      <v-flex md12>
        <v-layout>
          <v-btn
            v-if="isNew || isEdited"
            outline
            color="red"
            @click="stopViewing"
          >
            Cancel
          </v-btn>
          <v-btn
            @click="stopViewing"
            v-if="isViewingServiceRate && !isEdited"
            outline
            color="red"
          >
            <v-icon size="13" class="pr-2">fas fa-chevron-left</v-icon>Go Back
          </v-btn>
          <v-btn
            outline
            color="white"
            @click="editSettings"
            v-if="!isEdited  && serviceRate !== null && isViewingServiceRate"
            :disabled="!isAuthorised()"
          >
            Edit Rates
          </v-btn>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="!isNew && !isEdited && !isViewingServiceRate">
        <v-layout justify-end>
          <v-btn
            depressed
            color="blue"
            @click="setNewServiceRate"
            :disabled="!isAuthorised()"
          >
            Create New Rates
          </v-btn>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="!isNew && !isEdited && !isViewingServiceRate">
        <slot name="active-rates-summary"></slot>
      </v-flex>
    </v-layout>

    <v-card
      flat
      color="#2e2e2e"
      v-if="!isViewingServiceRate && !isNew && !isEdited"
      :class="{'mb-4': isDefault}"
    >
      <v-data-table
        :headers="headers"
        :items="allServiceRates"
        hide-actions
        class="gd-dark-theme"
        item-key="tableId"
      >
        <template v-slot:items="props">
          <tr
            @click="getServiceRateById(props.item.tableId)"
            :class="{activeSelectedRate: currentActiveServiceRate !== null && props.item.tableId === currentActiveServiceRate.tableId}"
            style="cursor: pointer"
            :key="props.item.tableId"
          >
            <td>{{ props.item.name}}</td>
            <td>{{ props.item.outsideMetroRate}}</td>
            <td>{{ returnReadableDate(props.item.validFromDate)}}</td>
            <td>{{ returnReadableDate(props.item.validToDate)}}</td>
          </tr>
        </template>
      </v-data-table>
    </v-card>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12>
          <v-layout v-if="newServiceRateSettings && !isDefault">
            <v-switch
              v-model="copyFromOtherClients"
              label="Copy from Existing Rates"
            >
            </v-switch>
          </v-layout>
          <v-autocomplete
            v-if="newServiceRateSettings && !copyFromOtherClients"
            :label="copyRatesLabel"
            :value="serviceRate.tableId"
            :items="allClientServiceRates"
            @change="getServiceRateById"
            item-value="tableId"
            outline
            hint="Please select Service Rate to View"
            persistent-hint
            class="v-solo-custom"
          >
            <template slot="selection" slot-scope="data">
              {{ data.item.name ? data.item.name : '' }} - {{
              returnReadableDate(data.item.validFromDate) }} - {{
              returnReadableDate(data.item.validToDate) }}
            </template>
            <template slot="item" slot-scope="data">
              {{ data.item.name ? data.item.name : '' }} - {{
              returnReadableDate(data.item.validFromDate) }} - {{
              returnReadableDate(data.item.validToDate) }}
            </template>
          </v-autocomplete>
          <v-layout v-if="newServiceRateSettings && copyFromOtherClients">
            <v-layout wrap v-if="!isDefault && copyFromOtherClients">
              <v-flex md12>
                <v-autocomplete
                  :label="associatedServiceRatesLabel"
                  :items="associatedEntityList"
                  @change="selectAssociatedClientAsset"
                  :disabled="!isEdited"
                  v-model="selectedAssociatedClientAsset"
                  hint="Rates will appear below if they exist"
                  persistent-hint
                  outline
                  item-value="id"
                  item-text="selectName"
                  :search-input.sync="copyRateEntitySearch"
                  class="v-solo-custom"
                >
                </v-autocomplete>
              </v-flex>
              <v-flex
                md12
                v-if="readableExistingClientsDateRangeItems.length > 0"
              >
                <v-autocomplete
                  label="Select Rate To Copy Across"
                  :items="readableExistingClientsDateRangeItems"
                  @change="viewExistingClientSelectedDateRange"
                  outline
                  hint="Please select Service Rate to View"
                  persistent-hint
                  class="v-solo-custom"
                >
                  <template slot="selection" slot-scope="data">
                    Table ID: {{ data.item.tableId }} - {{
                    data.item.validFromDate }} to {{ data.item.validToDate }}
                  </template>
                  <template slot="item" slot-scope="data">
                    Table ID: {{ data.item.tableId }} - valid {{
                    data.item.validFromDate }} to {{ data.item.validToDate }}
                  </template>
                </v-autocomplete>
              </v-flex>
            </v-layout>
          </v-layout>
        </v-flex>

        <v-flex
          md12
          v-if="isEdited || newServiceRateSettings || isViewingServiceRate || isDefault"
        >
          <v-layout>
            <v-flex md5>
              <v-layout align-center class="form-field-label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Rate Table Name:
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md7>
              <v-text-field
                :class="!isEdited ? 'solo-input-disable-display' : ''"
                class="v-solo-custom"
                solo
                flat
                label="Rate Table Name"
                color="light-blue"
                :disabled="!isEdited"
                v-model="serviceRate.name"
                :rules="[validate.required]"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex
          md12
          v-if="isEdited || newServiceRateSettings || isViewingServiceRate || isDefault"
        >
          <v-layout>
            <v-flex md5>
              <v-layout align-center class="form-field-label-container">
                <h6 class="subheader--faded pr-3 pb-0">
                  Outside Metro Area Surcharge (%):
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md7>
              <v-text-field
                :class="!isEdited ? 'solo-input-disable-display' : ''"
                class="v-solo-custom"
                solo
                flat
                label="Outside Metro Area Surcharge (%)"
                persistent-hint
                color="light-blue"
                type="number"
                :disabled="!isEdited"
                :rules="[validate.required, validate.percentage, validate.nonNegative]"
                v-model.number="serviceRate.outsideMetroRate"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex
          md12
          v-if="isEdited || newServiceRateSettings || isViewingServiceRate || isDefault"
        >
          <v-layout>
            <v-flex md5>
              <v-layout align-center class="form-field-label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Valid From:
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md7>
              <DateTimeInputs
                :epochTime.sync="validFromDate"
                :enableValidation="true"
                type="DATE_START_OF_DAY"
                dateLabel="Valid From"
                hintTextType="FORMATTED_SELECTION"
                :isRequired="true"
                :soloInput="true"
                :boxInput="false"
                :readOnly="!isEdited"
                :minimumEpochTime="allowedDates.from.min"
                :maximumEpochTime="allowedDates.from.max"
              ></DateTimeInputs>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex
          md12
          v-if="isEdited || newServiceRateSettings || isViewingServiceRate || isDefault"
        >
          <v-layout>
            <v-flex md5>
              <v-layout align-center class="form-field-label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Valid To:
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md7>
              <DateTimeInputs
                :epochTime.sync="validToDate"
                :enableValidation="true"
                type="DATE_END_OF_DAY"
                dateLabel="Valid To"
                hintTextType="FORMATTED_SELECTION"
                :isRequired="true"
                :soloInput="true"
                :boxInput="false"
                :readOnly="!isEdited"
                :minimumEpochTime="allowedDates.to.min"
                :maximumEpochTime="allowedDates.to.max"
              ></DateTimeInputs>
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex v-if="validFromEdits && validToEdits">
          <v-alert
            :value="validFromEdits.edit || validToEdits.edit"
            type="error"
          >
            Date overlaps with another service rates date range.
            <span v-if="validFromEdits.edit">
              Table id {{validFromEdits.tableId}}</span
            >
            <span v-if="validFromEdits.edit && validToEdits.edit"> & </span>
            <span v-if="validToEdits.edit">
              Table id {{validToEdits.tableId}}</span
            >
            will be automatically updated so no overlaps occur.
          </v-alert>
        </v-flex>

        <v-flex md12 v-if="invalidManualEnter">
          <v-icon size="14" color="error" class="pr">fad fa-exclamation</v-icon>
          The Date you manually entered overlaps with another fuel surcharge.
        </v-flex>
      </v-layout>
    </v-form>
  </v-flex>
</v-layout>
