<div class="job-progress-alert-history">
  <v-layout class="filter-result-container">
    <v-flex md12>
      <v-divider />
      <v-text-field
        class="ma-0"
        hide-details
        flat
        solo
        v-model="search"
        placeholder="Filter Results..."
      />
    </v-flex>
  </v-layout>

  <v-divider />

  <v-layout>
    <v-flex md12>
      <div class="table-content restrict-height">
        <div
          class="table-scrollable"
          id="job-progress-alert-scrollable-section"
        >
          <div class="job-list-container app-theme__center-content--body">
            <div class="message-contents">
              <v-layout
                v-if="filteredResults.length === 0"
                class="pa-3"
                justify-center
                style="color: #b2b0c3"
                >No conversations found...
              </v-layout>
              <v-layout
                column
                v-for="(jobAlert, index) in filteredResults"
                justify-start
                :key="jobAlert._id"
                class="message-contents__row"
              >
                <v-flex md12 class="chat-bubble__container">
                  <v-layout align-center>
                    <span class="chat-bubble__time">
                      {{returnFormattedTime(jobAlert.epochTime)}}
                    </span>
                    <span class="chat-bubble">
                      <v-layout row nowrap>
                        <span class="chat-bubble__text"
                          >{{ jobAlert.alertMessage }}</span
                        >
                      </v-layout>
                    </span>
                  </v-layout>
                </v-flex>
              </v-layout>
            </div>
          </div>
        </div>
      </div>
    </v-flex>
  </v-layout>
</div>
