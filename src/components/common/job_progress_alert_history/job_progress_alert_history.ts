import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import JobProgressAlert from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlert';
import JobProgressAlertHistoryRequest from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertHistoryRequest';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: { DatePickerBasic },
})
export default class JobProgressAlertHistory extends Vue {
  @Prop({ default: '' }) public driverId: string;

  public returnFormattedDate = returnFormattedDate;
  public returnFormattedTime = returnFormattedTime;

  public search: string = '';

  public jobProgressAlertList: JobProgressAlert[] = [];

  get allDrivers(): DriverDetailsSummary[] {
    return useDriverDetailsStore().getDriverList;
  }

  // Dispatch request with start and end range as today
  public async requestJobProgressAlertHistory() {
    const request: JobProgressAlertHistoryRequest = {
      startEpoch: returnStartOfDayFromEpoch(),
      endEpoch: returnEndOfDayFromEpoch(),
    };
    // Get job progress alert history and set to jobProgressAlertList
    const results =
      await useDriverMessageStore().searchJobProgressAlertHistory(request);
    if (results?.length) {
      this.jobProgressAlertList = results;
    } else {
      // Display error if null or no results
      showNotification(GENERIC_ERROR_MESSAGE);
    }
    this.scrollToBottom();
  }

  // Filter results based on search string value
  // This array is used in the v-for loop in the html
  get filteredResults(): JobProgressAlert[] {
    const notFiltered: JobProgressAlert[] = JSON.parse(
      JSON.stringify(this.jobProgressAlertList),
    );
    return notFiltered.filter((x: JobProgressAlert) => {
      const content = x.alertMessage.toLowerCase();
      const found = content.includes(this.search.toLowerCase());
      return found;
    });
  }

  // Scroll to bottom once data response has been received and rendered
  public scrollToBottom() {
    setTimeout(() => {
      const element = document.getElementById(
        'job-progress-alert-scrollable-section',
      );
      if (element) {
        element.scrollTop = element.scrollHeight;
      }
    }, 50);
  }

  public mounted() {
    this.requestJobProgressAlertHistory();
  }
}
