import { Component, Vue } from 'vue-property-decorator';

import { useRootStore } from '@/store/modules/RootStore';
import { returnFormattedCurrentDateAndTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { useGpsStore } from '@/store/modules/GpsStore';
import Mitt from '@/utils/mitt';
@Component({})
export default class DateTimeDisplay extends Vue {
  public rootStore = useRootStore();
  public gpsStore = useGpsStore();

  public timeInterval: ReturnType<typeof setInterval>;
  public perMinuteTimer: ReturnType<typeof setInterval>;
  public per30SecondTimer: ReturnType<typeof setInterval>;

  public date: string = '';
  public time: string = '';

  public setDateTime() {
    const dateTime: [string, string] = returnFormattedCurrentDateAndTime();
    this.date = dateTime[0];
    this.time = dateTime[1];
  }

  public beforeMount() {
    this.setDateTime();
    this.timeInterval = setInterval(this.setDateTime, 1000);

    this.perMinuteTimer = setInterval(() => {
      Mitt.emit('perMinuteTrigger', true);
    }, 60000);
    this.per30SecondTimer = setInterval(() => {
      this.gpsStore.processGpsQueue();
    }, 30000);
  }

  public beforeDestroy() {
    clearInterval(this.timeInterval);
    clearInterval(this.perMinuteTimer);
    clearInterval(this.per30SecondTimer);
  }
}
