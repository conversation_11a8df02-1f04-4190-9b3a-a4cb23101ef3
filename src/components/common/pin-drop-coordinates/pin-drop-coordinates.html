<section>
  <v-layout v-if="!enterAddress">
    <v-select
      v-model="selectedAddress"
      @change="selectAddress"
      :label="returnedAddressSearchList.length === 0 ? 'Please Drop A Pin On The Map' : 'Select Address'"
      :box="!soloInput"
      :solo="soloInput"
      :flat="soloInput"
      :disabled="isDisabled"
      :items="returnedAddressSearchList"
      item-text="properties.label"
      hint="Once a pin is dropped on the map a list of known address will populate the select above"
      return-object
      persistent-hint
      outline
      class="v-solo-custom"
    >
      <v-list-tile
        slot="prepend-item"
        :disabled="selectedLat === 0 || selectedLng === 0"
        @click="enterAddressManually"
      >
        <v-list-tile-content>
          <v-list-tile-title
            ><strong
              >Use pins location and enter address details manually</strong
            ></v-list-tile-title
          >
        </v-list-tile-content>
      </v-list-tile>
    </v-select>
  </v-layout>
  <v-layout>
    <div v-show="!enterAddress" id="pin-drop-map"></div>
  </v-layout>
  <v-form ref="pinManualAddressRef" v-if="enterAddress">
    <v-layout class="mb-2" row wrap>
      <v-flex xs12>
        <v-text-field
          v-model="address.addressLine1"
          label="Address Line 1"
          class="form-field-required"
          :rules="[validate.required]"
          color="orange"
        ></v-text-field>
        <v-text-field
          v-model="address.addressLine2"
          label="Address Line 2"
          color="orange"
        ></v-text-field>
        <v-text-field
          v-model="address.addressLine3"
          label="Address Line 3"
          color="orange"
        ></v-text-field>
        <v-text-field
          v-model="address.addressLine4"
          label="Address Line 4"
          color="orange"
        ></v-text-field>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-text-field
              v-model="address.suburb"
              label="Suburb"
              class="form-field-required"
              :rules="[validate.required]"
              color="orange"
            ></v-text-field>
          </v-flex>
          <v-flex px-2 md4>
            <v-select
              :items="selectedCountry ? selectedCountry.states : []"
              v-model="address.state"
              item-text="iso"
              item-value="iso"
              label="State"
              class="form-field-required"
              :rules="[validate.required]"
              color="orange"
            ></v-select>
          </v-flex>
          <v-flex md4>
            <v-text-field
              v-model="address.postcode"
              label="Postcode"
              class="form-field-required"
              :rules="[validate.required]"
              color="orange"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-select
          :items="countries"
          v-model="address.country"
          item-text="name"
          item-value="name"
          label="Country"
          class="form-field-required"
          :rules="[validate.required]"
          color="orange"
        >
        </v-select>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md6 pr-1
            ><v-text-field
              v-model="address.geoLocation[1]"
              label="Latitude"
              class="form-field-required"
              :rules="[validate.required, validate.latitude]"
              color="orange"
          /></v-flex>
          <v-flex md6
            ><v-text-field
              v-model="address.geoLocation[0]"
              label="Longitude"
              class="form-field-required"
              :rules="[validate.required, validate.longitude]"
              color="orange"
          /></v-flex>
        </v-layout>
      </v-flex>
      <v-flex xs12>
        <v-layout justify-end>
          <v-btn flat small color="red" @click="cancelManualAddress"
            >Cancel</v-btn
          >
          <v-btn depressed small color="blue" @click="saveManualAddress"
            >Add Address</v-btn
          >
        </v-layout>
      </v-flex>
      <v-flex md12 pt-2><v-divider></v-divider></v-flex>
    </v-layout>
  </v-form>
</section>
