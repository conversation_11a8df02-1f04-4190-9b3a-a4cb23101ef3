<section class="chat-alert-dialog">
  <v-layout
    v-if="jobCount"
    class="button app-bgcolor-400"
    @click="isViewingDialog = true"
    align-center
  >
    <span class="button-count">{{jobCount}}</span>
    <span class="button-text">{{buttonTitle}}</span>
  </v-layout>

  <ContentDialog
    :showDialog.sync="isViewingDialog"
    :title="dialogTitle"
    width="80%"
    contentPadding="pa-0"
    @cancel="isViewingDialog = false"
    :showActions="false"
  >
    <v-layout align-center class="banner-custom pa-3">
      <v-layout column>
        <h3>{{dialogTitle}}</h3>
        <h4>
          Viewing {{jobCount}} jobs flagged as
          {{returnProgressAlertTypeMessage(jobProgressAlertType)}}
        </h4>
      </v-layout>
      <span class="tags-chip">{{jobProgressAlertType.replace(/_/g, ' ')}}</span>
    </v-layout>
    <v-divider></v-divider>
    <v-layout>
      <v-flex md12 class="body-scrollable--75 pa-3">
        <table class="simple-data-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Time</th>
              <th>Job #</th>
              <th>Fleet #</th>
              <th>Driver</th>
              <th>Contact</th>
              <th>Message</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in jobProgressAlertList" :key="item._id">
              <td>{{item.date}}</td>
              <td>{{item.time}}</td>
              <td>{{item.displayId}}</td>
              <td>{{item.csrAssignedId}}</td>
              <td>{{item.driverName}}</td>
              <td>{{item.driverMobile}}</td>
              <td>{{item.alertMessage}}</td>
            </tr>
          </tbody>
        </table>
      </v-flex>
    </v-layout>
  </ContentDialog>
</section>
