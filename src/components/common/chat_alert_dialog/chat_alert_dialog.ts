import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  returnFormattedDate,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import JobProgressAlert from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlert';
import {
  JobProgressAlertType,
  returnProgressAlertTypeMessage,
} from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertType';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface JobProgressAlertDetails extends JobProgressAlert {
  date: string;
  time: string;
  displayId: string;
  csrAssignedId: string;
  driverName: string;
  driverMobile: string;
}
@Component({
  components: {
    ContentDialog,
  },
})
export default class ChatAlertDialog extends Vue {
  @Prop() public jobProgressAlertType: JobProgressAlertType;

  public driverMessageStore = useDriverMessageStore();

  public returnProgressAlertTypeMessage = returnProgressAlertTypeMessage;

  public dialogIsOpen: boolean = false;

  public dialogTitle = '';
  public buttonTitle = '';

  get isViewingDialog(): boolean {
    return this.dialogIsOpen;
  }
  set isViewingDialog(value: boolean) {
    this.dialogIsOpen = value;
  }

  get jobProgressAlertsMap(): Map<number, JobProgressAlert> | null {
    switch (this.jobProgressAlertType) {
      case JobProgressAlertType.APPROACHING_PICKUP_TIME:
        return this.driverMessageStore.pickupProgressAlerts;
      case JobProgressAlertType.AWAITING_ACCEPT:
        return this.driverMessageStore.acceptProgressAlerts;
      case JobProgressAlertType.LOAD_TIME:
        return this.driverMessageStore.loadTimeProgressAlerts;
      default:
        return null;
    }
  }

  get jobProgressAlertList(): JobProgressAlertDetails[] {
    if (!this.dialogIsOpen || this.jobProgressAlertsMap === null) {
      return [];
    }
    const fleetAssetStore = useFleetAssetStore();

    const sortAsc = (a: JobProgressAlert, b: JobProgressAlert) =>
      (a.epochTime ? a.epochTime : 0) - (b.epochTime ? b.epochTime : 0);
    return Array.from(this.jobProgressAlertsMap.values())
      .sort((a, b) => sortAsc(a, b))
      .map((a) => {
        const foundJob = useJobStore().operationJobsList.find(
          (j) => j.jobId === a.jobId,
        );
        const displayId =
          foundJob && foundJob.recurringJobId
            ? foundJob.recurringJobId
            : `${a.jobId}`;
        const fleet = foundJob
          ? fleetAssetStore.getFleetAssetFromFleetAssetId(foundJob.fleetAssetId)
          : undefined;
        const csrAssignedId = fleet ? fleet.csrAssignedId : 'N/A';
        const driver = foundJob
          ? useDriverDetailsStore().getDriverFromDriverId(foundJob.driverId)
          : undefined;
        const driverName = driver?.displayName ?? 'N/A';
        const driverMobile = driver?.mobile ?? 'N/A';
        return {
          ...a,
          date: returnFormattedDate(a.epochTime),
          time: returnFormattedTime(a.epochTime),
          displayId,
          csrAssignedId,
          driverName,
          driverMobile,
        };
      });
  }

  get jobCount(): number {
    if (!this.jobProgressAlertsMap) {
      return 0;
    }
    return this.jobProgressAlertsMap.size;
  }

  get activeAlertsCount(): number {
    if (this.jobProgressAlertsMap === null) {
      return 0;
    }
    return this.jobProgressAlertsMap.size;
  }

  public beforeMount() {
    const suffix = returnProgressAlertTypeMessage(this.jobProgressAlertType);
    if (suffix) {
      this.dialogTitle = `Jobs ${suffix}`;
    }
    switch (this.jobProgressAlertType) {
      case JobProgressAlertType.APPROACHING_PICKUP_TIME:
        this.buttonTitle = 'Pickup Soon ';
        break;
      case JobProgressAlertType.AWAITING_ACCEPT:
        this.buttonTitle = 'Not Accepted';
        break;
      case JobProgressAlertType.LOAD_TIME:
        this.buttonTitle = 'Long Load Time';
        break;
    }
  }
}
