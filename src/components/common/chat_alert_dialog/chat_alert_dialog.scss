.chat-alert-dialog {
  // position: relative;
  // height: 100%;
  // display: flex;
  // align-items: center;

  .button {
    position: relative;
    height: 75%;
    padding: 0px 8px;
    margin: 0px 1px;
    border-radius: 12px;
    background-color: rgba(79, 78, 90);
    color: $border-light-client;
    font-weight: 700;
    font-size: $font-size-small;

    .button-count {
      color: yellow;
      font-family: $sub-font-family;
      padding: 4px;
      font-weight: 700;
      font-size: $font-size-13;
    }
    &:hover {
      background-color: rgb(88, 87, 99);
      cursor: pointer;
    }
  }
}
