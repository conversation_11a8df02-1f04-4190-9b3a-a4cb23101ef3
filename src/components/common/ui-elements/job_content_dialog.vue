<template>
  <v-dialog
    v-model="dialogIsOpen"
    width="100%"
    class="ma-0 job-content-dialog"
    persistent
    :content-class="contentClassStr"
  >
    <div class="content-wrapper">
      <!-- Button toolbar group -->
      <div
        class="dialog-toolbar-btn-group"
        :class="{ 'with-panel': activePanel }"
        v-if="!sessionManager.isClientPortal()"
      >
        <button
          type="button"
          class="dialog-toolbar-btn notes"
          @click="openPanel('notes')"
          :class="{ active: activePanel === 'notes' }"
          aria-label="Notes"
          title="Notes"
        >
          <span class="icon-wrapper">
            <i class="far fa-sticky-note"></i>
          </span>
          <span v-if="activePanel === 'notes'" class="button-text">Notes</span>
        </button>
        <button
          v-if="!hideDriverChat"
          type="button"
          class="dialog-toolbar-btn chat"
          @click="openPanel('chat')"
          :class="{ active: activePanel === 'chat' }"
          aria-label="Driver Chat"
          title="Driver Chat"
        >
          <span class="icon-wrapper">
            <i class="far fa-comment-lines"></i>
          </span>
          <span v-if="activePanel === 'chat'" class="button-text"
            >Driver Chat</span
          >
        </button>
      </div>
      <div class="content-container" :style="contentContainerStyle">
        <div class="main-dialog-body" :style="mainDialogBodyStyle">
          <v-layout
            v-if="isContentDialog"
            justify-space-between
            class="app-theme__center-content--header no-highlight"
          >
            <span>
              {{ title }}
            </span>
            <ConfirmationDialog
              buttonText="Cancel"
              message="You currently have unsaved changes. Please confirm that you wish to proceed without saving."
              title="Unsaved Changes identified"
              @confirm="cancel"
              :cancelButtonText="'Go Back'"
              :isSmallButton="false"
              :buttonDisabled="isLoading"
              :isOutlineButton="true"
              :buttonColor="'error'"
              :confirmationButtonText="'proceed without saving'"
              :dialogIsActive="isConfirmUnsaved"
              :confirmationButtonColor="'error'"
              :cancelButtonColor="'blue'"
              :isDialogCloseConfirmation="true"
            ></ConfirmationDialog>
          </v-layout>
          <v-layout class="app-theme__center-content--body" row wrap>
            <v-flex md12>
              <v-layout row wrap :class="contentPadding">
                <v-flex md12>
                  <slot></slot>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12 v-if="showActions">
              <v-divider></v-divider>
              <v-layout
                :class="
                  showCancelBtn ? 'justify-space-between ' : 'justify-end'
                "
              >
                <ConfirmationDialog
                  v-if="showCancelBtn"
                  buttonText="Cancel"
                  message="You currently have unsaved changes. Please confirm that you wish to proceed without saving."
                  title="Unsaved Changes identified"
                  @confirm="cancel"
                  :cancelButtonText="'Go Back'"
                  :isSmallButton="false"
                  :buttonDisabled="isLoading"
                  :isOutlineButton="true"
                  :buttonColor="'error'"
                  :confirmationButtonText="'proceed without saving'"
                  :dialogIsActive="isConfirmUnsaved"
                  :confirmationButtonColor="'error'"
                  :cancelButtonColor="'blue'"
                ></ConfirmationDialog>
                <ConfirmationDialog
                  v-if="showActionButton"
                  :buttonText="actionBtnText"
                  :message="actionConfirmationMessage"
                  title="Please confirm before proceeding"
                  @confirm="action"
                  :buttonDisabled="isLoading"
                  :isFlatButton="true"
                  :buttonColor="
                    sessionManager.isClientPortal() ? 'info' : 'white'
                  "
                  :dialogIsActive="actionRequiresConfirmation"
                ></ConfirmationDialog>
                <v-spacer></v-spacer>
                <v-btn
                  color="blue"
                  class="v-btn-confirm-custom"
                  :disabled="isDisabled || isLoading"
                  :loading="isLoading"
                  depressed
                  @click="confirmAndClose"
                  >{{ confirmBtnText }}
                </v-btn>
              </v-layout>
            </v-flex>
          </v-layout>
        </div>
        <!-- SIDE BAR EXPANDED WITH NOTES AND DRIVER CHAT -->
        <div
          class="panel-flyout"
          :class="[activePanel, { open: !!activePanel }]"
          :style="panelFlyoutStyle"
          :aria-hidden="!activePanel"
          v-if="!sessionManager.isClientPortal()"
        >
          <template v-if="activePanel">
            <div class="panel-header">
              <div class="panel-header-buttons">
                <button
                  type="button"
                  class="panel-header-btn notes"
                  @click="openPanel('notes')"
                  :class="{ active: activePanel === 'notes' }"
                >
                  <span class="icon-wrapper">
                    <i class="far fa-sticky-note"></i>
                  </span>
                  <span v-if="activePanel === 'notes'" class="button-text"
                    >Notes</span
                  >
                </button>
                <button
                  v-if="!hideDriverChat"
                  type="button"
                  class="panel-header-btn chat"
                  @click="openPanel('chat')"
                  :class="{ active: activePanel === 'chat' }"
                  aria-label="Driver Chat"
                  title="Driver Chat"
                >
                  <span class="icon-wrapper">
                    <i class="far fa-comment-lines"></i>
                  </span>
                  <span v-if="activePanel === 'chat'" class="button-text"
                    >Driver Chat</span
                  >
                </button>
              </div>
              <button
                class="panel-close-btn"
                @click="closePanel"
                aria-label="Close panel"
              >
                <i class="fas fa-chevron-left"></i>
              </button>
            </div>
            <div class="panel-content">
              <!-- Notes Panel Content -->
              <div v-if="activePanel === 'notes'" class="notes-panel">
                <JobBookingAllNotesSection
                  :jobDetails="jobDetails"
                  :clientDetails="clientDetails"
                  :pudId="pudId"
                />
              </div>

              <!-- Driver Chat Panel Content -->
              <div v-if="activePanel === 'chat'" class="chat-panel">
                <v-flex class="driver-chat-panel" v-if="jobDetails?.driverId">
                  <!-- Chat header -->
                  <v-flex class="chat-header">
                    <div class="header-title">
                      <v-icon size="14" color="orange" class="pr-2"
                        >chat</v-icon
                      >
                      <span>{{ jobDetails?.driverName }} Chat</span>
                    </div>
                    <!-- Driver info -->
                    <v-flex class="header-subtitle">
                      <v-icon size="14" class="px-2" color="grey"
                        >schedule</v-icon
                      >
                      {{ readyTime }} - {{ finishTime }}
                    </v-flex>
                    <v-flex class="header-subtitle">
                      <v-icon size="14" class="px-2" color="grey"
                        >pin_drop</v-icon
                      >
                      {{ driverLocation }}
                    </v-flex>
                  </v-flex>
                  <v-btn
                    v-if="showExpandIcon"
                    depressed
                    small
                    flat
                    @click="emit('expand')"
                    color="warning"
                  >
                    <v-icon size="16" class="px-2">chat</v-icon>
                    Message {{ jobDetails.driverName }}
                  </v-btn>
                  <v-btn
                    depressed
                    small
                    flat
                    @click="showMessageHistoryDialog = true"
                    color="accent"
                  >
                    <v-icon size="16" class="px-2">fas fa-history</v-icon>
                    View Chat History
                  </v-btn>
                  <!-- Conversation Component -->

                  <DriverConversation
                    :canSendMessage="false"
                    :driverId="jobDetails.driverId"
                  ></DriverConversation>
                </v-flex>
                <v-flex class="driver-chat-panel" v-else>
                  <span class="header-title">NO DRIVER</span>
                </v-flex>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- DRIVER MESSAGE HISTORY DIALOG -->
    <v-dialog
      v-model="showMessageHistoryDialog"
      v-if="jobDetails?.driverId && showMessageHistoryDialog"
      width="800px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-layout column>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Driver Message History</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="showMessageHistoryDialog = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout row wrap class="app-theme__center-content--body">
          <v-flex md12>
            <v-layout row wrap>
              <DriverChatHistory
                :singleDriverType="true"
                :driverId="jobDetails?.driverId"
              ></DriverChatHistory>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-layout>
    </v-dialog>
  </v-dialog>
</template>

<script setup lang="ts">
import JobBookingAllNotesSection from '@/components/booking/job_booking_all_notes_section.vue';
import DriverChatHistory from '@/components/common/driver_chat_history/driver_chat_history.vue';
import DriverConversation from '@/components/common/driver_conversation/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { sessionManager } from '@/store/session/SessionState';
import {
  computed,
  ComputedRef,
  onMounted,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    title: string;
    width?: string;
    confirmBtnText?: string;
    showDialog?: boolean;
    isConfirmUnsaved?: boolean;
    isDisabled?: boolean;
    isLoading?: boolean;
    showActions?: boolean;
    showCancelBtn?: boolean;
    contentPadding?: string;
    contentClass?: string;
    showActionButton?: boolean;
    actionBtnText?: string;
    actionRequiresConfirmation?: boolean;
    actionConfirmationMessage?: string;
    jobDetails?: JobDetails | null;
    clientDetails?: ClientDetails | null;
    isContentDialog: boolean;
    pudId?: string | null;
    showExpandIcon?: boolean;
    sidePanelDoesNotExpandContent?: boolean;
    hideDriverChat?: boolean;
  }>(),
  {
    width: '95vw',
    confirmBtnText: 'save',
    showDialog: false,
    isConfirmUnsaved: false,
    isDisabled: false,
    isLoading: false,
    showActions: true,
    showCancelBtn: true,
    contentPadding: 'pa-3',
    contentClass: '',
    showActionButton: false,
    actionBtnText: '',
    actionRequiresConfirmation: false,
    actionConfirmationMessage: '',
    isContentDialog: true,
    pudId: null,
    showExpandIcon: false,
    jobDetails: null,
    clientDetails: null,
    sidePanelDoesNotExpandContent: false,
    hideDriverChat: false,
  },
);

const emit = defineEmits<{
  (event: 'action', payload: void): void;
  (event: 'confirm', payload: void): void;
  (event: 'cancel', payload: void): void;
  (event: 'update:showDialog', payload: boolean): void;
  (event: 'expand'): void;
}>();

const dialogIsOpen: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.showDialog;
  },
  set(value: boolean): void {
    emit('update:showDialog', value);
  },
});

const showMessageHistoryDialog = ref(false);
const panelWidthRatio = 0.15;
const panelMinWidth = 220;
const panelMaxWidth = 420;

// Local storage keys
const STORAGE_KEY_PREFIX = 'job_content_dialog_';
const getStorageKey = (key: string) => `${STORAGE_KEY_PREFIX}${key}`;

// Panel state management with localStorage persistence
const activePanel = ref<null | 'notes' | 'chat'>(null);

/**
 * If contentClass is provided, it will be used as the class for the dialog
 * content. Otherwise use the default class 'v-dialog-custom'. If the user is in
 * the client portal, add the class 'client-portal'.
 */
const contentClassStr: ComputedRef<string> = computed(() => {
  const classes = props.contentClass
    ? [props.contentClass]
    : ['v-dialog-custom'];

  if (sessionManager.isClientPortal()) {
    classes.push('client-portal');
  }

  return classes.join(' ');
});

// Additional button beside the cancel button to get the parent component to
// perform a custom action
function action() {
  emit('action');
}

function confirmAndClose() {
  emit('confirm');
}

function cancel() {
  emit('cancel');
}

function openPanel(panel: 'notes' | 'chat') {
  // Prevent opening chat panel if it's hidden
  if (panel === 'chat' && props.hideDriverChat) {
    return;
  }

  if (activePanel.value === panel) {
    activePanel.value = null;
  } else {
    activePanel.value = panel;
  }
}

function closePanel() {
  activePanel.value = null;
}

// Parse px, rem, vw, % values to px
function parsePx(val: string | number): number {
  if (typeof val === 'number') {
    return val;
  }
  if (val.endsWith('px')) {
    return parseFloat(val);
  }
  if (val.endsWith('vw')) {
    return (parseFloat(val) / 100) * window.innerWidth;
  }
  if (val.endsWith('%')) {
    return (parseFloat(val) / 100) * window.innerWidth;
  }
  return parseFloat(val);
}

// Panel width calculations
const panelFlyoutWidthPx = computed((): number => {
  if (!activePanel.value) {
    return 0;
  }

  const panelW = Math.max(
    panelMinWidth,
    Math.min(panelMaxWidth, window.innerWidth * panelWidthRatio),
  );

  return panelW > 0 ? panelW : 0;
});

const contentContainerStyle: ComputedRef<any> = computed(() => {
  const baseWidth = parsePx(props.width);
  const panelWidth = panelFlyoutWidthPx.value;

  if (props.sidePanelDoesNotExpandContent) {
    return {
      width: baseWidth + (activePanel.value ? panelWidth : 0) + 'px',
      position: 'relative',
      display: 'flex',
      flexDirection: 'row',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
      maxWidth: '95vw',
    };
  } else {
    return {
      width: baseWidth + panelWidth + 'px',
      position: 'relative',
      display: 'flex',
      flexDirection: 'row',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
      maxWidth: '95vw',
    };
  }
});

const mainDialogBodyStyle: ComputedRef<any> = computed(() => {
  const baseWidth = parsePx(props.width);

  if (props.sidePanelDoesNotExpandContent) {
    // Always fixed width for main content
    return {
      width: baseWidth + 'px',
      position: 'relative',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
      margin: '0',
      maxWidth: baseWidth + 'px',
    };
  } else {
    // Default: main content shrinks when panel is open
    const baseWidthVw = activePanel.value ? '80vw' : '95vw';
    const mainW = Math.max(320, parsePx(baseWidthVw));
    return {
      width: mainW + 'px',
      position: 'relative',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
      margin: '0',
      maxWidth: activePanel.value ? '80vw' : '95vw',
    };
  }
});

const panelFlyoutStyle: ComputedRef<{
  right: string;
  width: string;
  minWidth: string;
  maxWidth: string;
  height: string;
  transition: string;
}> = computed(() => {
  return {
    right: '0',
    width: panelFlyoutWidthPx.value + 'px',
    minWidth: panelFlyoutWidthPx.value > 0 ? panelMinWidth + 'px' : '0',
    maxWidth: panelMaxWidth + 'px',
    height: '100%',
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  };
});

// Driver info computed properties
const readyTime = computed(() => {
  if (!props.jobDetails?.pudItems?.length) {
    return '';
  }
  return returnFormattedDate(props.jobDetails.pudItems[0].epochTime, 'hh:mm a');
});

const finishTime = computed(() => {
  if (!props.jobDetails?.pudItems?.length) {
    return '';
  }
  const last = props.jobDetails.pudItems[props.jobDetails.pudItems.length - 1];
  return returnFormattedDate(last.epochTime + last.loadTime, 'hh:mm a');
});

const driverLocation = computed(() => {
  if (!props.jobDetails?.pudItems?.length) {
    return '';
  }
  return props.jobDetails.pudItems[0].address.suburb;
});

// Save panel state to localStorage when it changes
watch(activePanel, (newPanel) => {
  if (newPanel) {
    localStorage.setItem(getStorageKey('activePanel'), newPanel);
  } else {
    localStorage.removeItem(getStorageKey('activePanel'));
  }
});

// Load panel state from localStorage on mount
onMounted(() => {
  const savedPanel = localStorage.getItem(getStorageKey('activePanel'));
  if (savedPanel && (savedPanel === 'notes' || savedPanel === 'chat')) {
    if (savedPanel === 'chat' && props.hideDriverChat) {
      return;
    }
    activePanel.value = savedPanel;
  }
});
</script>

<style scoped lang="scss">
/* Fix for v-menu z-index issues when using JobContentDialog */
:deep(.v-menu) {
  z-index: 1001 !important;
}

:deep(.v-tooltip) {
  z-index: 1001 !important;
}

/* Global styles for the job content dialog */
:deep(.v-dialog) {
  border: none;
  overflow: visible !important;
  background-color: none;
  box-shadow: none;
}

// CLIENT PORTAL STYLES
:deep(.v-dialog-custom.client-portal) {
  background-color: transparent !important;
}

.content-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: fit-content;
  margin: 0 auto;
}

.content-container {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  max-width: 95vw;
  margin: 0 auto;
  overflow: hidden;
  will-change: width;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-dialog-body {
  flex: 1 1 auto;
  min-width: 0;
  overflow: hidden;
  will-change: width;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-color);
  border-radius: 12px;
}

/* Minimal sticky toolbar */
.dialog-toolbar-btn-group {
  position: absolute;
  right: -60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 14px;
  z-index: 1000;
  padding: 6px;
  background: var(--background-color-400);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: var(--box-shadow);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
  opacity: 1;
  transform: translateX(0);
}

.dialog-toolbar-btn-group.with-panel {
  opacity: 0;
  transform: translateX(20px);
  pointer-events: none;
}

.button-text {
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-color);
  margin-left: 8px;
  white-space: nowrap;
  opacity: 0;
  animation: fadeInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  transition: var(--transition-fast);
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.dialog-toolbar-btn {
  width: 46px;
  height: 46px;
  border-radius: 26px;
  border: 1px solid var(--border-color);
  outline: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
  background: var(--background-color-300);
  position: relative;
  overflow: hidden;
  will-change: transform, background, border-color, box-shadow;
}

.dialog-toolbar-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.dialog-toolbar-btn:hover::before {
  left: 100%;
}

.dialog-toolbar-btn.notes:hover {
  background: var(--background-color-400);
  border-color: var(--warning);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dialog-toolbar-btn.chat:hover {
  background: var(--background-color-400);
  border-color: var(--accent-secondary);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.icon-wrapper i {
  font-size: 1.5rem;
  color: var(--light-text-color);
  transition: var(--transition-fast);
  will-change: color, transform;
}

.dialog-toolbar-btn:hover .icon-wrapper i,
.dialog-toolbar-btn.active .icon-wrapper i {
  color: var(--text-color);
  transform: scale(1.1);
}

.dialog-toolbar-btn.notes:hover .icon-wrapper i,
.dialog-toolbar-btn.active.notes .icon-wrapper i {
  color: var(--warning);
}

.dialog-toolbar-btn.chat:hover .icon-wrapper i,
.dialog-toolbar-btn.active.chat .icon-wrapper i {
  color: var(--accent-secondary);
}

.panel-flyout {
  flex: 0 0 auto;
  position: relative;
  overflow: hidden;
  min-width: 0;
  width: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  will-change: width, opacity, transform;
  opacity: 0;
  transform: translateX(-20px);
  background: var(--background-color-100);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  margin-left: 4px;

  /* Scrollbar styles for all scrollable elements inside panel */
  ::-webkit-scrollbar {
    width: 2px !important;
  }

  ::-webkit-scrollbar-track {
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--border-color) !important;
    border-radius: 20px;
  }

  * {
    scrollbar-width: thin;
    scrollbar-gutter: stable;
  }
}

.panel-flyout.open {
  width: var(--panel-width);
  opacity: 1;
  transform: translateX(0);
  box-shadow: var(--box-shadow);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &.chat {
    border-left: 1px solid var(--accent-secondary);
  }
  &.notes {
    border-left: 1px solid var(--warning);
  }
}

.panel-flyout .panel-content {
  flex: 1 1 auto;
  padding: 2px 4px 2px 12px;
  overflow: hidden;
  height: calc(100vh - 380px);
  background: var(--background-color-200);
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.panel-flyout.open .panel-content {
  opacity: 1;
  transform: translateX(0);
  overflow-y: auto;
  overflow-x: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--background-color-200);
  border-bottom: 1px solid var(--border-color);
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity, transform;
}

.panel-flyout.open .panel-header {
  opacity: 1;
  transform: translateX(0);
}

.panel-header-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-header-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 1px solid var(--border-color);
  outline: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--background-color-300);
  position: relative;
  overflow: hidden;
  will-change: transform, background, border-color, box-shadow;
}

.panel-header-btn.active {
  width: auto;
  padding: 0 16px;
}

.panel-header-btn.notes.active {
  border-color: var(--warning);
  i {
    color: var(--warning);
  }
  .button-text {
    color: var(--warning);
  }
}

.panel-header-btn.chat.active {
  border-color: var(--accent-secondary);
  i {
    color: var(--accent-secondary);
  }
  .button-text {
    color: var(--accent-secondary);
  }
}

.panel-header-btn:hover {
  background: var(--background-color-400);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.panel-header-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.panel-header-btn:hover::before {
  left: 100%;
}

.panel-header-btn.notes:hover {
  border-color: var(--warning);
  i {
    color: var(--warning);
  }
}

.panel-header-btn.chat:hover {
  border-color: var(--accent-secondary);
  i {
    color: var(--accent-secondary);
  }
}

.panel-flyout .panel-close-btn {
  background: var(--background-color-300);
  border-radius: 40px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--light-text-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.9rem;
  will-change: background, color, transform;
}

.panel-flyout .panel-close-btn:hover {
  background: var(--background-color-400);
  color: var(--text-color);
  border-color: var(--border-color-100);
}

.notes-panel,
.chat-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-items: center;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.panel-flyout.open .notes-panel,
.panel-flyout.open .chat-panel {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.3s; /* Delay content fade-in */
}

/* Ensure driver chat panel scrollbar is consistent */
.driver-chat-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .chat-header {
    border-bottom: 1px solid $translucent;
    color: var(light-text-color);
  }
  .header-subtitle {
    color: var(--light-text-color);
    padding: 1px;
  }
  .header-title {
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.5px;
    padding: 6px;
  }
}
// Ensure consistent timing for all animations
.content-container,
.main-dialog-body,
.panel-flyout,
.panel-header,
.panel-content,
.dialog-toolbar-btn-group {
  transition-duration: 0.4s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
