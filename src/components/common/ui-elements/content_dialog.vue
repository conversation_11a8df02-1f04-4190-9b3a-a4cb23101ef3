<template>
  <v-dialog
    v-model="dialogIsOpen"
    :width="width"
    class="ma-0"
    persistent
    :content-class="contentClassStr"
  >
    <v-layout
      justify-space-between
      class="app-theme__center-content--header no-highlight"
    >
      <span>{{ title }}</span>
      <ConfirmationDialog
        buttonText="Cancel"
        message="You currently have unsaved changes. Please confirm that you wish to proceed without saving."
        title="Unsaved Changes identified"
        @confirm="cancel"
        :cancelButtonText="'Go Back'"
        :isSmallButton="false"
        :buttonDisabled="isLoading"
        :isOutlineButton="true"
        :buttonColor="'error'"
        :confirmationButtonText="'proceed without saving'"
        :dialogIsActive="isConfirmUnsaved"
        :confirmationButtonColor="'error'"
        :cancelButtonColor="'blue'"
        :isDialogCloseConfirmation="true"
      ></ConfirmationDialog>
    </v-layout>

    <v-layout class="app-theme__center-content--body" row wrap>
      <v-flex md12>
        <v-layout row wrap :class="contentPadding">
          <v-flex md12>
            <slot></slot>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="showActions">
        <v-divider></v-divider>
        <v-layout
          :class="showCancelBtn ? 'justify-space-between ' : 'justify-end'"
        >
          <ConfirmationDialog
            v-if="showCancelBtn"
            buttonText="Cancel"
            message="You currently have unsaved changes. Please confirm that you wish to proceed without saving."
            title="Unsaved Changes identified"
            @confirm="cancel"
            :cancelButtonText="'Go Back'"
            :isSmallButton="false"
            :buttonDisabled="isLoading"
            :isOutlineButton="true"
            :buttonColor="'error'"
            :confirmationButtonText="'proceed without saving'"
            :dialogIsActive="isConfirmUnsaved"
            :confirmationButtonColor="'error'"
            :cancelButtonColor="'blue'"
          ></ConfirmationDialog>

          <ConfirmationDialog
            v-if="showActionButton"
            :buttonText="actionBtnText"
            :message="actionConfirmationMessage"
            title="Please confirm before proceeding"
            @confirm="action"
            :buttonDisabled="isLoading"
            :isFlatButton="true"
            :buttonColor="sessionManager.isClientPortal() ? 'info' : 'white'"
            :dialogIsActive="actionRequiresConfirmation"
          ></ConfirmationDialog>

          <v-spacer></v-spacer>
          <v-btn
            color="blue"
            class="v-btn-confirm-custom"
            :disabled="isDisabled || isLoading"
            :loading="isLoading"
            :id="confirmBtnId || null"
            depressed
            @click="confirmAndClose"
            >{{ confirmBtnText }}
          </v-btn>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-dialog>
</template>

<script setup lang="ts">
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { sessionManager } from '@/store/session/SessionState';
import { computed, ComputedRef, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    title: string;
    width?: string;
    confirmBtnText?: string;
    showDialog?: boolean;
    isConfirmUnsaved?: boolean;
    isDisabled?: boolean;
    isLoading?: boolean;
    isAuthorised?: boolean;
    // shows or hides the action buttons at the bottom of the template
    showActions?: boolean;
    showCancelBtn?: boolean;
    contentPadding?: string;
    contentClass?: string;
    showActionButton?: boolean;
    actionBtnText?: string;
    // If actionRequiresConfirmation is TRUE, then we will show a confirmation dialog before
    // triggering the @action event. The dialog will show the message defined in
    // actionConfirmationMessage
    actionRequiresConfirmation?: boolean;
    actionConfirmationMessage?: string;
    confirmBtnId?: string;
  }>(),
  {
    width: '600px',
    confirmBtnText: 'save',
    showDialog: false,
    isConfirmUnsaved: false,
    isDisabled: false,
    isLoading: false,
    isAuthorised: true,
    showActions: true,
    showCancelBtn: true,
    contentPadding: 'pa-3',
    contentClass: '',
    showActionButton: false,
    // Shows an additional button beside the cancel button
    actionBtnText: '',
    actionRequiresConfirmation: false,
    actionConfirmationMessage: '',
    confirmBtnId: '',
  },
);

const emit = defineEmits<{
  (event: 'action', payload: void): void;
  (event: 'confirm', payload: void): void;
  (event: 'cancel', payload: void): void;
  (event: 'update:showDialog', payload: boolean): void;
}>();

const dialogIsOpen: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.showDialog;
  },
  set(value: boolean): void {
    emit('update:showDialog', value);
  },
});

/**
 * If contentClass is provided, it will be used as the class for the dialog
 * content. Otherwise use the default class 'v-dialog-custom'. If the user is in
 * the client portal, add the class 'client-portal'.
 */
const contentClassStr: ComputedRef<string> = computed(() => {
  const classes = props.contentClass
    ? [props.contentClass]
    : ['v-dialog-custom'];

  if (sessionManager.isClientPortal()) {
    classes.push('client-portal');
  }

  return classes.join(' ');
});

// Additional button beside the cancel button to get the parent component to
// perform a custom action
function action() {
  emit('action');
}

function confirmAndClose() {
  emit('confirm');
}

function cancel() {
  emit('cancel');
}
</script>
