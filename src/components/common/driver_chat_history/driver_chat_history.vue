<template>
  <div class="driver-chat-history">
    <v-layout
      justify-space-between
      class="driver-search-top-section px-2 pt-2 pb-1"
    >
      <v-flex md4 v-if="!singleDriverType">
        <v-form>
          <SelectEntity
            :entityTypes="[EntityType.DRIVER]"
            :id.sync="driverChatHistoryRequest.driverId"
          />
        </v-form>
      </v-flex>
      <v-flex md3 class="px">
        <DatePickerBasic
          :soloInput="true"
          @setEpoch="setStartEpoch"
          v-model="driverChatHistoryRequest.startEpoch"
          :labelName="singleDriverType ? 'From Date' : 'Search Date'"
          :epochTime="driverChatHistoryRequest.startEpoch"
        >
        </DatePickerBasic>
      </v-flex>
      <v-flex md3 class="px" v-if="singleDriverType">
        <DatePickerBasic
          @setEpoch="setEndEpoch"
          :soloInput="true"
          v-model="driverChatHistoryRequest.endEpoch"
          :labelName="'To Date'"
          :epochTime="driverChatHistoryRequest.endEpoch"
        >
        </DatePickerBasic>
      </v-flex>
      <v-spacer></v-spacer>
      <v-flex :class="singleDriverType ? 'md3' : 'md2'" class="mx-4">
        <v-btn
          class="view-details-button"
          block
          depressed
          :loading="isAwaitingResponse"
          @click="searchDriverChatHistory"
          >Search</v-btn
        >
      </v-flex>
    </v-layout>
    <v-layout class="filter-result-container">
      <v-flex md12>
        <v-text-field
          hide-details
          class="v-solo-custom ma-2"
          solo
          flat
          color="orange"
          v-model="search"
          placeholder="Filter Results..."
        >
          <template v-slot:prepend-inner>
            <v-tooltip right>
              <template v-slot:activator="{ on }">
                <v-icon v-on="on" size="17" color="grey lighten-1" class="mr-2"
                  >far fa-search</v-icon
                >
              </template>
              Start typing to search for certain phrases within this drivers
              messages.
            </v-tooltip>
          </template>
        </v-text-field>
      </v-flex>
    </v-layout>

    <v-divider />
    <v-layout>
      <v-flex md12>
        <div
          class="table-content"
          :class="singleDriverType ? 'asset-information' : 'restrict-height'"
        >
          <div class="table-scrollable" ref="messageContentsDiv">
            <div class="job-list-container app-theme__center-content--body">
              <div class="message-contents">
                <v-layout justify-center pa-4 v-if="isAwaitingResponse">
                  <v-progress-circular
                    :size="50"
                    color="light-blue"
                    indeterminate
                  ></v-progress-circular>
                </v-layout>
                <v-layout
                  v-if="!isAwaitingResponse && filteredResults.length === 0"
                  class="pa-3"
                  justify-center
                  style="color: #b2b0c3"
                  >No conversations found...
                </v-layout>
                <v-layout
                  column
                  v-for="datedMessage in filteredResults"
                  justify-start
                  :key="datedMessage.date"
                  class="message-contents__row"
                >
                  <v-layout justify-center>
                    <span class="conversation-date">{{
                      datedMessage.date
                    }}</span>
                  </v-layout>

                  <v-layout
                    wrap
                    :key="item._id"
                    v-for="(item, chatIndex) in datedMessage.chatMessages"
                    class="message-contents__row"
                    px-2
                  >
                    <v-layout column class="chat-bubble__container">
                      <v-layout pb-1 align-center>
                        <span
                          class="chat-bubble__username"
                          v-if="
                            chatIndex === 0 ||
                            item.senderName !==
                              datedMessage.chatMessages[chatIndex - 1]
                                .senderName
                          "
                        >
                          {{ item.senderName }}
                          <span
                            v-if="
                              item.fleetAssetId ||
                              chatIndex === 0 ||
                              item.fleetAssetId !==
                                datedMessage.chatMessages[chatIndex - 1]
                                  .fleetAssetId
                            "
                          >
                            {{
                              csrAssignedIdByFleetAssetId(item.fleetAssetId)
                            }}</span
                          >
                        </span>
                      </v-layout>

                      <v-hover v-slot:default="{ hover }">
                        <v-layout row align-center>
                          <!-- Chat Bubble -->
                          <div class="chat-bubble">
                            <span class="chat-bubble__text">
                              {{ item.content }}
                              <span class="chat-bubble__time">
                                {{ returnFormattedTime(item.timestamp) }}
                              </span>
                            </span>
                          </div>

                          <!-- Copy Icon NEXT to chat bubble (visible on hover) -->
                          <div v-if="hover" style="margin-left: 8px">
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on }">
                                <v-btn
                                  flat
                                  icon
                                  @click="copyMessage(item.content)"
                                  class="ma-0"
                                  v-on="on"
                                >
                                  <v-icon size="18" color="accent"
                                    >far fa-copy</v-icon
                                  >
                                </v-btn>
                              </template>
                              <span>Copy</span>
                            </v-tooltip>
                          </div>
                        </v-layout>
                      </v-hover>
                    </v-layout>
                  </v-layout>
                </v-layout>
              </div>
            </div>
          </div>
        </div>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import ChatHistoryRequest from '@/interface-models/Generic/ChatConversation/RequestChatHistory';

import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import {
  computed,
  ComputedRef,
  nextTick,
  onBeforeMount,
  onMounted,
  ref,
  Ref,
} from 'vue';
interface DatedChatMessage {
  date: string;
  chatMessages: ChatMessage[];
}

const props = withDefaults(
  defineProps<{
    singleDriverType: boolean;
    driverId: string;
  }>(),
  {
    singleDriverType: false,
    driverId: '',
  },
);

const fleetAssetStore = useFleetAssetStore();

const driverChatHistoryRequest: Ref<ChatHistoryRequest> = ref(
  new ChatHistoryRequest(),
);
const messageContentsDiv = ref<HTMLElement | null>(null);
const search: Ref<string> = ref('');
const isAwaitingResponse: Ref<boolean> = ref(false);

const driverChatHistory: Ref<DatedChatMessage[] | null> = ref(null);

// Handle selections from start date picker
function setStartEpoch(startEpoch: number) {
  driverChatHistoryRequest.value.startEpoch =
    returnStartOfDayFromEpoch(startEpoch);

  // If we're not in single driver mode, we're only ever searching a single
  // day at a time.
  if (!props.singleDriverType) {
    driverChatHistoryRequest.value.endEpoch =
      returnEndOfDayFromEpoch(startEpoch);
  }
}
// Handle selections from end date picker
function setEndEpoch(endEpoch: number) {
  driverChatHistoryRequest.value.endEpoch = returnEndOfDayFromEpoch(endEpoch);
}

// Dispatch request with current parameters
async function searchDriverChatHistory() {
  if (driverChatHistory.value && driverChatHistory.value.length) {
    driverChatHistory.value = null;
  }
  isAwaitingResponse.value = true;
  const results = await useDriverMessageStore().requestChatMessagesForDriverId(
    driverChatHistoryRequest.value,
  );
  if (results) {
    constructDatedChatMessageList(results);
  } else {
    showNotification(
      'Something went wrong. The messages could not be received. Please try again soon.',
    );
    driverChatHistory.value = [];
  }
  isAwaitingResponse.value = false;
}
// Handle response for chat message list request
function constructDatedChatMessageList(chatMessages: ChatMessage[]): void {
  const conversation: DatedChatMessage[] = [];
  // Set to empty list if no chat messages were in response
  if (!chatMessages.length) {
    driverChatHistory.value = [];
    return;
  }
  chatMessages = chatMessages.sort((a, b) => a.timestamp - b.timestamp);
  const datedChatMessage: DatedChatMessage = {
    date: returnFormattedDate(chatMessages[0].timestamp),
    chatMessages: [],
  };
  for (let i = 0; i < chatMessages.length; i++) {
    if (
      datedChatMessage.date === returnFormattedDate(chatMessages[i].timestamp)
    ) {
      datedChatMessage.chatMessages.push(chatMessages[i]);
      if (i === chatMessages.length - 1) {
        conversation.push(JSON.parse(JSON.stringify(datedChatMessage)));
      }
      continue;
    }
    conversation.push(JSON.parse(JSON.stringify(datedChatMessage)));
    datedChatMessage.date = returnFormattedDate(chatMessages[i].timestamp);
    datedChatMessage.chatMessages = [];
    datedChatMessage.chatMessages.push(chatMessages[i]);
  }
  driverChatHistory.value = conversation;
}

// function to copy text from message bubble.
function copyMessage(text: string) {
  navigator.clipboard.writeText(text);
}

function csrAssignedIdByFleetAssetId(fleetAssetId: string) {
  const csrAssignedId = fleetAssetStore.csrAssignedIdMap.get(fleetAssetId);
  if (!csrAssignedId) {
    return '';
  }
  return ' - ' + csrAssignedId;
}
// Filter results based on search string value
const filteredResults: ComputedRef<DatedChatMessage[]> = computed<
  DatedChatMessage[]
>(() => {
  if (!driverChatHistory.value) {
    return [];
  }

  const notFiltered: DatedChatMessage[] = JSON.parse(
    JSON.stringify(driverChatHistory.value),
  );

  if (!search.value.trim()) {
    return driverChatHistory.value;
  }

  return notFiltered
    .map((group) => ({
      date: group.date,
      chatMessages: group.chatMessages.filter((c) =>
        c.content.toLowerCase().includes(search.value.toLowerCase()),
      ),
    }))
    .filter((group) => group.chatMessages.length > 0);
});

function scrollToBottom() {
  setTimeout(() => {
    nextTick(() => {
      const container = messageContentsDiv.value;
      if (container) {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth',
        });
      }
    });
  }, 100);
}

// If single driver type, we have all required data for query so request immediately
onBeforeMount(() => {
  if (props.singleDriverType) {
    driverChatHistoryRequest.value.driverId = props.driverId;
  } else {
    driverChatHistoryRequest.value.startEpoch = returnStartOfDayFromEpoch();
    driverChatHistoryRequest.value.endEpoch = returnEndOfDayFromEpoch();
  }
  searchDriverChatHistory();
});

onMounted(() => {
  scrollToBottom();
});
</script>

<style scoped lang="scss">
.driver-chat-history {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden !important;

  .message-contents__row {
    position: relative;
    padding: 4px 0px;
    margin: 1px 0px;

    .conversation-date {
      font-size: $font-size-medium;
      color: var(--light-text-color);
      text-transform: uppercase;
      font-weight: 600;
      margin-top: 2px;
    }

    .chat-bubble__container {
      position: relative;
      width: 100%;

      .chat-bubble__username {
        color: var(--text-color);
        font-weight: 500;
        font-size: $font-size-large;
        padding-left: 4px;
      }

      .chat-bubble__time {
        font-size: $font-size-10;
        color: #bebebe;
        padding-left: 30px;
      }
      .chat-bubble {
        font-size: $font-size-large;
        min-width: 12%;
        max-width: max-content;
        color: var(--text-color);
        font-weight: 400;
        padding: 4px 0px 10px 0px;
        background-color: rgb(35, 64, 94);
        border-radius: 0px 10px 10px 10px;
        margin-left: 16px;
        cursor: pointer;

        .chat-bubble__text {
          padding: 4px 8px 0px 6px;
          line-height: 1.1;
        }

        .chat-bubble__icon-container {
          opacity: 0;
          min-width: 20px;
          text-align: center;

          &.always-visible {
            opacity: 1;
            .chat-bubble__icon {
              color: rgb(255, 166, 0);
            }
          }
          &:hover {
            cursor: pointer;
            .chat-bubble__icon {
              transition: 0s;
              color: rgb(255, 166, 0);
            }
          }
          .chat-bubble__icon {
            padding: 0px 4px 4px 4px;
            color: rgb(49, 49, 49);
          }
        }
      }
    }
  }

  .table-content {
    overflow: hidden;
    position: relative;

    &.restrict-height {
      .table-scrollable {
        height: calc(600px);
        max-height: calc(600px);
        overflow-y: scroll;
      }
    }

    &.asset-information {
      .table-scrollable {
        height: calc(85vh - 90px);
        max-height: calc(85vh - 90px - 75px);
        overflow-y: scroll;
      }
    }
  }
}
</style>
