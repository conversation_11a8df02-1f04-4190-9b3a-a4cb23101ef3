<section class="driver-conversation">
  <div class="table-content restrict-height">
    <v-layout
      class="table-scrollable"
      v-if="isAwaitingResponse"
      justify-center
      align-center
      fill-height
    >
      <img
        src="@/static/loader/infinity-loader-light.svg"
        height="80px"
        width="80px"
      />
    </v-layout>
    <div
      class="table-scrollable"
      ref="message-contents-div"
      v-if="!isAwaitingResponse && messageList.length"
    >
      <div class="message-contents" v-if="messageList.length">
        <v-layout v-for="(item, index) in messageList" :key="item._id" row>
          <v-layout
            column
            class="message-bubble-container"
            :class="[item.isOperationsMessage ? 'operations' : 'driver']"
          >
            <v-layout
              v-if="differentSenderFromPrevious(index)"
              pb-1
              align-center
              class="message-details-row"
            >
              <span class="username"> {{item.senderName}} </span>
              <span class="datetime">
                {{returnFormattedTime(item.timestamp, `DD/MM/YY`)}}
              </span>
            </v-layout>
            <div
              class="message-bubble"
              :class="{'pinned': item.actionRequired, 'new-message': item.isNewChat}"
            >
              <span> {{item.content}} </span
              ><span class="message-bubble-time">
                {{returnFormattedTime(item.timestamp, `HH:mm`)}}</span
              >
              <div class="message-menu">
                <v-layout class="message-menu-row" align-center>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on }">
                      <v-btn
                        flat
                        icon
                        @click="copyMessage(item.content)"
                        class="ma-0"
                        v-on="on"
                      >
                        <v-icon size="18">far fa-copy</v-icon>
                      </v-btn>
                    </template>
                    <span>Copy</span>
                  </v-tooltip>

                  <div>
                    <v-menu
                      :right="!item.isOperationsMessage"
                      :left="item.isOperationsMessage"
                    >
                      <template v-slot:activator="{ on: menu }">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on: tooltip }">
                            <v-btn
                              flat
                              icon
                              v-on="{ ...tooltip, ...menu }"
                              class="ma-0"
                            >
                              <v-icon size="18">far fa-ellipsis-v </v-icon>
                            </v-btn>
                          </template>
                          <span>View Additional Actions</span>
                        </v-tooltip>
                      </template>
                      <v-list dense class="v-list-custom">
                        <v-list-tile @click="changeActiveState(item)">
                          <v-list-tile-avatar>
                            <v-icon size="16">fal fa-thumbtack</v-icon>
                          </v-list-tile-avatar>
                          <v-list-tile-content>
                            <v-list-tile-title v-if="!item.actionRequired"
                              >Pin Message</v-list-tile-title
                            >
                            <v-list-tile-title v-else
                              >Unpin Message</v-list-tile-title
                            >
                          </v-list-tile-content>
                        </v-list-tile>
                        <v-list-tile @click="linkOrUnlinkMessage(item)">
                          <v-list-tile-avatar>
                            <v-icon size="16">fal fa-link</v-icon>
                          </v-list-tile-avatar>
                          <v-list-tile-content>
                            <v-list-tile-title v-if="!item.jobId"
                              >Link with Job</v-list-tile-title
                            >
                            <v-list-tile-title v-else
                              >Unlink from Job #{{
                              recurringJobIdMap.get(item.jobId) ?
                              recurringJobIdMap.get(item.jobId) : item.jobId ?
                              item.jobId : '--' }}</v-list-tile-title
                            >
                          </v-list-tile-content>
                        </v-list-tile>
                      </v-list>
                    </v-menu>
                  </div>

                  <span v-if="item.jobId" class="linked-jobid"
                    >#{{ recurringJobIdMap.get(item.jobId) ?
                    recurringJobIdMap.get(item.jobId) : item.jobId ? item.jobId
                    : '--' }}</span
                  >
                  <span class="date-time"> {{ item.readableDateTime }} </span>
                </v-layout>
              </div>
            </div>
          </v-layout>
        </v-layout>
      </div>
    </div>
    <v-layout
      class="table-scrollable"
      v-else
      column
      justify-center
      align-center
      fill-height
    >
      <h5 class="subheader--bold">No recent messages found</h5>
      <span class="no-content-message">
        To view older messages,
        <span
          class="is-link"
          v-if="canJumpToHistory"
          @click="jumpToHistoryComponent"
          >click here</span
        >
        <span v-else>please go to Driver Message History.</span>
      </span>
    </v-layout>
  </div>
  <div v-if="canSendMessage">
    <DriverConversationTextfield
      :messageList="messageList"
      :driverId="driverId"
      :jobId="jobId"
    ></DriverConversationTextfield>
  </div>
  <ContentDialog
    v-if="showLinkJobDialog"
    :showDialog.sync="showLinkJobDialog"
    title="Select a job to link this message with"
    width="50%"
    contentPadding="pa-3"
    :isDisabled="!linkChatMessageToJobId"
    :isLoading="awaitingLinkMessageResponse"
    @cancel="closeLinkChatMessageDialog"
    @confirm="linkMessageWithJob"
  >
    <v-layout>
      <v-flex md12>
        <table class="simple-data-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Job #</th>
              <th>Client</th>
              <th>Fleet #</th>
              <th>Driver</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="job in associatedJobList"
              :key="job.jobId"
              class="row-selectable"
              :class="{'row-selected': linkChatMessageToJobId === job.jobId}"
              @click="linkChatMessageToJobId = job.jobId"
            >
              <td>{{job.readableJobDate}}</td>
              <td>{{job.displayId}}</td>
              <td>{{job.clientName}}</td>
              <td>{{job.csrAssignedId}}</td>
              <td>{{job.driverName}}</td>
              <td>{{job.status}}</td>
            </tr>
          </tbody>
        </table>
      </v-flex>
    </v-layout>
  </ContentDialog>
</section>
