.driver-conversation {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .table-content {
    overflow: hidden;
    position: relative;

    &.restrict-height {
      height: calc(100% - 48px);
      max-height: calc(100% - 48px);
    }

    .table-scrollable {
      max-height: 100%;
      height: 100%;
      flex-direction: column;
      position: relative;
      overflow-y: auto;
      display: flex;
      padding-bottom: 40px;
      padding-top: 10px;

      .no-content-message {
        color: rgb(178, 178, 178);
        font-size: $font-size-medium;
        .is-link {
          color: $highlight;
          font-weight: 600;
          &:hover {
            cursor: pointer;
            text-decoration: underline;
          }
        }
      }
    }
  }

  .message-contents {
    padding: 8px 12px;

    .message-bubble-container {
      .message-details-row {
        .username {
          padding: 0px 6px;
          font-family: $sub-font-family;
          font-size: $font-size-12;
          text-transform: uppercase;
          font-weight: 600;
        }
        .datetime {
          font-size: $font-size-12;
          color: rgb(195, 195, 195);
        }
      }

      &.operations {
        align-items: flex-end;
        .message-details-row {
          flex-direction: row-reverse;
        }
        .message-bubble {
          background-color: rgb(35, 64, 94);
          // padding: 6px 12px;

          .message-menu {
            right: 100%;
            .message-menu-row {
              flex-direction: row-reverse;
            }
          }
        }
      }
      @keyframes highlight {
        from {
          border-color: rgb(196, 216, 241);
        }
        to {
          border-color: normal;
        }
      }

      &.driver {
        align-items: start;
        .message-bubble {
          background-color: rgb(72, 71, 80);

          &.new-message {
            animation-name: highlight;
            animation-duration: 2s; /* adjust as needed */
          }
          .message-menu {
            left: 100%;
          }
        }
      }

      .message-bubble {
        padding: 8px 10px;
        margin-top: 2px;
        border-radius: 12px 2px 12px 12px;
        display: inline-block;
        max-width: 80%;
        word-wrap: break-word;
        white-space: pre-wrap;

        .message-bubble-time {
          font-size: 10px;
          opacity: 0.5;
          margin-left: 8px;
          vertical-align: bottom;
          white-space: nowrap;
          padding-left: 4px;
        }

        &.pinned {
          border-color: rgb(255, 166, 0);
        }

        .message-menu {
          position: absolute;
          display: none;
          top: 0px;
          .linked-jobid {
            background-color: rgba(72, 71, 80, 0.628);
            border: 1px solid rgb(72, 71, 80);
            // font-size: $font-size-12;
            font-style: italic;
            padding: 1px 8px;
            border-radius: 3px;
            font-family: $sub-font-family;
          }
          .date-time {
            white-space: nowrap;
            color: rgb(109, 106, 123);
            padding: 0px 8px;
            font-size: 0.85em;
          }
        }

        &:hover {
          cursor: pointer;
          filter: brightness(110%);
          .message-menu {
            display: unset;
          }
        }
      }
    }
  }

  .chat-text-field-input-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}
