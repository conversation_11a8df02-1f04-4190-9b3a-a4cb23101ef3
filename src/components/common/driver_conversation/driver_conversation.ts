import { Component, Prop, Vue } from 'vue-property-decorator';

import {
  returnEndOfDayFromEpoch,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import { useRootStore } from '@/store/modules/RootStore';
import moment from 'moment-timezone';

import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';

import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';

import DriverConversationTextfield from '@/components/common/driver_conversation/driver_conversation_textfield/index.vue';
import { handleChatMessage } from '@/helpers/ChatMessageHelpers/ChatMessageHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import ChatHistoryRequest from '@/interface-models/Generic/ChatConversation/RequestChatHistory';
import { BulkChatMessageResponse } from '@/interface-models/Generic/ChatConversation/SendBulkChatMessageRequest';
import { useJobStore } from '@/store/modules/JobStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import Mitt from '@/utils/mitt';

@Component({
  components: {
    ContentDialog,
    DriverConversationTextfield,
  },
})
export default class DriverConversation extends Vue {
  @Prop() public driverId: string;
  @Prop({ default: null }) public jobId: number | null;
  @Prop({ default: true }) public canSendMessage: boolean;

  public driverMessageStore = useDriverMessageStore();

  public returnFormattedTime = returnFormattedTime;

  public messageContent: string = '';

  public messageList: ChatMessage[] = [];
  public isAwaitingResponse: boolean = false;

  public showLinkJobDialog: boolean = false;
  public linkChatMessageId: string | null = null;
  public linkChatMessageToJobId: number | null = null;
  public awaitingLinkMessageResponse: boolean = false;

  public canJumpToHistory: boolean = false;

  public scrollTimeout: ReturnType<typeof setTimeout>;

  get recurringJobIdMap() {
    if (!useRootStore().operationsPortalLoadedData.JOBS) {
      return;
    }
    return useRecurringJobStore().recurringJobIdMap;
  }

  /**
   * Emits to the parent component, which will jump to the Chat Message History
   * component.
   */
  public jumpToHistoryComponent() {
    this.$emit('jumpToHistoryComponent');
  }

  /**
   * Handles the chat message API response for a specific driver ID.
   * @param messageList - The list of chat messages.
   */
  public handleChatMessagesForDriverId(messageList: ChatMessage[]) {
    this.messageList = messageList.map((m) => {
      return Object.assign(new ChatMessage(), { ...m, isNewChat: false });
    });
    this.scrollToBottom();
  }

  /**
   * Returns an array of OperationJobSummary objects associated with the driver.
   * If the driverId or showLinkJobDialog is falsy, an empty array is returned.
   * @returns {OperationJobSummary[]} The associated job list.
   */
  get associatedJobList(): OperationJobSummary[] {
    if (!this.driverId || !this.showLinkJobDialog) {
      return [];
    }
    return useJobStore().operationJobsList.filter(
      (job: OperationJobSummary) => job.driverId === this.driverId,
    );
  }

  /**
   * This function checks if the sender of the current message is different from
   * the sender of the previous message.
   * @param {number} index - The index of the current message in the message
   * list.
   * @returns {boolean} - Returns true if the sender is different or if it's the
   * first message, otherwise returns false.
   */
  public differentSenderFromPrevious(index: number): boolean {
    if (
      index === 0 ||
      this.messageList[index].senderId !== this.messageList[index - 1].senderId
    ) {
      return true;
    }
    return false;
  }
  /**
   * This function toggles the active state of a chat message. If action is not
   * required, it sets actionRequired and isActioned properties. If
   * action is required, it resets actionRequired and isActioned properties.
   * @param {ChatMessage} message - The chat message to change the active state
   * of.
   */
  public changeActiveState(message: ChatMessage) {
    if (!message.actionRequired) {
      message.actionRequired = true;
      message.isActioned = false;
    } else {
      message.actionRequired = false;
      message.isActioned = true;
    }
    // Send update request
    this.driverMessageStore.updateChatMessage(message);
  }
  /**
   * Links or unlinks a chat message to a job.
   * @param {ChatMessage} chatMessage - The chat message to link or unlink.
   */
  public linkOrUnlinkMessage(chatMessage: ChatMessage) {
    if (!!chatMessage.jobId) {
      this.driverMessageStore.removeChatMessageFromJob(chatMessage);
    } else {
      this.showLinkChatMessageDialog(chatMessage);
    }
  }

  /**
   * Shows the link chat message dialog for the selected message.
   * @param {ChatMessage} chatMessage - The chat message to show the dialog for.
   */
  public showLinkChatMessageDialog(chatMessage: ChatMessage) {
    this.linkChatMessageId = chatMessage._id;
    this.linkChatMessageToJobId = null;
    this.showLinkJobDialog = true;
  }

  /**
   * Closes the link chat message dialog and resets local variables.
   */
  public closeLinkChatMessageDialog() {
    this.linkChatMessageId = null;
    this.linkChatMessageToJobId = null;
    this.showLinkJobDialog = false;
    this.awaitingLinkMessageResponse = false;
  }

  /**
   * Links a chat message with a job.
   */
  public async linkMessageWithJob() {
    if (!this.linkChatMessageId || !this.linkChatMessageToJobId) {
      return;
    }
    const foundChatMessage = this.messageList.find(
      (c) => c._id === this.linkChatMessageId,
    );
    if (foundChatMessage) {
      this.awaitingLinkMessageResponse = true;
      const result = await this.driverMessageStore.addChatMessageToJob(
        foundChatMessage,
        this.linkChatMessageToJobId,
      );
      if (!result) {
        showNotification(
          'Something went wrong. The message could not be linked to the job. Please try again later.',
        );
      }
      this.closeLinkChatMessageDialog();
    }
  }
  /**
   * Requests the chat history for the driver.
   */
  public async requestChatHistory() {
    const chatHistoryRequest: ChatHistoryRequest = {
      driverId: this.driverId,
      startEpoch: returnStartOfDayFromEpoch(
        moment().subtract(7, 'days').valueOf(),
      ),
      endEpoch: returnEndOfDayFromEpoch(),
    };
    this.isAwaitingResponse = true;
    // Send request and handle response
    const results =
      await useDriverMessageStore().requestChatMessagesForDriverId(
        chatHistoryRequest,
      );

    if (results) {
      this.handleChatMessagesForDriverId(results);
    } else {
      showNotification(
        'Something went wrong. The messages could not be received. Please try again soon.',
      );
      this.messageList = [];
    }
    this.isAwaitingResponse = false;
  }

  /**
   * Scrolls the conversation to the bottom.
   */
  public scrollToBottom() {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    this.scrollTimeout = setTimeout(() => {
      this.$nextTick(() => {
        const container = this.$refs['message-contents-div'] as HTMLElement;
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          });
        }
      });
    }, 50);
  }

  /**
   * Handles the received chat message. Used for new messages from drivers and
   * operations, email updates, and updates to job associations.
   * @param payload The chat message payload.
   */
  public handleReceivedChatMessage(payload: ChatMessage | null) {
    if (
      payload &&
      (payload.senderId === this.driverId ||
        payload.receiverId === this.driverId)
    ) {
      // Init chat message
      const chatMessage: ChatMessage = handleChatMessage(payload);
      const foundIndex = this.messageList.findIndex(
        (m) => m._id === chatMessage._id,
      );
      // Replace existing entry
      if (foundIndex !== -1) {
        this.messageList.splice(foundIndex, 1, chatMessage);
      } else {
        // Add to list and jump to bottom of list
        this.messageList.push(chatMessage);
        this.scrollToBottom();
        // Remove the highlight after a delay
        setTimeout(() => {
          chatMessage.isNewChat = false;
        }, 2000); // adjust delay as needed
      }
    }
  }

  /**
   * Mitt listener for all incoming chat messages and chat message updates. Adds
   * it to the local chat message list if it doesn't already exist, otherwise
   * replaces it.
   */
  private handleBulkMessageResponse(response: BulkChatMessageResponse | null) {
    if (!response?.chatMessages) {
      return;
    }
    // Filter the bulk message receiverIds and find if any
    // included this driver. If so, insert it into the list.
    response.chatMessages.forEach((message: ChatMessage) => {
      if (message.receiverId === this.driverId) {
        this.handleReceivedChatMessage(message);
      }
    });
  }

  // function to copy text from message bubble.
  public copyMessage = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      console.log('Message copied to clipboard!');
    });
  };

  public beforeMount() {
    this.requestChatHistory();
    if (this.$listeners.jumpToHistoryComponent) {
      this.canJumpToHistory = true;
    }
    // Add listeners for the various chat message APIs
    // NOTE: Is there a cleaner way to handle this?
    Mitt.on('receivedMessage', this.handleReceivedChatMessage);
    Mitt.on('publishedMessage', this.handleReceivedChatMessage);
    Mitt.on('updatedMessage', this.handleReceivedChatMessage);
    Mitt.on('removedChatMessage', this.handleReceivedChatMessage);
    Mitt.on('addedChatMessage', this.handleReceivedChatMessage);
    Mitt.on('receivedBulkChatMessageResponse', this.handleBulkMessageResponse);
  }

  public beforeDestroy() {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    // Remove listeners for the various chat message APIs
    Mitt.off('receivedMessage', this.handleReceivedChatMessage);
    Mitt.off('publishedMessage', this.handleReceivedChatMessage);
    Mitt.off('updatedMessage', this.handleReceivedChatMessage);
    Mitt.off('removedChatMessage', this.handleReceivedChatMessage);
    Mitt.off('addedChatMessage', this.handleReceivedChatMessage);
    Mitt.off('receivedBulkChatMessageResponse', this.handleBulkMessageResponse);
  }

  public mounted() {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    this.scrollToBottom();
  }
}
