<section class="address-search-au">
  <span
    class="no-address-txt"
    v-if="addressAddJob.length >= 0 && searchInput !== null && isClientPortal"
  >
    If the address you are looking for is not appearing in the list, please
    select an address nearby and leave a note with the details of the address or
    notify
    {{ this.companyDetailsStore.divisionDetails?.divisionShortName }} of the
    issue.</span
  >
  <v-layout
    align-center
    justify-space-between
    class="pb-1"
    v-if="!hideHeadingRow"
  >
    <span class="address-component__heading"
      >{{ selectedViewTypeHeading }}
    </span>
    <div>
      <v-menu left :attach="attach">
        <template v-slot:activator="{ on: menu }">
          <v-tooltip bottom :attach="attach">
            <template #activator="{ on: tooltip }">
              <v-btn
                flat
                icon
                v-on="{ ...tooltip, ...menu }"
                :disabled="formDisabled || disableMenuOptions"
                tabindex="-1"
                class="ma-0"
              >
                <v-icon size="22">far fa-ellipsis-v</v-icon>
              </v-btn>
            </template>
            <span>Address Search Options</span>
          </v-tooltip>
        </template>
        <v-list class="v-list-custom" dense>
          <v-list-tile
            v-for="(item, index) in menuOptions"
            :key="item.id"
            dense
            @click="menuItemSelected(item.value)"
          >
            <v-list-tile-avatar class="pa-0">
              <v-icon size="16">{{ item.icon }}</v-icon>
            </v-list-tile-avatar>
            <v-list-tile-title class="pr-2">{{
              item.longName
            }}</v-list-tile-title>
          </v-list-tile>
          <v-list-tile
            dense
            @click="addReturnToFirstPud"
            v-if="numberOfExistingStops > 0"
          >
            <v-list-tile-avatar class="pa-0">
              <v-icon size="16">fas fa-exchange-alt</v-icon>
            </v-list-tile-avatar>
            <v-list-tile-title class="pr-2"
              >Return To First Stop</v-list-tile-title
            >
          </v-list-tile>
          <v-list-tile
            dense
            @click="addReturnToDefaultDispatchLocation"
            v-if="hasDefaultDispatchAddress"
          >
            <v-list-tile-avatar class="pa-0">
              <v-icon size="16">fas fa-exchange-alt</v-icon>
            </v-list-tile-avatar>
            <v-list-tile-title class="pr-2"
              >Return to Default Dispatch Location</v-list-tile-title
            >
          </v-list-tile>
          <v-list-tile
            dense
            @click="addPreloadLeg"
            v-if="enableAddPreload && numberOfExistingStops > 0"
          >
            <v-list-tile-avatar class="pa-0">
              <v-icon size="16">fas fa-exchange-alt</v-icon>
            </v-list-tile-avatar>
            <v-list-tile-title class="pr-2"
              >Add Final Preload Stop</v-list-tile-title
            >
          </v-list-tile>
          <StreetViewLink
            :coordinates="address.geoLocation"
            :isTile="true"
          ></StreetViewLink>
        </v-list>
      </v-menu>
    </div>
  </v-layout>

  <v-layout align-start>
    <!--Nickname elements-->
    <v-slide-x-reverse-transition mode="out-in" hide-on-leave>
      <v-layout
        row
        wrap
        align-center
        v-if="viewTypeString === 'nickname'"
        class="mb-1"
      >
        <v-flex md12>
          <v-layout row align-center mb-2>
            <v-autocomplete
              label="Please Select From Clients Common Addresses"
              :items="filteredNickNameList"
              color="orange"
              :return-object="true"
              v-model="addressProp"
              single-line
              outline
              class="v-solo-custom"
              browser-autocomplete="off"
              auto-select-first
              :item-value="item => item._id"
              :item-text="item => `${item.addressNickname}`"
              :search-input.sync="updateSearch"
              @update:search-input="updateSearch"
              @change="onAddressChange"
              hide-details
              hide-selected
              flat
            >
              <template slot="selection" slot-scope="data">
                <span
                  ><strong> {{ data.item.addressNickname }} </strong> -
                  {{ data.item.address.formattedAddress }}</span
                >
              </template>
              <template slot="item" slot-scope="data">
                <span
                  :style="addressProp.formattedAddress == data.item.address.formattedAddress ? 'color: orange !important' : ''"
                  ><strong>{{ data.item.addressNickname }}</strong> -
                  {{ data.item.address.formattedAddress }}</span
                >
              </template>
            </v-autocomplete>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-slide-x-reverse-transition>
    <!--address search elements-->

    <v-layout row wrap v-if="viewTypeString === 'search'">
      <v-flex xs12 v-if="enableSuburbSelect">
        <v-layout row align-center mb-2>
          <v-autocomplete
            class="address-search-auto address-search-auto-complete v-solo-custom"
            label="Suburb Search"
            v-model="suburbSelection"
            :disabled="formDisabled"
            item-text="formattedAddress"
            return-object
            :items="suburbSearchResults"
            :box="boxInput"
            :search-input.sync="suburbInput"
            :placeholder="address.suburb"
            auto-select-first
            type="search"
            autocomplete="off"
            browser-autocomplete="off"
            :suffix="address.postcode"
            :autofocus="setFocus && searchAddress && enableSuburbSelect"
            :solo="soloInput"
            color="orange"
            outline
            hide-details
            flat
          >
            <template slot="item" slot-scope="data">
              <span
                :class="!addressIsInDepotState(data.item.formattedAddress) ? 'greyOutOutsideStateAddress' : ''"
                >{{ data.item.formattedAddress }}</span
              >
            </template>
            <template slot="selection" slot-scope="data">
              <span>{{ data.item.name }}</span>
            </template>
          </v-autocomplete>
        </v-layout>
      </v-flex>
      <v-flex xs12 v-if="!hideFullAddress">
        <v-layout row align-center mb-2>
          <v-autocomplete
            class="address-search-auto address-search-auto-complete v-solo-custom"
            :label="displayLabel"
            v-model="addressId"
            :disabled="formDisabled"
            return-object
            :items="addressAddJob"
            :box="boxInput"
            :hint="soloInput ? displayLabel : ''"
            :persistent-hint="soloInput"
            :placeholder="addressSearchPlaceholder"
            @change="searchItemSelected"
            item-text="formattedAddress"
            :class="[{'form-field-required' : addressIsRequired}, {'solo-input-disable-display' : soloInput && formDisabled && !isClientPortal}, {'v-solo-custom' : soloInput && !isClientPortal}]"
            :rules="addressIsRequired ? [validate.required] : []"
            :search-input.sync="searchInput"
            :allow-overflow="false"
            browser-autocomplete="off"
            type="search"
            autocomplete="off"
            :menu-props="{closeOnContentClick:true}"
            :autofocus="setFocus && searchAddress && !enableSuburbSelect"
            :solo="soloInput"
            color="orange"
            outline
            :error="errorMessages.length > 0"
            :error-messages="errorMessages"
            :attach="attach"
            hide-details
            flat
          >
            <v-list-tile
              slot="prepend-item"
              v-for="address of addressAddJob"
              :key="address.id"
              @click="searchItemSelected(address)"
              :class="!addressIsInDepotState(address.formattedAddress) ? 'greyOutOutsideStateAddress' : ''"
            >
              <v-list-tile-content>
                <v-list-tile-title>
                  {{ address.formattedAddress }}</v-list-tile-title
                >
              </v-list-tile-content>
            </v-list-tile>
            <v-list-tile slot="prepend-item" v-if="addressAddJob.length === 0">
              <v-list-tile-content>
                <v-list-tile-title> No data available</v-list-tile-title>
              </v-list-tile-content>
            </v-list-tile>
            <template slot="selection" slot-scope="data">
              <span v-if="enableSuburbSelect"
                >{{ address.addressLine1 }} {{ address.addressLine2 }}</span
              >
              <span v-else>{{ data.item.formattedAddress }}</span>
            </template>

            <template slot="item" slot-scope="data">
              <v-list-tile style="display: none"> </v-list-tile>
            </template>
            <template v-slot:no-data>
              <v-list-tile style="display: none"> </v-list-tile>
            </template>
          </v-autocomplete>
          <!--add nickname button enabled when address is selected-->
          <span
            v-if="enableNicknamedAddress || enableAddNickNameAddress"
            :class="soloInput ? 'pt-3' : ''"
          >
            <v-slide-x-reverse-transition mode="out-in">
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <v-icon
                    @click="showNickNameInput(true)"
                    class="pl-2"
                    v-on="on"
                    size="24"
                    :disabled="!address.addressLine1 || address.geoLocation[0] === 0 || address.geoLocation[1] === 0 || nicknameAddressAlreadyExists || formDisabled"
                    >far fa-plus-circle</v-icon
                  >
                </template>
                <span>Add to Common Addresses</span>
              </v-tooltip>
            </v-slide-x-reverse-transition>
          </span>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-layout>

  <!-- Client Common Address Dialog - add location name and save -->
  <GDialog
    ref="commonAddressMaintenanceRef"
    v-if="nickNameInput && clientCommonAddress && (enableNicknamedAddress || enableAddNickNameAddress)"
    :width="'500px'"
    :title="'Client Common Address'"
    :confirmBtnText="'Save common address'"
    :isDelete="false"
    :confirmDisabled="isLoadingClientCommonAddressSave"
    :isLoading="isLoadingClientCommonAddressSave"
    @closeDialog="showNickNameInput(false)"
    @confirm="saveClientCommonAddress"
  >
    <div class="pa-3">
      <GTitle
        :title="'Location Name Required'"
        :subtitle="address.formattedAddress"
      />
      <GTextField
        v-model="clientCommonAddress.addressNickname"
        :placeholder="'Location Name'"
        :rules="[validate.required]"
        :disabled="false"
        :autofocus="true"
      ></GTextField>
    </div>
  </GDialog>

  <!-- manually enter address input elements -->
  <v-form ref="manualAddressRef" v-if="viewTypeString === 'manual'">
    <v-layout class="mb-2" row wrap>
      <v-flex xs12>
        <v-layout row wrap>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">Country / Region</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 pb-2>
            <v-select
              :items="countries"
              v-model="manualAddressObj.country"
              item-text="name"
              item-value="name"
              :solo="soloInput"
              label="Country"
              :rules="[validate.required]"
              class="v-solo-custom form-field-required"
              color="orange"
              hide-details
              :attach="attach"
              outline
              flat
            ></v-select>
          </v-flex>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">Street Address</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 pb-2>
            <v-text-field
              v-model="manualAddressObj.addressLine1"
              label="Address Line 1"
              :solo="soloInput"
              class="v-solo-custom"
              color="orange"
              hide-details
              outline
              flat
            ></v-text-field>
          </v-flex>
          <v-flex md12 pb-2>
            <v-text-field
              v-model="manualAddressObj.addressLine2"
              label="Address Line 2"
              :solo="soloInput"
              class="v-solo-custom"
              color="orange"
              hide-details
              outline
              flat
            >
            </v-text-field>
          </v-flex>
          <v-flex md6 pb-2>
            <v-text-field
              v-model="manualAddressObj.addressLine3"
              label="Address Line 3"
              :solo="soloInput"
              class="v-solo-custom"
              color="orange"
              hide-details
              outline
              flat
            >
            </v-text-field>
          </v-flex>
          <v-flex md6 pl-1 pb-2>
            <v-text-field
              :solo="soloInput"
              flat
              v-model="manualAddressObj.addressLine4"
              label="Address Line 4"
              class="v-solo-custom"
              color="orange"
              hide-details
              outline
              flat
            >
            </v-text-field>
          </v-flex>
          <v-flex md4 pb-2>
            <v-text-field
              v-model="manualAddressObj.suburb"
              label="Suburb"
              :solo="soloInput"
              flat
              :rules="[validate.required]"
              class="v-solo-custom form-field-required"
              color="orange"
              hide-details
              outline
              flat
            ></v-text-field>
          </v-flex>
          <v-flex md4 px-2 pb-2>
            <v-select
              :items="selectedCountry ? selectedCountry.states : []"
              v-model="manualAddressObj.state"
              item-text="iso"
              item-value="iso"
              :solo="soloInput"
              label="State"
              :disabled="!selectedCountry"
              :rules="[validate.required]"
              class="v-solo-custom form-field-required"
              hide-details
              outline
              color="orange"
              :attach="attach"
              flat
            ></v-select>
          </v-flex>
          <v-flex md4 pb-2>
            <v-text-field
              v-model="manualAddressObj.postcode"
              label="Postcode"
              :solo="soloInput"
              :rules="[validate.required]"
              class="v-solo-custom form-field-required"
              color="orange"
              hide-details
              outline
              flat
            ></v-text-field>
          </v-flex>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">Latitude / Longitude</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 :key="selectedCountry.name" v-if="selectedCountry">
            <v-layout>
              <v-flex md6 pr-1>
                <v-text-field
                  v-model.number="manualAddressObj.geoLocation[1]"
                  label="Latitude"
                  :solo="soloInput"
                  flat
                  :rules="[validate.required, validate.latitude, validateLatitude]"
                  class="v-solo-custom form-field-required"
                  color="orange"
                  hide-details
                  outline
                  flat
                ></v-text-field>
              </v-flex>
              <v-flex md6 pl-1>
                <v-text-field
                  v-model.number="manualAddressObj.geoLocation[0]"
                  label="Longitude"
                  :solo="soloInput"
                  flat
                  :rules="[validate.required, validate.longitude, validateLongitude]"
                  class="v-solo-custom form-field-required"
                  color="orange"
                  hide-details
                  outline
                  flat
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex xs12 pt-4>
        <v-layout justify-center>
          <v-btn flat color="white" small outline @click="clearManualAddress"
            >Clear</v-btn
          >
          <v-spacer></v-spacer>
          <v-btn flat color="error" small @click="cancelManualAddress"
            >Cancel</v-btn
          >
          <v-btn color="primary" depressed small @click="saveManualAddress">
            <span class="pr-1">Use</span> Address
          </v-btn>
        </v-layout>
      </v-flex>
      <v-flex md12 pt-2><v-divider></v-divider></v-flex>
    </v-layout>
  </v-form>

  <!-- pin drop elements -->
  <v-layout class="mb-2" row wrap v-if="viewTypeString === 'pindrop'">
    <v-flex md12>
      <PinDropCoordinates
        :address="address"
        :flatInput="soloInput"
        :soloInput="soloInput"
        :isDisabled="formDisabled"
        :mapCenter="[depotCoordinates[0], depotCoordinates[1]]"
        @locationSelected="cancelPinDropInput"
      >
      </PinDropCoordinates>
    </v-flex>
  </v-layout>
</section>
