<section class="driver-message-component app-theme__center-content--body">
  <v-layout
    class="task-bar app-theme__center-content--header no-highlight pa-0"
  >
    <v-tooltip right v-if="currentTab === 'CONVERSATION'">
      <template v-slot:activator="{ on }">
        <v-btn
          flat
          v-on="on"
          icon
          @click="currentTab = 'CURRENT'"
          class="ma-0 pa-0"
        >
          <v-icon size="14" color="grey lighten-1">fal fa-arrow-left</v-icon>
        </v-btn>
      </template>
      Return to Messages
    </v-tooltip>
    <span
      v-if="currentTab === 'CURRENT' || !conversationHeader"
      style="padding: 6px 8px"
      >Driver Chat</span
    >
    <span v-else style="padding: 6px 8px">{{conversationHeader}}</span>

    <v-spacer></v-spacer>
    <ChatAlertDialog
      v-if="$vuetify.breakpoint.lgAndUp"
      jobProgressAlertType="APPROACHING_PICKUP_TIME"
    ></ChatAlertDialog>
    <ChatAlertDialog
      v-if="$vuetify.breakpoint.lgAndUp"
      jobProgressAlertType="AWAITING_ACCEPT"
    ></ChatAlertDialog>
    <ChatAlertDialog
      v-if="$vuetify.breakpoint.lgAndUp"
      jobProgressAlertType="LOAD_TIME"
    ></ChatAlertDialog>

    <div>
      <v-menu right>
        <template v-slot:activator="{ on: menu }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on: tooltip }">
              <v-btn flat icon v-on="{ ...tooltip, ...menu }" class="ma-0">
                <v-icon size="14" color="grey lighten-1"
                  >fas fa-ellipsis-v
                </v-icon>
              </v-btn>
            </template>
            <span>View Additional Actions</span>
          </v-tooltip>
        </template>
        <v-list class="v-list-custom" dense>
          <v-list-tile @click="setShowDriverChatHistoryDialog(true)">
            <v-list-tile-avatar>
              <v-icon size="16">fas fa-history</v-icon>
            </v-list-tile-avatar>
            <v-list-tile-title class="pr-2">
              Search Driver Message History
            </v-list-tile-title>
          </v-list-tile>
          <v-list-tile @click="jobProgressAlertHistoryDialogController = true">
            <v-list-tile-avatar>
              <v-icon size="16">far fa-alarm-exclamation</v-icon>
            </v-list-tile-avatar>
            <v-list-tile-title class="pr-2">
              View Job Alert History
            </v-list-tile-title>
          </v-list-tile>

          <v-list-tile @click="setShowDriverBulkMessageDialog(true)">
            <v-list-tile-avatar>
              <v-icon size="16">fal fa-broadcast-tower</v-icon>
            </v-list-tile-avatar>
            <v-list-tile-title class="pr-2">
              Message All Drivers
            </v-list-tile-title>
          </v-list-tile>
          <OperationsSettingsDialog
            key="message-list-settings-dialog"
            buttonText="Driver Message Settings"
            title="Driver Messages - Settings"
            @confirm="tableSettingsUpdated"
            :buttonDisabled="false"
            buttonColor="teal accent-3"
            faIconName="fal fa-sliders-h"
            :dialogWidth="500"
            :isListTile="true"
            :useLeadingIcon="true"
            :settingsList="messageListSettings"
            :defaultSettingsList="messageListSetting"
            :allowExclusions="true"
          >
          </OperationsSettingsDialog>
        </v-list>
      </v-menu>
    </div>
  </v-layout>

  <div class="tbl-content" v-if="currentTab === 'CURRENT'">
    <table cellpadding="0" cellspacing="0">
      <thead v-if="$vuetify.breakpoint.lgAndUp">
        <tr>
          <th v-for="header in headers" :key="header.text">{{header.text}}</th>
        </tr>
      </thead>
      <tbody>
        <template v-for="(item, index) in chatMessageList">
          <tr
            v-if="!item.isSystemMessage"
            @click.stop="chatMessageSelected(item)"
            v-on:keyup.enter="chatMessageSelected(item, true)"
            v-on:keyup.down="tabHandler('down', item)"
            v-on:keyup.up="tabHandler('up', item)"
            v-on:keyup.delete="hideChatMessage(item, index)"
            v-on:keyup.escape="removeElementFocus"
            :key="item._id"
            :tabindex="index + 1"
            :id="item._id"
            class="chat-message"
            :class="[selectedChatMessageId === item._id ? 'selected' : '']"
          >
            <template v-if="$vuetify.breakpoint.lgAndUp">
              <td class="text-xs-left">
                {{ messagesAllSameDay ? returnFormattedTime(item.timestamp,
                `HH:mm`) : returnFormattedTime(item.timestamp, `HH:mm DD/MM/YY`)
                }}
              </td>
              <td class="text-xs-left">
                {{ recurringJobIdMap.get(item.jobId) ?
                recurringJobIdMap.get(item.jobId) : item.jobId ? item.jobId :
                '--' }}
              </td>
              <td
                class="text-xs-left"
                :class="item.isOperationsMessage ? 'accent-text--primary' : ''"
              >
                <span v-if="item.isOperationsMessage">
                  {{item.senderName ? item.senderName :
                  returnDriverName(item.senderId)}}
                </span>
                <span v-else>
                  {{item.fleetAssetId ? fleetCsrAssignedId(item.fleetAssetId) :
                  'Unknown'}} ({{item.senderName ? item.senderName :
                  returnDriverName(item.senderId)}})
                </span>
              </td>
              <td class="text-xs-left" :class="{'pr-4': item.isPinned}">
                {{ item.content }}
              </td>
            </template>
            <template v-else>
              <td
                class="text-xs-left"
                :class="item.isOperationsMessage ? 'accent-text--primary' : ''"
              >
                <v-layout column align-center>
                  <span v-if="item.isOperationsMessage">
                    {{item.senderName ? item.senderName :
                    returnDriverName(item.senderId)}}
                  </span>
                  <span v-else>
                    {{item.fleetAssetId ? fleetCsrAssignedId(item.fleetAssetId)
                    : 'Unknown'}}
                  </span>
                  <span style="font-size: 8px; white-space: nowrap">
                    {{ returnTimeFromNow(item.timestamp, `HH:mm`) }}
                  </span>
                  <span style="font-size: 8px; white-space: nowrap">
                    {{ recurringJobIdMap.get(item.jobId) ?
                    recurringJobIdMap.get(item.jobId) : item.jobId ? item.jobId
                    : '' }}
                  </span>
                </v-layout>
              </td>
              <td
                class="text-xs-left"
                :class="{'pr-4': item.isPinned}"
                colspan="3"
              >
                {{ item.content }}
              </td>
            </template>
            <div
              v-if="item.isPinned"
              class="pr-1"
              style="position: absolute; right: 0px"
            >
              <v-tooltip bottom class="pa-0">
                <template v-slot:activator="{ on }">
                  <v-btn
                    flat
                    v-on="on"
                    icon
                    small
                    @click="hideChatMessage(item, -1)"
                    class="ma-0 pa-0"
                  >
                    <v-icon color="orange" size="11">fas fa-thumbtack</v-icon>
                  </v-btn>
                </template>
                Unpin Message
              </v-tooltip>
            </div>
          </tr>
          <template v-else>
            <tr
              class="alert-message"
              :class="[selectedChatMessageId === item._id ? 'selected' : '', (!!associatedAlerts && associatedAlerts.length && selectedChatMessageId === item._id) ? 'alerts-expanded' : '']"
              @click.stop="chatMessageSelected(item)"
              v-on:keyup.enter="chatMessageSelected(item, true)"
              v-on:keyup.down="tabHandler('down', item)"
              v-on:keyup.up="tabHandler('up', item)"
              v-on:keyup.delete="hideChatMessage(item, index)"
              v-on:keyup.escape="removeElementFocus"
              :key="item._id"
              :tabindex="index + 1"
              :id="item._id"
            >
              <td colspan="4">
                <v-layout align-center pl-1 pr-4>
                  <v-flex>
                    <v-layout align-center>
                      <span class="px-2"
                        >{{returnFormattedTime(item.timestamp, `HH:mm`)}}</span
                      >
                      <v-divider> </v-divider>
                    </v-layout>
                  </v-flex>
                  <div class="job-progress-alert-chip">{{ item.content }}</div>
                  <v-flex>
                    <v-divider> </v-divider>
                  </v-flex>
                </v-layout>
              </td>
            </tr>
            <tr
              v-if="!!associatedAlerts && associatedAlerts.length && selectedChatMessageId === item._id"
              v-for="(alert, alertIndex) in associatedAlerts"
              :key="alert._id"
              tabindex="-1"
              :class="{'first-row': alertIndex === 0,
                'last-row': alertIndex === associatedAlerts.length - 1,
                'selected-child': (!!alert.jobId && currentSelectedJobId === alert.jobId)}"
              class="chat-message"
              @click="(!!alert.jobId && currentSelectedJobId !== alert.jobId) ? selectJobProgressAlert(alert) : null"
            >
              <td class="text-xs-left">
                {{ returnFormattedTime(alert.epochTime, `HH:mm`) }}
              </td>
              <td class="text-xs-left" :class="[]">
                <span class="job-id">
                  {{ recurringJobIdMap.get(alert.jobId) ?
                  recurringJobIdMap.get(alert.jobId) : alert.jobId ? alert.jobId
                  : '--' }}
                </span>
              </td>
              <td class="text-xs-left">
                <span> {{returnDriverName(alert.driverId)}} </span>
              </td>
              <td class="text-xs-left">{{ alert.alertMessage }}</td>
            </tr>
          </template>
        </template>
      </tbody>
    </table>
  </div>

  <!-- =================================================================== -->
  <!-- BY DRIVER -->
  <!-- =================================================================== -->

  <DriverConversation
    :key="currentConversationSenderId"
    :driverId="currentConversationSenderId"
    @jumpToHistoryComponent="jumpToHistoryComponent"
    v-if="currentTab === 'CONVERSATION'"
  >
  </DriverConversation>

  <v-dialog
    v-model="showDriverChatHistoryDialog"
    persistent
    width="60%"
    content-class="v-dialog-custom"
  >
    <v-layout
      justify-space-between
      align-center
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>Driver Chat History</span>
      <v-spacer></v-spacer>
      <div
        class="app-theme__center-content--closebutton"
        @click="setShowDriverChatHistoryDialog(false)"
      >
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>
    <v-layout class="app-theme__center-content--body dialog-content">
      <DriverChatHistory v-if="showDriverChatHistoryDialog" />
    </v-layout>
  </v-dialog>
  <v-dialog
    v-model="jobProgressAlertHistoryDialogController"
    persistent
    width="60%"
    content-class="v-dialog-custom"
  >
    <v-layout
      justify-space-between
      align-center
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>Job Alert History</span>
      <v-spacer></v-spacer>
      <div
        class="app-theme__center-content--closebutton"
        @click="jobProgressAlertHistoryDialogController = false"
      >
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>
    <v-layout class="app-theme__center-content--body dialog-content">
      <JobProgressAlertHistory v-if="jobProgressAlertHistoryDialogController" />
    </v-layout>
  </v-dialog>
  <v-dialog
    v-model="showDriverBulkMessageDialog"
    persistent
    width="30%"
    content-class="v-dialog-custom"
  >
    <v-layout
      justify-space-between
      align-center
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>Message All Drivers</span>
      <v-spacer></v-spacer>
      <div
        class="app-theme__center-content--closebutton"
        @click="setShowDriverBulkMessageDialog(false)"
      >
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>
    <v-layout class="app-theme__center-content--body dialog-content">
      <DriverBulkMessage
        v-if="showDriverBulkMessageDialog"
        @setShowDriverBulkMessageDialog="setShowDriverBulkMessageDialog"
      />
    </v-layout>
  </v-dialog>
</section>
