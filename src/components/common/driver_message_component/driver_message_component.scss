.driver-message-component {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .task-bar {
    // border-radius: 10px 10px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .messages-header__tab-container {
    height: 30px;
    max-height: 30px;
    .messages-header__tab-item {
      transition: 0.1s;
      text-align: center;
      padding: 4px;
      border-style: solid;
      border-color: #403f46;
      border-width: 0px 1px 1px 1px;

      &.middle-item {
        border-width: 0px 0px 1px 0px;
      }

      &.selected {
        .messages-header__tab-item--text {
          font-size: $font-size-medium;
          color: var(--text-color);
          text-transform: uppercase;
          font-weight: 600;
        }
      }

      .messages-header__tab-item--text {
        font-size: $font-size-medium;
        color: grey;
        text-transform: uppercase;
        font-weight: 600;
      }

      &:hover {
        cursor: pointer;
        background-color: #39373f;
      }
    }
  }

  .message-contents {
    padding: 8px 12px;

    .message-bubble-container {
      .message-details-row {
        padding-top: 6px;

        .username {
          padding: 0px 6px;
          font-family: $sub-font-family;
          font-size: $font-size-12;
          text-transform: uppercase;
          font-weight: 600;
        }
        .datetime {
          font-size: $font-size-12;
          color: var(--light-text-color);
        }
      }

      &.operations {
        align-items: flex-end;
        .message-details-row {
          flex-direction: row-reverse;
        }
        .message-bubble {
          background-color: rgb(35, 64, 94);

          .message-menu {
            right: 100%;
            .message-menu-row {
              flex-direction: row-reverse;
            }
          }
        }
      }
      &.driver {
        align-items: start;
        .message-bubble {
          background-color: rgb(72, 71, 80);
          .message-menu {
            left: 100%;
          }
        }
      }
      .message-bubble {
        position: relative;
        display: inline-block;
        max-width: 80%;
        padding: 8px 10px;
        width: fit-content;
        border-radius: 16px;
        margin-bottom: 1px;
        font-size: 0.95em;
        border: 2px solid transparent;

        &.pinned {
          border-color: $toast-warning-bg;
        }

        .message-menu {
          position: absolute;
          display: none;
          top: 0px;
          .linked-jobid {
            background-color: rgba(72, 71, 80, 0.628);
            border: 1px solid rgb(72, 71, 80);
            // font-size: $font-size-12;
            font-style: italic;
            padding: 1px 8px;
            border-radius: 3px;
            font-family: $sub-font-family;
          }
        }

        &:hover {
          cursor: pointer;
          filter: brightness(110%);
          .message-menu {
            display: unset;
          }
        }
      }
    }
  }

  .chat-text-field-input-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }

  .tbl-content {
    height: calc((100vh - (40px + 20px)) / 2);
    overflow-y: auto;
    overflow-x: hidden;
    margin-top: 0px;

    table {
      width: 100%;
      border-collapse: collapse;

      /* Set header to stick to the top of the container. */
      thead tr th {
        position: sticky;
        top: 0;
        z-index: 5;
      }
    }

    thead {
      th {
        padding: 5px 12px;
        text-align: left;
        font-weight: 600;
        font-size: $font-size-11;
        color: var(--text-color);
        text-transform: uppercase;
        font-family: $sub-font-family;
        background-color: var(--background-color-250);
        box-shadow: 0px 0px 0px 2px #5b5b5b;
      }
    }

    tbody {
      .expand-enter-active,
      .expand-leave-active {
        transition: all 0.3s ease;
      }
      .expand-enter,
      .expand-leave-to {
        opacity: 0;
        transform: scaleY(0.5);
      }
      th {
        padding: 5px 12px;
        text-align: left;
        font-weight: 500;
        font-size: $font-size-11;
        color: var(--text-color);
        text-transform: uppercase;
      }
      td {
        text-align: left;
        vertical-align: middle;
        font-weight: 400;
        color: var(--light-text-color);
        font-size: $font-size-12;
        border: 0px;

        // border-bottom: solid 1px rgba(255, 255, 255, 0.1);
      }
      tr {
        background: none;
        transition: 0s;
        max-width: 100%;
        position: relative;
        $active-accent: $highlight;
        $active-bg: $bg-light;

        max-height: 30px;

        &:nth-child(odd) {
          background-color: #e6e6ff0a;
        }

        &.chat-message {
          &.first-row {
            td {
              border-top: solid 2px $border-light-client;
            }
          }
          &.last-row {
            td {
              border-bottom: solid 2px $border-light-client;
            }
          }
          &.selected-child {
            .job-id {
              color: $active-accent;
              // font-weight: bold;
            }
          }

          max-height: 30px;
          &:hover {
            background-color: rgba(255, 255, 255, 0.058);
            cursor: pointer;
          }
          td {
            padding: 5px 12px;
            border-bottom: solid 1px rgba(255, 255, 255, 0.1);
          }

          &.selected {
            background-color: $active-bg;

            td {
              // font-weight: 300;
              color: black;
              border: none;
            }
          }
        }

        &.alert-message {
          max-height: 30px;
          background: none;
          &.alerts-expanded {
            .job-progress-alert-chip {
              border-bottom-left-radius: 0px;
              border-bottom-right-radius: 0px;
            }
          }

          td {
            padding-top: 6px;
          }
          .job-progress-alert-chip {
            position: relative;
            margin-inline: 0px 8px;
            padding: 4px 12px;
            border-radius: 12px;
            background-color: rgb(73, 73, 78);
            color: $border-light-client;
            font-weight: 500;
            border: 1px solid transparent;

            &:hover {
              cursor: pointer;
              border: 1px solid #c2c2d757;
            }
          }
          &.selected {
            .job-progress-alert-chip {
              background-color: $active-bg;
              color: black;
            }
          }
        }
        &:focus {
          border: none;
          outline: none;
        }
      }
    }
  }
}
