import { Component, Vue, Watch } from 'vue-property-decorator';

import {
  returnEndOfDayFromEpoch,
  returnFormattedTime,
  returnNaturalDateTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import { useRootStore } from '@/store/modules/RootStore';
import moment from 'moment-timezone';

import { useOperationsStore } from '@/store/modules/OperationsStore';

import DriverBulkMessage from '@/components/common/driver_bulk_message.vue';
import DriverChatHistory from '@/components/common/driver_chat_history/driver_chat_history.vue';
import JobProgressAlertHistory from '@/components/common/job_progress_alert_history/index.vue';
import OperationsSettingsDialog from '@/components/common/ui-elements/operations_settings_dialog.vue';
import OperationsDashboardSetting, {
  driverMessageDefaultSettings,
} from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import { JobProgressAlertType } from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertType';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';

import ChatAlertDialog from '@/components/common/chat_alert_dialog/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import JobProgressAlert from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlert';
import JobProgressAlertHistoryRequest from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertHistoryRequest';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';

import DriverConversation from '@/components/common/driver_conversation/index.vue';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import Mitt from '@/utils/mitt';

enum MessageTabs {
  CURRENT = 'CURRENT',
  CONVERSATION = 'CONVERSATION',
}
@Component({
  components: {
    DriverChatHistory,
    JobProgressAlertHistory,
    DriverBulkMessage,
    OperationsSettingsDialog,
    ChatAlertDialog,
    ContentDialog,
    DriverConversation,
  },
})
export default class DriverMessageComponent extends Vue {
  public fleetAssetStore = useFleetAssetStore();
  public companyDetailsStore = useCompanyDetailsStore();
  public driverMessageStore = useDriverMessageStore();
  public operationsStore = useOperationsStore();
  public driverDetailsStore = useDriverDetailsStore();
  public messageListSetting: OperationsDashboardSetting[] =
    driverMessageDefaultSettings;

  public returnFormattedTime = returnFormattedTime;
  public returnNaturalDateTime = returnNaturalDateTime;

  public unsentMessage: string = '';
  public settingsView: boolean = false;

  public showDriverAlerts: boolean = false;
  public showMessagePreview: boolean = false;

  public showJobProgressAlertHistoryDialog: boolean = false;
  public showDriverChatHistoryDialog: boolean = false;
  public showDriverBulkMessageDialog: boolean = false;

  public selectedChatMessageId: string | null = null;
  public showingAlertsForChatId: string | null = null;
  public associatedAlerts: JobProgressAlert[] | null = null;

  public canJumpToHistory: boolean = false;

  get jobProgressAlertHistoryDialogController() {
    return this.showJobProgressAlertHistoryDialog;
  }
  set jobProgressAlertHistoryDialogController(value: boolean) {
    this.showJobProgressAlertHistoryDialog = value;
  }

  get currentConversationSenderId() {
    return this.driverMessageStore.currentConversationId;
  }
  set currentConversationSenderId(value: string) {
    this.driverMessageStore.setCurrentConversationId(value);
    if (!!value) {
      this.selectedChatMessageId = null;
    }
  }

  get currentTab() {
    return !!this.currentConversationSenderId
      ? MessageTabs.CONVERSATION
      : MessageTabs.CURRENT;
  }
  set currentTab(value: MessageTabs) {
    if (value === MessageTabs.CURRENT) {
      this.currentConversationSenderId = '';
    }
  }
  // Sort messages for driver messages (role ID 10) or system notifications (role ID -1)
  get chatMessageList(): ChatMessage[] {
    const allMessages: ChatMessage[] = this.driverMessageStore.chatMessageList;

    const driverMessages: ChatMessage[] = allMessages.filter((message) => {
      // Check if message is a system message and within list of types that
      // should be displayed
      const isSystemMessage =
        message.isSystemMessage &&
        this.allowedSystemMessageTypes.includes(message.receiverId);
      return (
        message.isDriverMessage || isSystemMessage || message.actionRequired
      );
    });
    if (this.currentTab === MessageTabs.CURRENT) {
      return driverMessages.filter((message) => !message.isActioned);
    } else {
      return driverMessages;
    }
  }
  // If on the Current tab and all messages are today, then return true. Use to
  // determine whether we need to show the full date and time or just the time
  get messagesAllSameDay(): boolean {
    if (this.currentTab === MessageTabs.CONVERSATION) {
      return false;
    }
    const sod = returnStartOfDayFromEpoch();
    const eod = returnEndOfDayFromEpoch();
    return this.chatMessageList.every(
      (c) => c.timestamp >= sod && c.timestamp < eod,
    );
  }

  public headers: TableHeader[] = [
    {
      text: 'Time',
      align: 'left',
      sortable: true,
      value: 'timestamp',
      class: 'chat-table-header',
    },
    {
      text: 'Job#',
      align: 'left',
      sortable: true,
      value: 'jobId',
      class: 'chat-table-header',
    },
    {
      text: 'From',
      align: 'left',
      sortable: true,
      value: 'senderName',
      class: 'chat-table-header',
    },

    {
      text: 'Message',
      align: 'left',
      sortable: true,
      value: 'content',
      class: 'chat-table-header',
    },
  ];

  /**
   * Returns a string representing the time elapsed since the given epoch time in milliseconds.
   *
   * @param epochMs - The epoch time in milliseconds.
   * @returns A string representing the time elapsed since `epochMs`. The string is formatted according to the user's locale.
   */
  public returnTimeFromNow(epochMs: number): string {
    const tz: string = this.companyDetailsStore.userLocale;
    const time = moment(epochMs).tz(tz);

    return time.fromNow();
  }

  // The current settings config for the Message component
  get messageListSettings(): OperationsDashboardSetting[] {
    return JSON.parse(
      JSON.stringify(this.operationsStore.dashboardSettings.messageList),
    );
  }
  // Captures emit from Operations Settings component, and commits it to the store
  public tableSettingsUpdated(settingsList: OperationsDashboardSetting[]) {
    this.operationsStore.updateDashboardSettingsDriverMessage(settingsList);
  }
  // Check if certain settings types exist in Settings and construct a list of
  // the currently enabled settings. Used to determine what messages get shown
  // in the CURRENT messages tab.
  get allowedSystemMessageTypes(): string[] {
    const allowedList: string[] = [];
    if (this.isSettingActive('showJobAlerts')) {
      allowedList.push(JobProgressAlertType.JOB_STATUS_UPDATE);
    }
    if (this.isSettingActive('showStopAlerts')) {
      allowedList.push(JobProgressAlertType.PUD_STATUS_UPDATE);
    }
    if (this.isSettingActive('showAttachmentAlerts')) {
      allowedList.push(JobProgressAlertType.ATTACHMENT_ADDED);
    }
    if (this.isSettingActive('showApproachingPickupTimeAlerts')) {
      allowedList.push(JobProgressAlertType.APPROACHING_PICKUP_TIME);
    }
    if (this.isSettingActive('showLateAcceptAlerts')) {
      allowedList.push(JobProgressAlertType.AWAITING_ACCEPT);
    }
    if (this.isSettingActive('showLoadTimeAlerts')) {
      allowedList.push(JobProgressAlertType.LOAD_TIME);
    }
    if (this.isSettingActive('showFinalLegAlerts')) {
      allowedList.push(JobProgressAlertType.COMPLETED_FINAL_LEG);
    }

    if (this.isSettingActive('showAppNotifications')) {
      allowedList.push(JobProgressAlertType.LOG_IN);
      allowedList.push(JobProgressAlertType.LOG_OUT);
    }
    if (this.isSettingActive('showAppRestartNotifications')) {
      allowedList.push(JobProgressAlertType.APP_RESTART);
    }
    if (this.isSettingActive('showGpsDisabledNotifications')) {
      allowedList.push(JobProgressAlertType.GPS_DISABLED);
    }
    return allowedList;
  }

  // Handle emit from settings dialog component
  public isSettingActive(settingId: string, driverId: string = ''): boolean {
    const settings = this.messageListSettings;
    const foundSetting = settings.find((s) => s.id === settingId);
    if (!foundSetting) {
      return false;
    }
    const isActive =
      foundSetting.active &&
      (!driverId ||
        !foundSetting.excludeIds ||
        !foundSetting.excludeIds.includes(driverId));

    return isActive;
  }

  public chatMessageSelected(
    message: ChatMessage,
    showAdditionalDetails = false,
  ) {
    let changedFocus = false;
    if (!showAdditionalDetails) {
      changedFocus = this.focusChatMessage(message._id);
    }

    // Select jobId in store to update other dashboard components, but only if
    // it's different from the currently selected job
    if (message.jobId && this.operationsStore.selectedJobId !== message.jobId) {
      this.setSelectedJobId(message.jobId);
    }

    // If message is from driver then selected them as active fleetAsset and driver
    if (
      (message.isDriverMessage || message.isSystemMessage) &&
      message.fleetAssetId &&
      message.senderId
    ) {
      this.operationsStore.setSelectedFleetAssetId(message.fleetAssetId);
      this.operationsStore.setSelectedDriverId(message.senderId);
    }
    if (showAdditionalDetails) {
      // If it's a message from operations (in case of pinned message) then use
      // the receiveId to set conversationId. If it's not an operations message
      // then use senderId
      if (message.isOperationsMessage && message.receiverId) {
        this.currentConversationSenderId = message.receiverId;
      } else if (message.isDriverMessage && message.senderId) {
        this.currentConversationSenderId = message.senderId;
      } else if (message.isSystemMessage) {
        this.setAssociatedAlertsFromChatMessage(message);
      }
    }
  }

  /**
   * Handles click on a job progress alert item in the html, for system messages
   * that have associated JobProgressAlerts. Sets the selected job ID in the
   * dashboard and also the driver fleet combination in the driver list
   *
   * @param alert - The job progress alert to be selected.
   */
  public selectJobProgressAlert(alert: JobProgressAlert): void {
    this.operationsStore.setSelectedJobId(alert.jobId);
    this.operationsStore.getFullJobDetails(alert.jobId);
    const fleets = this.returnFleetAssetIdsFromDriverId(alert.driverId);
    if (fleets.length) {
      this.operationsStore.setSelectedDriverId(alert.driverId);
      this.operationsStore.setSelectedFleetAssetId(fleets[0]);
    }
  }

  /**
   * Sets the selected job ID in state and dispatches request for the full job
   * details.
   *
   * @param jobId - The ID of the job to be selected.
   */
  public setSelectedJobId(jobId: number): void {
    if (this.currentSelectedJobId === jobId) {
      return;
    }
    // Close any expanded rows in the job list and allocated work tables
    Mitt.emit('closeDashboardJobRows', 'driver-message');
    this.operationsStore.setSelectedJobId(jobId);
    this.operationsStore.getFullJobDetails(jobId);
  }

  get currentSelectedJobId() {
    return this.operationsStore.selectedJobId;
  }

  /**
   * Sets the associated alerts for a chat message. If the message is a system
   * message and has associated IDs, it retrieves the appropriate alerts from
   * the store and assigns them to the component. If the message is already
   * associated with alerts, it hides them when triggered again.
   *
   * @param message - The chat message to set the associated alerts for.
   */
  public setAssociatedAlertsFromChatMessage(message: ChatMessage): void {
    if (
      !message.isSystemMessage ||
      !message.associatedIds ||
      !message.associatedIds.length ||
      !message._id
    ) {
      this.showingAlertsForChatId = null;
      this.associatedAlerts = null;
      return;
    }
    // If we're already viewing the alerts for this message, then hide them if
    // we trigger it again
    if (
      !!this.showingAlertsForChatId &&
      this.showingAlertsForChatId === message._id
    ) {
      this.showingAlertsForChatId = null;
      this.associatedAlerts = null;
      setTimeout(() => {
        this.focusElement(message._id!);
      }, 10);
      return;
    }
    // Get the appropriate map from the store
    let mapToCheck: Map<number, JobProgressAlert> | null = null;
    switch (message.receiverId) {
      case JobProgressAlertType.LOAD_TIME:
        mapToCheck = this.driverMessageStore.loadTimeProgressAlerts;
        break;
      case JobProgressAlertType.AWAITING_ACCEPT:
        mapToCheck = this.driverMessageStore.acceptProgressAlerts;
        break;
      case JobProgressAlertType.APPROACHING_PICKUP_TIME:
        mapToCheck = this.driverMessageStore.pickupProgressAlerts;
        break;
    }
    if (mapToCheck === null || mapToCheck.size === 0) {
      return;
    }
    const associatedAlerts = Array.from(mapToCheck.values()).filter((j) =>
      message.associatedIds!.includes(j._id),
    );
    this.showingAlertsForChatId = message._id;
    this.associatedAlerts = associatedAlerts;
  }

  // Return the csrAssignedId from fleetAssetId
  public fleetCsrAssignedId(fleetAssetId: string) {
    const csrAssignedId =
      this.fleetAssetStore.csrAssignedIdMap.get(fleetAssetId);
    return csrAssignedId ? csrAssignedId : '-';
  }
  // Return the driver name from supplied driverId
  // This will only be called in cases where message.senderName was empty AND
  // The senderName wasn't populated by the store on load
  public returnDriverName(driverId: string) {
    const foundDriver = this.driverDetailsStore.getDriverFromDriverId(driverId);
    return foundDriver?.displayName ?? '-';
  }

  /**
   * Hides a chat message and performs necessary actions.
   * @param message - The chat message to hide.
   * @param index - The index of the chat message in the list.
   */
  public hideChatMessage(message: ChatMessage, index: number) {
    // Focus next message in list if it exists
    if (index !== -1) {
      const nextMessage = this.chatMessageList[index + 1];
      if (nextMessage) {
        this.focusChatMessage(nextMessage._id);
      }
    }
    // If system message, then just delete from store
    if (message.roleId === -1) {
      this.driverMessageStore.updateSystemChatMessage(message);
    } else {
      // If not a system message, send request to update message in database
      message.actionRequired = false;
      message.isActioned = true;
      this.driverMessageStore.updateChatMessage(message);
    }
  }

  /**
   * Removes the focus from the currently active element.
   */
  public removeElementFocus() {
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
    this.selectedChatMessageId = null;
    this.showingAlertsForChatId = null;
    this.associatedAlerts = null;
  }

  /**
   * Gets the current driver details.
   * @returns The driver details summary or undefined if the current
   * conversation sender ID is not available.
   */
  get conversationHeader(): string | undefined {
    if (!this.currentConversationSenderId) {
      return;
    }
    const driver = this.driverDetailsStore.getDriverFromDriverId(
      this.currentConversationSenderId,
    );
    if (!driver) {
      return;
    }
    const fleetCsrAssignedIds: string[] = this.returnFleetAssetIdsFromDriverId(
      this.currentConversationSenderId,
    ).map((f) => this.fleetCsrAssignedId(f));

    return fleetCsrAssignedIds.length
      ? `${driver.displayName} (${fleetCsrAssignedIds.join(', ')})`
      : driver.displayName;
  }

  /**
   * Returns an array of fleet asset IDs associated with the given driver ID,
   * based on the map of currently selected 'active' drivers in the store.
   * @param driverId - The ID of the driver.
   * @returns An array of fleet asset IDs.
   */
  public returnFleetAssetIdsFromDriverId(driverId: string): string[] {
    const fleets: string[] = [];
    this.fleetAssetStore.fleetAssetActiveDriver.forEach((value, key) => {
      if (value === driverId) {
        fleets.push(key);
      }
    });
    return fleets;
  }

  get externalMessageRequest(): boolean {
    return this.driverMessageStore.requestingMessageFromExternalComponent;
  }

  get recurringJobIdMap() {
    if (!useRootStore().operationsPortalLoadedData.JOBS) {
      return;
    }
    return useRecurringJobStore().recurringJobIdMap;
  }

  @Watch('externalMessageRequest')
  public sendMessageFromExternalRequest(value: boolean) {
    if (value) {
      this.unsentMessage = this.driverMessageStore.messageContent;
      this.driverMessageStore.setRequestingMessageFromExternalComponent(false);
    }
  }

  /**
   * Sets the value of showDriverChatHistoryDialog, which controls the
   * visibility of the DriverChatHistory component.
   *
   * @param value - The new value for showDriverChatHistoryDialog.
   */
  public setShowDriverChatHistoryDialog(value: boolean) {
    this.showDriverChatHistoryDialog = value;
  }

  /**
   * Sets the value of showDriverBulkMessageDialog, which controls the
   * visibility of the DriverBulkMessage component.
   *
   * @param value - The new value for showDriverBulkMessageDialog.
   */
  public setShowDriverBulkMessageDialog(value: boolean) {
    this.showDriverBulkMessageDialog = value;
  }
  /**
   * Handles 'up' and 'down' key presses to navigate through chat messages.
   * @param {string} value - The key pressed ('up' or 'down').
   * @param {ChatMessage} item - The current chat message.
   */
  public tabHandler(value: string, item: ChatMessage): void {
    if (!item._id) {
      return;
    } // Exit if item has no id

    const foundIndex = this.chatMessageList.findIndex(
      (chat) => chat._id === item._id,
    );
    if (foundIndex === -1) {
      return;
    } // Exit if item is not in chatMessageList

    let newIndex;

    // Set newIndex based on 'up' or 'down' key press
    if (value === 'up') {
      newIndex =
        foundIndex > 0 ? foundIndex - 1 : this.chatMessageList.length - 1;
    }
    if (value === 'down') {
      newIndex =
        foundIndex < this.chatMessageList.length - 1 ? foundIndex + 1 : 0;
    }

    // Focus chat message at newIndex if defined
    if (newIndex !== undefined) {
      this.focusChatMessage(this.chatMessageList[newIndex]._id);
    }
  }

  public focusChatMessage(elId: string | undefined | null): boolean {
    if (!elId) {
      this.selectedChatMessageId = null;
      return false;
    }
    if (elId === this.selectedChatMessageId) {
      return false;
    }

    // If we changed the focus then we should set associatedAlerts to null so
    // that the expanded panel closes
    if (!!this.associatedAlerts) {
      this.showingAlertsForChatId = null;
      this.associatedAlerts = null;
      // Set brief timeout so focusing works correctly
      setTimeout(() => {
        this.focusElement(elId);
      }, 10);
    } else {
      this.focusElement(elId);
    }
    return true;
  }

  public focusElement(id: string) {
    this.selectedChatMessageId = id;
    const foundElement = document.getElementById(id);
    if (foundElement) {
      foundElement.focus();
    }
  }

  public changeCurrentTab(tab: MessageTabs) {
    this.currentTab = tab;
    this.settingsView = false;
    if (tab === MessageTabs.CURRENT) {
      const element = document.getElementById('message-contents-div');
      if (element) {
        element.scrollTop = 0;
      }
    }
  }

  /**
   * Disables keyboard scroll for specific keys.
   * @param e - The keyboard event.
   */
  public disableKeyboardScroll(e: KeyboardEvent) {
    if (['ArrowUp', 'ArrowDown'].includes(e.key)) {
      e.preventDefault();
    }
  }

  /**
   * For the selected conversation, open that driver's Fleet Asset Dialog and go
   * to the tab for Message History. For cases when the user wants to view
   * further into the driver's message history.
   */
  public jumpToHistoryComponent() {
    const fleets: string[] = this.returnFleetAssetIdsFromDriverId(
      this.currentConversationSenderId,
    );
    if (fleets.length) {
      this.operationsStore.setSelectedDriverId(
        this.currentConversationSenderId,
      );
      this.operationsStore.setSelectedFleetAssetId(fleets[0]);
      this.operationsStore.setSelectedFleetAssetDialogTab('MESSAGE_HISTORY');
      this.operationsStore.setViewingAssetInformationDialog(true);
    } else {
      this.setShowDriverChatHistoryDialog(true);
    }
  }

  /**
   * As JobProgressAlerts are only sent by the backend every 5 minutes, we need
   * fetch the last batch when we load this component. If the jobProgressAlert
   * maps in the store are null, then we should request them so the components
   * have a starting value.
   */
  public async getInitialJobProgressAlerts() {
    if (
      this.driverMessageStore.loadTimeProgressAlerts === null ||
      this.driverMessageStore.acceptProgressAlerts === null ||
      this.driverMessageStore.pickupProgressAlerts === null
    ) {
      const userTimeZone = this.companyDetailsStore.userLocale;
      // Find results were generated in the last five minutes, which will be the most recent
      const request: JobProgressAlertHistoryRequest = {
        startEpoch: moment().tz(userTimeZone).subtract(5, 'minutes').valueOf(),
        endEpoch: moment().valueOf(),
      };
      // Request jobProgress alert history and set response to store
      const results =
        await this.driverMessageStore.searchJobProgressAlertHistory(request);
      this.driverMessageStore.savedJobProgressAlertHistoryResponse({
        company: '',
        division: '',
        jobProgressAlertList: results ?? [],
      });
    }
  }

  public beforeMount() {
    this.getInitialJobProgressAlerts();
  }

  public created() {
    window.addEventListener('keydown', this.disableKeyboardScroll, false);
  }
  public beforeDestroy() {
    window.removeEventListener('keydown', this.disableKeyboardScroll);
  }
}
