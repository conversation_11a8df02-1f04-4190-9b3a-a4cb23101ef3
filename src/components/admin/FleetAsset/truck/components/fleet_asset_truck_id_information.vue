<template>
  <div class="fleet-asset-truck-identifying-information">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">Identifying Information</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Vehicle Identification Number (VIN)
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.vinNumber"
              :disabled="!isEdited"
              solo
              flat
              autofocus
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Engine Number</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.engineNumber"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Select Truck Colour</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              class="v-solo-custom"
              flat
              v-model="fleetAsset.fleetAssetTypeObject.colour"
              :items="vehicleColors"
              :disabled="!isEdited"
            ></v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Vehicle Make</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :disabled="!isEdited"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              solo
              flat
              v-model="fleetAsset.fleetAssetTypeObject.make"
              :items="truckMakes"
              item-value="longName"
              item-text="longName"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Vehicle Model</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :disabled="!isEdited"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              solo
              flat
              v-model="fleetAsset.fleetAssetTypeObject.model"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Vehicle Year</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :disabled="!isEdited"
              :items="yearList"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              class="v-solo-custom"
              flat
              v-model="fleetAsset.fleetAssetTypeObject.year"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Home Address</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <AddressSearchAU
              :formDisabled="!isEdited"
              :address="fleetAsset.locationAddress"
              :soloInput="true"
              :enableNicknamedAddress="false"
              :enableReturnToDefaultDispatchAddress="false"
              :enableSuburbSelect="false"
              :hideHeadingRow="true"
            >
            </AddressSearchAU>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import TruckDetails from '@/interface-models/FleetAsset/models/FleetAssetTypes/Truck/TruckDetails';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import truckMakes from '@/interface-models/Generic/TruckMakes/TruckMakes';
import { computed, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset & { fleetAssetTypeObject: TruckDetails };
    isEdited: boolean;
    fleetAssetOwner: FleetAssetOwnerSummary;
  }>(),
  {
    isEdited: false,
  },
);

const vehicleColors = ref<string[]>([
  'White',
  'Black',
  'Grey',
  'Silver',
  'Gold',
  'Blue',
  'Red',
  'Orange',
  'Yellow',
  'Green',
]);

// List of years to be displayed in the dropdown select for vehicle year
const yearList = computed(() => {
  const years: number[] = [];
  const currentYear = new Date().getFullYear();
  for (let year = currentYear; year > currentYear - 40; year--) {
    years.push(year);
  }
  return years;
});
</script>

<style scoped lang="scss">
.fleet-asset-truck-identifying-information {
  padding: 0;

  .label-container {
    height: 48px;
  }
}
</style>
