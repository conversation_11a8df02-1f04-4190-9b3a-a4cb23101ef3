<template>
  <div class="fleet-asset-truck-key-information">
    <v-layout wrap>
      <v-flex offset-md1 md10>
        <RateExpirationSummaryAlert
          :expirationSummaries="fleetAsset.allRateExpirationSummaries"
        ></RateExpirationSummaryAlert>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Key Details & Status</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Fleet Asset ID <span v-if="isOutsideHire">(Outside Hire)</span>
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              solo
              flat
              class="v-solo-custom"
              v-model="fleetAsset.csrAssignedId"
              :disabled="!isEdited"
              color="light-blue"
              :rules="[validate.required, validateCsrAssignedId]"
              autofocus
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="!isOutsideHire">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Truck / Vehicle Class
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              solo
              flat
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.truckClass"
              :items="weightServiceTypeList"
              item-text="optionSelectName"
              color="light-blue"
              :disabled="!isEdited"
              :rules="[validate.required]"
              item-value="shortServiceTypeName"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="!isOutsideHire">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Body Type
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              solo
              flat
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.bodyType"
              item-value="id"
              item-text="longName"
              color="light-blue"
              :items="truckTypes"
              :rules="[validate.required]"
              :disabled="!isEdited"
            ></v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="!isOutsideHire">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Toll Class
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              solo
              flat
              class="v-solo-custom"
              :disabled="!isEdited"
              v-model="fleetAsset.fleetAssetTypeObject.tollClass"
              :items="vehicleClasses"
              :rules="[validate.required]"
              color="light-blue"
              item-text="longName"
              item-value="classId"
            ></v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <!-- <v-flex md12>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="form-field-label-container">
            <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker"
              >Active Status</span
            >
          </v-layout>
        </v-flex>
        <v-flex md8>
          <StatusSelect
            key="retirementStatus"
            :statusCategory="8"
            :boxInput="false"
            :soloInput="true"
            :statusList="fleetAsset.statusList"
            :resetSelectedSecondaryStatus="false"
            :validate="validate"
            :formDisabled="!isEdited"
          >
          </StatusSelect>
        </v-flex>
      </v-layout>
    </v-flex> -->

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Active Status
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-layout>
              <StatusSelect
                :key="statusListKey"
                :statusCategory="8"
                :boxInput="false"
                :soloInput="true"
                :statusList="fleetAsset.statusList"
                :resetSelectedSecondaryStatus="false"
                :validate="validate"
                :formDisabled="true"
              >
              </StatusSelect>

              <SubcontractorUpdateOperationalStatus
                :isEdited="isEdited"
                :entityId="fleetAsset.fleetAssetId"
                :entityType="entityType"
                :statusList="fleetAsset.statusList"
                :csrAssignedId="fleetAsset.csrAssignedId"
                :isFleetAssetActive="
                  fleetAsset.statusList.includes(47) ? false : true
                "
              />
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Drivers</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
          <span
            class="tags-chip mx-1"
            v-if="fleetAsset.requiredCraneLicenceType"
            >{{ fleetAsset.requiredCraneLicenceType }}
          </span>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">All Associated Drivers</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              solo
              flat
              class="v-solo-custom"
              :disabled="!fleetStatus"
              :items="fleetAssetOwner.associatedDriverList"
              v-model="associatedDrivers"
              item-text="displayName"
              item-value="driverId"
              persistent-hint
              color="white"
              :rules="[validate.required]"
              multiple
            >
              <template v-slot:selection="data">
                <span
                  >{{ data.item.name }}
                  <span
                    v-if="
                      data.item.statusList.includes(13) ||
                      data.item.statusList.includes(47)
                    "
                  >
                    <span class="px-2"> | </span>
                    <span
                      :style="{
                        color: getDriverColorByStatusList(data.item.statusList),
                      }"
                    >
                      {{
                        getDriverStatusNameByStatusList(data.item.statusList)
                      }}</span
                    >
                  </span>
                </span>
                <span
                  class="pr-2"
                  v-if="data.index !== associatedDrivers.length - 1"
                  >,
                </span>
              </template>
              <template v-slot:item="data">
                <div class="flex-row align-center">
                  <v-checkbox
                    :input-value="
                      associatedDrivers.includes(data.item.driverId)
                    "
                  ></v-checkbox>
                  <span
                    >{{ data.item.name }}
                    <span
                      v-if="
                        data.item.statusList.includes(13) ||
                        data.item.statusList.includes(47)
                      "
                    >
                      <span class="px-2"> | </span>
                      <span
                        :style="{
                          color: getDriverColorByStatusList(
                            data.item.statusList,
                          ),
                        }"
                      >
                        {{
                          getDriverStatusNameByStatusList(data.item.statusList)
                        }}</span
                      >
                    </span>
                  </span>
                </div>
              </template>
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  availableDefaultDrivers.length
                    ? 'form-field-required-marker'
                    : ''
                "
              >
                Default Driver
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              solo
              flat
              class="v-solo-custom"
              :disabled="!fleetStatus || !availableDefaultDrivers.length"
              :items="availableDefaultDrivers"
              item-text="displayName"
              :rules="availableDefaultDrivers.length ? [validate.required] : []"
              item-value="driverId"
              v-model="fleetAsset.fleetAssetTypeObject.defaultDriver"
              color="blue lighten-1"
              persistent-hint
            >
              <template v-slot:selection="data">
                <span
                  >{{ data.item.name }}
                  <span
                    v-if="
                      data.item.statusList.includes(13) ||
                      data.item.statusList.includes(47)
                    "
                  >
                    <span class="px-2"> | </span>
                    <span
                      :style="{
                        color: getDriverColorByStatusList(data.item.statusList),
                      }"
                    >
                      {{
                        getDriverStatusNameByStatusList(data.item.statusList)
                      }}</span
                    >
                  </span>
                </span>
              </template>
              <template v-slot:item="data">
                <div class="flex-row align-center">
                  <span
                    >{{ data.item.name }}
                    <span
                      v-if="
                        data.item.statusList.includes(13) ||
                        data.item.statusList.includes(47)
                      "
                    >
                      <span class="px-2"> | </span>
                      <span
                        :style="{
                          color: getDriverColorByStatusList(
                            data.item.statusList,
                          ),
                        }"
                      >
                        {{
                          getDriverStatusNameByStatusList(data.item.statusList)
                        }}</span
                      >
                    </span>
                  </span>
                </div>
              </template>
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3 v-if="!isOutsideHire"
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">3. Registration Information</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="!isOutsideHire">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Vehicle Registration Number
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              solo
              flat
              class="v-solo-custom"
              :disabled="!isEdited"
              :rules="[validate.required]"
              v-model="
                fleetAsset.fleetAssetTypeObject.registrationDetails
                  .registrationNumber
              "
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 v-if="!isOutsideHire">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                State of Registration
              </h6>
            </v-layout>
          </v-flex>

          <v-flex md8>
            <v-select
              solo
              flat
              class="v-solo-custom"
              :disabled="!isEdited"
              :items="stateOfRegistrationSelector"
              item-value="stateShortName"
              item-text="stateLongName"
              :rules="[validate.required]"
              v-model="
                fleetAsset.fleetAssetTypeObject.registrationDetails
                  .stateOfRegistration
              "
            >
            </v-select>
          </v-flex> </v-layout
      ></v-flex>

      <v-flex md12 v-if="!isOutsideHire">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Registration Expiry
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="
                fleetAsset.fleetAssetTypeObject.registrationDetails.expiry
              "
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Registration Expiry"
              :readOnly="!isEdited"
              :soloInput="true"
              :boxInput="false"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
              :isRequired="true"
            ></DateTimeInputs>
          </v-flex> </v-layout
      ></v-flex>

      <!-- NEW FIELDS -->
      <v-flex md12 pb-3 v-if="!isOutsideHire"
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">4. Operational Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                <span class="pr-2">
                  <InformationTooltip
                    :right="true"
                    :tooltipType="HealthLevel.INFO"
                  >
                    <v-layout slot="content" row wrap>
                      <v-flex md12>
                        <p class="mb-1" slot="content">
                          Ticking this box will mean that this Fleet Asset will
                          always have a Fuel Levy available, and thus will not
                          appear in the Expiring Fuel Levies Dashboard.
                        </p>
                      </v-flex>
                    </v-layout>
                  </InformationTooltip>
                </span>
                On Expired Fuel Surcharge:
              </h6>
            </v-layout>
          </v-flex>

          <v-flex md8>
            <v-checkbox
              v-model="fleetAsset.expiredFuelSurchargeDefaultsToDivisionRate"
              :disabled="!isEdited"
              label="Use Division Fuel Surcharge Rate"
              persistent-hint
              :hint="
                fleetAsset.expiredFuelSurchargeDefaultsToDivisionRate
                  ? 'Division rate will be applied when no valid Fleet Asset Fuel Surcharge is found'
                  : 'If no Fleet Asset Fuel Surcharge Rate is found, booking and pricing will be unavailable'
              "
              color="light-blue"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-divider class="my-4" />
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                <span class="pr-2">
                  <InformationTooltip
                    :right="true"
                    :tooltipType="HealthLevel.INFO"
                  >
                    <v-layout slot="content" row wrap>
                      <v-flex md12>
                        <p class="mb-1">
                          If any of the service rates are selected in this
                          configuration, this vehicle's rate card will utilise
                          the division fleet rate card for that rate type. This
                          vehicle's rate card will always take precedence if it
                          has a custom rate card defined for the same service /
                          rate type combination..
                        </p>
                      </v-flex>
                    </v-layout>
                  </InformationTooltip>
                </span>
                This vehicle's Rate Card will merge with the Division Fleet Rate
                Card for the following service rates:
              </h6>
              <span class="px-2"> </span>
            </v-layout>
          </v-flex>

          <v-flex md8>
            <div class="checkbox-group pb-2">
              <div
                v-for="service in serviceTypeRate"
                :key="service.rateTypeId"
                class="checkbox-wrapper"
                :class="{ disabled: !isEdited }"
              >
                <label class="custom-checkbox">
                  <input
                    type="checkbox"
                    v-model="selectedRateTypeIds"
                    :value="service.rateTypeId"
                    :key="service.rateTypeId"
                    :disabled="!isEdited || isNoneLocked(service.rateTypeId)"
                  />
                  <span class="checkbox-icon"></span>
                  <span class="checkbox-label">{{ service.longName }}</span>
                </label>
              </div>
            </div>
            <span
              v-if="selectedRateTypeIds.includes(0)"
              class="none-selected-txt pt-2"
              ><v-icon size="16" class="pr-2">warning</v-icon> Fleet Rates Are
              Set To Not Merge with Division Rates</span
            >
          </v-flex>
        </v-layout>
        <v-divider class="my-4" />
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                <span class="pr-2">
                  <InformationTooltip
                    :right="true"
                    :tooltipType="HealthLevel.INFO"
                  >
                    <v-layout slot="content" row wrap>
                      <v-flex md12>
                        <p class="mb-1">
                          Ticking this box will mean that this Fleet Asset will
                          always have rates available, and thus will not appear
                          in the Expiring Rates Dashboard.
                        </p>
                      </v-flex>
                    </v-layout>
                  </InformationTooltip>
                </span>
                On Expired Custom Fleet Rate Card:
              </h6>
              <span class="px-2"> </span>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="fleetAsset.expiredServiceRateDefaultsToDivisionRate"
              :disabled="!isEdited || selectedRateTypeIds.includes(0)"
              label="Use Division Service Rates"
              persistent-hint
              :hint="
                fleetAsset.expiredServiceRateDefaultsToDivisionRate
                  ? 'Division rate will be applied when no valid Fleet Asset Service Rate is found'
                  : 'If no Fleet Asset Service Rate is found, booking and pricing will be unavailable'
              "
              color="light-blue"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                <span class="pr-2">
                  <InformationTooltip
                    :right="true"
                    :tooltipType="HealthLevel.INFO"
                  >
                    <v-layout slot="content" row wrap>
                      <v-flex md12>
                        <p class="mb-1">
                          Ticking this box will mean that this Fleet Asset is
                          using the current standard default rate card. If you
                          un-ticket this box, this Fleet Asset should have a
                          current Default Rate Config otherwise they will appear
                          in the Expiring Default Rates Dashboard.
                        </p>
                      </v-flex>
                    </v-layout>
                  </InformationTooltip>
                </span>
                Uses Current Division Fleet Rate Card For Merging:
              </h6>
              <span class="px-2"> </span>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="fleetAsset.usesStandardDivisionRates"
              :disabled="!isEdited || selectedRateTypeIds.includes(0)"
              label="Uses Current Division Fleet Rate Card"
              persistent-hint
              :hint="
                fleetAsset.usesStandardDivisionRates
                  ? 'Merge with current division rates and will ignore this config.'
                  : 'Not Merge with division Rates (see Default Rates Config tab)'
              "
              color="light-blue"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pb-3 v-if="isAuthorisedAdmin()"
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">Compliance Overrides</h5>
          <span class="accent-text--card warning-type-outline px-2">
            ADMIN ONLY
          </span>
          <v-flex px-2>
            <v-divider></v-divider>
          </v-flex>
          <span>
            <v-switch
              label="Show Advanced"
              v-model="showAdminComplianceSettings"
              :disabled="!isEdited"
            ></v-switch>
          </span>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="isAuthorisedAdmin() && showAdminComplianceSettings">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Insurance Compliance:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="insuranceComplianceRequired"
              :disabled="!isEdited"
              color="light-blue"
              label="Insurance Compliance Required"
              hint="Compliance checks related to Insurances will be carried out for this Driver. Overdue, expiring or missing Insurances will appear on the Subcontractor Dashboard"
              persistent-hint
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="isAuthorisedAdmin() && showAdminComplianceSettings">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Registration Compliance:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="registrationComplianceRequired"
              :disabled="!isEdited"
              color="light-blue"
              label="Registration Compliance Required"
              hint="Compliance checks related to Registrations will be carried out for this Driver. Overdue, expiring or missing Registrations will appear on the Subcontractor Dashboard"
              persistent-hint
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="isAuthorisedAdmin() && showAdminComplianceSettings">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Equipment Compliance:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="equipmentComplianceRequired"
              :disabled="!isEdited"
              color="light-blue"
              label="Additional Equipment Compliance Required"
              hint="Compliance checks related to Additional Equipment Services will be carried out for this Driver. Overdue, expiring or missing Services will appear on the Subcontractor Dashboard"
              persistent-hint
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import StatusSelect from '@/components/common/status_select.vue';
import SubcontractorUpdateOperationalStatus from '@/components/common/subcontractor_update_operational_status/index.vue';
import RateExpirationSummaryAlert from '@/components/common/ui-elements/rate_expiration_summary_alert.vue';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import { TruckDetails } from '@/interface-models/FleetAsset/models/FleetAssetTypes/Truck/TruckDetails';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { SubcontractorAssociationUpdateResponse } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateResponse';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import serviceTypeRates, {
  JobRateType,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { truckTypes } from '@/interface-models/Generic/TruckTypes/TruckTypes';
import { OperationStatus } from '@/interface-models/Generic/WebSocketRequest/OperationStatus';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import vehicleClasses from '@/interface-models/ServiceRates/AdditionalCharges/static/vehicleClasses';
import StateRegistrationSelectorDataAU from '@/static/staticData/state_registrationAU.json';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useMittListener } from '@/utils/useMittListener';
import {
  computed,
  ComputedRef,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset & { fleetAssetTypeObject: TruckDetails };
    fleetAssetOwner: FleetAssetOwnerSummary;
    isEdited: boolean;
    isCancel: boolean;
  }>(),
  {
    isEdited: false,
    isCancel: true,
  },
);

const entityType = ref(SubcontractorEntityType.FLEET_ASSET);
const stateOfRegistrationSelector = ref(StateRegistrationSelectorDataAU);
const showAdminComplianceSettings = ref(false);
const csrAssignedId = ref(props.fleetAsset.csrAssignedId);

const fleetStore = useFleetAssetStore();

const availableDefaultDrivers = computed(() => {
  const allOwnerDrivers = props.fleetAssetOwner.associatedDriverList;
  return allOwnerDrivers.filter((d: any) =>
    props.fleetAsset.associatedDrivers.includes(d.driverId),
  );
});

// store selectedRateTypeIds before isEdited
const initialRates: Ref<number[]> = ref(
  props.fleetAsset.mergeServiceRatesWithDivisionRates?.length > 0
    ? props.fleetAsset.mergeServiceRatesWithDivisionRates
    : [0],
);

const selectedRateTypeIds: Ref<number[]> = ref(
  props.fleetAsset.mergeServiceRatesWithDivisionRates?.length > 0
    ? props.fleetAsset.mergeServiceRatesWithDivisionRates
    : [0],
);

// serviceTypeRate for  merge with the Division Fleet Rate Card checkboxes
// add new option NONE, rateTypeId = 0
// remove Trip/Quoted Rate from serviceTypeRate
const serviceTypeRate = computed(() => {
  // Filter out JobRateType.TRIP (rateTypeId === 6)
  const filteredRates = serviceTypeRates.filter(
    (service) => service.rateTypeId !== JobRateType.TRIP,
  );

  // Add the new NONE option
  const noneOption = {
    rateTypeId: 0, // Use 0 or any other value for NONE
    shortName: 'None',
    longName: 'None',
    adhoc: false,
  };

  return [noneOption, ...filteredRates]; // Return the filtered list with the NONE option added
});

// get fleet asset status for allocated driver
// 4 is active 47 is do-not-use, when 47 allocated driver disabled
const fleetStatus: WritableComputedRef<boolean> = computed(() => {
  if (props.fleetAsset.statusList.includes(47) || !props.isEdited) {
    return false;
  }
  return true;
});

// The list of all drivers under the fleet asset owner, that could potentially be used as drivers
const associatedDrivers: WritableComputedRef<string[]> = computed({
  get: (): string[] => props.fleetAsset.associatedDrivers,
  set: (newValue: string[]) => {
    const truck = props.fleetAsset.fleetAssetTypeObject as TruckDetails;

    if (
      !newValue ||
      newValue.length === 0 ||
      !newValue.includes(truck.defaultDriver)
    ) {
      truck.defaultDriver = '';
    }

    props.fleetAsset.associatedDrivers = newValue;
  },
});

// Return true if the owner is of affiliation '3', corresponding with Outside
// Hire. Use this to set outsideHire property in Fleet Asset, as well as to set
// required fields
const isOutsideHire = computed(() => {
  return (
    props.fleetAssetOwner !== undefined &&
    props.fleetAssetOwner.affiliation === '3'
  );
});

// Return serviceTypes with pallet rate service removed
const weightServiceTypeList = computed(() => {
  return useCompanyDetailsStore().getServiceTypesList.filter(
    (s: ServiceTypes) => s.longServiceTypeName !== 'UNIT RATE',
  );
});

const validate = computed(() => {
  return validationRules;
});

// insuranceRequired;
// registrationRequired;
// equipmentServiceRequired;

// Controller for switches for INSURANCE settings, visible/editable by admin
// only
const insuranceComplianceRequired: WritableComputedRef<boolean> = computed({
  get: (): boolean => props.fleetAsset.insuranceComplianceRequired,
  set: (value: boolean) => {
    if (!isAuthorisedAdmin()) {
      return;
    }
    // If complianceOverrides is previously undefined, then set default values
    // to all positive values
    if (!props.fleetAsset.complianceOverrides) {
      props.fleetAsset.restoreComplianceDefaults();
    }
    props.fleetAsset.complianceOverrides!.insuranceRequired = value;
  },
});

// Controller for switches for REGISTRATION compliance settings,
// visible/editable by admin only
const registrationComplianceRequired: WritableComputedRef<boolean> = computed({
  get: (): boolean => props.fleetAsset.registrationComplianceRequired,
  set: (value: boolean) => {
    if (!isAuthorisedAdmin()) {
      return;
    }
    // If complianceOverrides is previously undefined, then set default values
    // to all positive values
    if (!props.fleetAsset.complianceOverrides) {
      props.fleetAsset.restoreComplianceDefaults();
    }
    props.fleetAsset.complianceOverrides!.registrationRequired = value;
  },
});

// Controller for switches for EQUIPMENT compliance settings, visible/editable
// by admin only
const equipmentComplianceRequired: WritableComputedRef<boolean> = computed({
  get: (): boolean => props.fleetAsset.equipmentComplianceRequired,
  set: (value: boolean) => {
    if (!isAuthorisedAdmin()) {
      return;
    }
    // If complianceOverrides is previously undefined, then set default values
    // to all positive values
    if (!props.fleetAsset.complianceOverrides) {
      props.fleetAsset.restoreComplianceDefaults();
    }
    props.fleetAsset.complianceOverrides!.equipmentServiceRequired = value;
  },
});

// Check if we are authorised to see selects related to compliance
const isAuthorisedAdmin = () => {
  return hasAdminRole();
};

/**
 * sets the alert color of the driver based on their status list. Utilized in the trucks associated drivers select.
 * @param {number[]} statusList - The statusList of the driver
 * @return {string} Hex color value
 */
const getDriverColorByStatusList = (statusList: number[]): string => {
  const warningColor = '#ff5252';
  const doNotUseColor = '#fb8c00';
  const activeColor = '#FFFFFF';
  const color: string = statusList.includes(13)
    ? warningColor
    : statusList.includes(47)
      ? doNotUseColor
      : activeColor;
  return color;
};

/**
 * sets the status name of the driver based on their status list. Utilized in the trucks associated drivers select.
 * @param {number[]} statusList - The statusList of the driver
 * @return {string} - one of RETIRED, CLOSED or empty string
 */
const getDriverStatusNameByStatusList = (statusList: number[]): string => {
  return statusList.includes(13)
    ? 'RETIRED'
    : statusList.includes(47)
      ? 'CLOSED'
      : '';
};

/**
 * Used as key for StatusSelect component to force re-render when statusList is
 * updated
 */
const statusListKey: ComputedRef<string> = computed(() => {
  return `${props.fleetAsset.statusList}`;
});

/**
 * Handles response to status update called from
 * SubcontractorUpdateOperationalStatus component. Used to update the statusList
 * in the fleetAsset prop.
 * @param response - contains properties from updated document, including the
 * updated statusList which we'll set to props.fleetAsset
 */
function handleSubcontractorEntityUpdate(
  response: SubcontractorAssociationUpdateResponse | null,
): void {
  if (
    response?.fleetAssetId === props.fleetAsset.fleetAssetId &&
    response.entityType === SubcontractorEntityType.FLEET_ASSET &&
    response.operationStatus === OperationStatus.SUCCESS &&
    !!response.statusList
  ) {
    props.fleetAsset.statusList = response.statusList;
  }
}

/**
 * Helper function to validate the assigned Fleet CSR ID.
 * Ensures that the entered ID is unique among active fleet assets.
 * @param {string} value - The CSR ID entered by the user.
 * @returns {boolean | string} - Returns `true` if valid, otherwise returns an error message.
 */
function validateCsrAssignedId(
  value: string,
): true | 'Fleet ID is already in use' {
  // Fetch the list of fleet assets
  const fleetAssets = fleetStore.getAllFleetAssetList;

  // Check for active fleet assets (statusList includes 4 for ACTIVE)
  const activeFleetAssets = fleetAssets.filter(
    (asset: FleetAssetSummary) => asset.isActive,
  );

  // Check if the entered ID already exists among active fleet assets
  const isDuplicate = activeFleetAssets.some(
    (asset: FleetAssetSummary) =>
      asset.csrAssignedId === value && csrAssignedId.value !== value,
  );

  // Return validation message or true (valid)
  return isDuplicate ? 'Fleet ID is already in use' : true;
}

// set merging option to none and disable deselect
function isNoneLocked(id: number) {
  return (
    id === 0 &&
    selectedRateTypeIds.value.length === 1 &&
    selectedRateTypeIds.value[0] === 0
  );
}

/**
 * watch for selectedRateTypeIds to switch between none and other service rate types
 * if none is selected disable and set to false expiredServiceRateDefaultsToDivisionRate and usesStandardDivisionRates
 * emit selectedRateTypeIds to update fleet Asset details in parent
 */
watch(
  selectedRateTypeIds,
  (newVal, oldVal) => {
    // If user unchecks everything, default to [0]
    if (newVal.length === 0) {
      selectedRateTypeIds.value = [0];
      props.fleetAsset.expiredServiceRateDefaultsToDivisionRate = false;
      props.fleetAsset.usesStandardDivisionRates = false;
      props.fleetAsset.mergeServiceRatesWithDivisionRates = [];
      return;
    }

    const added = newVal.filter((id) => !oldVal?.includes(id));
    if (added.length) {
      const toggled = added[0];
      if (toggled === 0) {
        // “None” was checked → clear everything else
        selectedRateTypeIds.value = [0];
        props.fleetAsset.expiredServiceRateDefaultsToDivisionRate = false;
        props.fleetAsset.usesStandardDivisionRates = false;
        props.fleetAsset.mergeServiceRatesWithDivisionRates = [];
      } else {
        // some non‐zero was checked → remove “None” if present
        selectedRateTypeIds.value = newVal.filter((id) => id !== 0);
        props.fleetAsset.mergeServiceRatesWithDivisionRates =
          selectedRateTypeIds.value;
      }
    }
  },
  { immediate: true },
);

watch(
  () => props.isCancel,
  (isCancel) => {
    if (isCancel) {
      selectedRateTypeIds.value = [...initialRates.value];
    }
  },
);

useMittListener(
  'updateSubcontractorEntityAssociationResponse',
  handleSubcontractorEntityUpdate,
);
</script>

<style scoped lang="scss">
.fleet-asset-truck-key-information {
  padding: 0;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .checkbox-wrapper {
    display: flex;
    align-items: center;

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
      pointer-events: none;
    }

    .custom-checkbox {
      display: flex;
      align-items: center;
      cursor: pointer;
      position: relative;
      user-select: none;

      input[type='checkbox'] {
        opacity: 0;
        position: absolute;
        width: 0;
        height: 0;
      }

      .checkbox-icon {
        height: 18px;
        width: 18px;
        border-radius: 2px;
        border: 1.5px solid var(--light-text-color);
        background-color: none;
        margin-right: 12px;
        transition:
          background-color 0.2s,
          border-color 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        opacity: 0.9;

        &::after {
          content: '✔';
          display: none;
          width: 12px;
          height: 18px;
          background-color: #00b0ff;
          color: $app-dark-primary-400;
        }
      }

      input[type='checkbox']:checked + .checkbox-icon {
        opacity: 0.9;
        border-color: #00b0ff;
        background-color: #00b0ff;

        &::after {
          display: block;
          background-color: none; // checkmark color inside filled box
        }
      }

      .checkbox-label {
        font-size: 16px;
        color: var(--text-color);
        padding-left: 4px;
      }
    }
  }
}

.none-selected-txt {
  padding-top: 12px;
  font-size: 16px;
  font-weight: 500;
  color: $warning;

  .v-icon {
    color: $warning;
  }
}
</style>
