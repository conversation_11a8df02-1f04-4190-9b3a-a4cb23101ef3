<template>
  <div class="client-details-key-information">
    <v-layout mt-3 row wrap v-if="!isClientOperationalStatusUpdate">
      <v-flex md12 pb-1
        ><v-layout align-center>
          <h5 class="subheader--bold px-3 pt-1">Current Work</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
          <h5 class="subheader--bold pt-1 px-3">
            {{ activeWorkJobDetailsList.length }} Jobs
          </h5>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        mb-2
        class="rounded-lg px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
      >
        <v-layout justify-space-between class="table-row-custom rounded-lg">
          <v-flex md4>
            <span class="table-row-custom__header header-type"> Date </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span class="table-row-custom__header header-type"> Job ID </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span class="table-row-custom__header header-type"> Progress </span>
          </v-flex>
        </v-layout>
        <v-layout
          justify-space-between
          class="table-row-custom"
          v-for="job in activeWorkJobDetailsList"
          :key="job.jobId"
        >
          <v-flex md4>
            <span>
              {{ job.readableJobDate }}
            </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span>
              {{ job.recurringJobId ? job.recurringJobId : job.jobId }}
            </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span>
              {{ job.status }}
            </span>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-layout mt-3 row wrap>
      <v-flex md12 pb-1
        ><v-layout align-center>
          <h5 class="subheader--bold px-3 pt-1">Pending Work</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
          <h5 class="subheader--bold--14 pt-1 px-3">
            {{ pendingWorkJobDetailsList.length }} Jobs
          </h5>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        mb-2
        class="rounded-lg px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
      >
        <v-layout justify-space-between class="table-row-custom">
          <v-flex md4>
            <span class="table-row-custom__header header-type"> Date </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span class="table-row-custom__header header-type"> Job ID </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span class="table-row-custom__header header-type"> Progress </span>
          </v-flex>
        </v-layout>
        <v-layout
          justify-space-between
          class="table-row-custom"
          v-for="job in pendingWorkJobDetailsList"
          :key="job.jobId"
        >
          <v-flex md4>
            <span>
              {{ job.readableJobDate }}
            </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span>
              {{ job.recurringJobId ? job.recurringJobId : job.jobId }}
            </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span>
              {{ job.status }}
            </span>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>

    <v-layout row wrap pt-3>
      <v-flex md12 pb-1
        ><v-layout align-center>
          <h5 class="subheader--bold px-3 pt-1">Active Permanent Jobs</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
          <h5 class="subheader--bold--14 pt-1 px-3">
            {{ recurringJobList.length }} Jobs
          </h5>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        class="px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
      >
        <v-layout justify-space-between class="table-row-custom">
          <v-flex md4>
            <span class="table-row-custom__header header-type"> Client </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span class="table-row-custom__header header-type"> Job ID </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span class="table-row-custom__header header-type">
              Next Scheduled
            </span>
          </v-flex>
        </v-layout>
        <v-layout
          justify-space-between
          class="table-row-custom"
          v-for="job in recurringJobList"
          :key="job.initialJobId"
        >
          <v-flex md4>
            <span>{{
              job.client
                ? `${job.client.clientName} - ${job.client.id}`
                : 'Unknown'
            }}</span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span>
              {{ job.initialJobId ? job.initialJobId : 'Unknown' }}
            </span>
          </v-flex>
          <v-flex md4 class="table-row-cell right-align">
            <span>
              {{
                job.recurrenceDetails &&
                job.recurrenceDetails.nextScheduledRecurrence
                  ? returnFormattedDate(
                      job.recurrenceDetails.nextScheduledRecurrence,
                    )
                  : '-'
              }}
            </span>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { ClientActiveAssociations } from '@/interface-models/Client/ClientActiveAssociations';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import RecurringJobTemplate from '@/interface-models/Jobs/RecurringJob/RecurringJobTemplate';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';

const jobStore = useJobStore();

const pendingWorkJobIdList: Ref<number[]> = ref([]);
const activeWorkJobIdList: Ref<number[]> = ref([]);
const recurringJobList: Ref<RecurringJobTemplate[]> = ref([]);

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited?: boolean;
    isClientOperationalStatusUpdate?: boolean;
  }>(),
  {
    isEdited: false,
    isClientOperationalStatusUpdate: false,
  },
);

const emit = defineEmits<{
  (event: 'setCanCloseAccount', canClose: boolean): void;
}>();

// Function to set active associations
function setActiveAssociations(
  response: ClientActiveAssociations | null,
): void {
  if (!response) {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Client Details - Associations',
    });
    return;
  }
  pendingWorkJobIdList.value = response.pendingWorkJobIdList;
  activeWorkJobIdList.value = response.activeWorkJobIdList;
  recurringJobList.value = response.recurringJobList;
  if (props.isClientOperationalStatusUpdate) {
    emit(
      'setCanCloseAccount',
      pendingWorkJobIdList.value.length === 0 &&
        recurringJobList.value.length === 0,
    );
  }
}

// Computed properties for pending and active work job details
const pendingWorkJobDetailsList: ComputedRef<OperationJobSummary[]> = computed(
  () => {
    return jobStore.operationJobsList.filter(
      (x: OperationJobSummary) =>
        x.jobId && pendingWorkJobIdList.value.includes(x.jobId),
    );
  },
);

const activeWorkJobDetailsList: ComputedRef<OperationJobSummary[]> = computed(
  () => {
    return jobStore.operationJobsList.filter(
      (x: OperationJobSummary) =>
        x.jobId && activeWorkJobIdList.value.includes(x.jobId),
    );
  },
);

// Function to get clients' active associations
async function getClientsActiveAssociations(): Promise<void> {
  const results =
    await useClientDetailsStore().requestClientsActiveAssociations(
      props.clientDetails.clientId,
    );
  setActiveAssociations(results);
}
onMounted(() => {
  getClientsActiveAssociations();
});
</script>

<style scoped lang="scss">
.client-details-key-information {
  padding: 0;
}
</style>
