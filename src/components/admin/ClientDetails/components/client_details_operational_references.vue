<template>
  <v-layout class="client-operational-references">
    <div class="add-reference-btn">
      <v-btn
        small
        color="info"
        @click="newReferenceDetails"
        :disabled="isLoading"
        :loading="isLoading"
        >Create New Reference</v-btn
      >
    </div>

    <v-flex md12>
      <v-data-table
        class="gd-dark-theme"
        :headers="headers"
        :items="referenceTableData"
        :rows-per-page-items="[10, 20]"
      >
        <template v-slot:items="props">
          <tr
            @click="
              editReferenceDetails(props.item.type, props.item.referenceTypeId)
            "
            style="cursor: pointer"
          >
            <td>{{ props.item.type }}</td>
            <td>{{ props.item.reference }}</td>
            <td>{{ props.item.requiredType }}</td>
          </tr>
        </template>
      </v-data-table>
    </v-flex>

    <v-dialog
      v-if="referenceDetails"
      v-model="referenceDetailsDialogIsOpen"
      width="700px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-flex md12>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header header-border-highlight"
        >
          <span>Client Reference Requirements</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="cancelReferenceDetails"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-form ref="referenceForm">
              <v-layout
                wrap
                class="body-scrollable--65 body-min-height--65"
                pa-3
              >
                <v-flex md12 pb-3
                  ><v-layout align-center>
                    <h5 class="subheader--bold pr-3 pt-1">Reference Details</h5>
                    <v-flex>
                      <v-divider></v-divider>
                    </v-flex>
                  </v-layout>
                </v-flex>

                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >
                          Reference Location:
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-select
                        class="v-solo-custom"
                        solo
                        flat
                        :items="availableReferenceLocations"
                        item-text="name"
                        item-value="id"
                        :disabled="!isNew"
                        color="light-blue"
                        v-model="selectedReferenceType"
                        :rules="[validate.required]"
                      />
                    </v-flex>
                  </v-layout>
                </v-flex>

                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >
                          Reference Type:
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-select
                        class="v-solo-custom"
                        solo
                        flat
                        :items="availableReferenceTypes"
                        item-text="longName"
                        item-value="id"
                        :disabled="!isNew"
                        color="light-blue"
                        :rules="[validate.required]"
                        v-model="referenceDetails.referenceTypeId"
                      />
                    </v-flex>
                  </v-layout>
                </v-flex>

                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >
                          Reference Requirement:
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-select
                        class="v-solo-custom"
                        solo
                        color="light-blue"
                        flat
                        :items="availableReferenceRequirementTypes"
                        item-text="longName"
                        item-value="id"
                        :rules="[validate.required]"
                        v-model="referenceDetails.requiredTypeId"
                      />
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-form>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn
                color="error"
                depressed
                outline
                @click="cancelReferenceDetails"
              >
                <span class="ml-1">cancel</span>
              </v-btn>
              <v-spacer></v-spacer>

              <ConfirmationDialog
                v-if="!isNew"
                buttonText="Remove"
                message="Removing a required reference will impact jobs that are currently in progress, please confirm that you wish to remove this Reference."
                title="Confirm Removal of Reference"
                @confirm="removeReference"
                :isSmallButton="false"
                :isDepressedButton="false"
                :isBlockButton="false"
                :isFlatButton="true"
                :buttonColor="'error'"
                :confirmationButtonText="'Confirm Deletion'"
                :dialogIsActive="true"
                :noButtonMargin="true"
              >
              </ConfirmationDialog>
              <v-btn
                v-if="!isNew"
                color="info"
                depressed
                @click="updateReferenceDetails"
              >
                <span class="ml-1">Update reference</span>
              </v-btn>
              <v-btn
                v-if="isNew"
                color="blue"
                class="v-btn-confirm-custom"
                depressed
                @click="addReferenceDetails"
              >
                <span class="ml-1">Add reference</span>
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-dialog>
  </v-layout>
</template>
<script setup lang="ts">
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientReferenceDetails from '@/interface-models/Client/ClientDetails/References/ClientReferenceDetails';
import referenceRequirementTypes, {
  ReferenceRequirementType,
} from '@/interface-models/Client/ClientDetails/References/ReferenceRequirementTypes';
import referenceTypes, {
  ReferenceTypes,
} from '@/interface-models/Generic/ReferenceTypes/ReferenceTypes';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import { ComputedRef, Ref, computed, ref, watch } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited?: boolean;
    isLoading?: boolean;
  }>(),
  {
    isEdited: false,
    isLoading: false,
  },
);

const emit = defineEmits(['saveParentDocument']);

const referenceDetails = ref<ClientReferenceDetails | null>(null);

const referenceDetailsDialogIsOpen: Ref<boolean> = ref(false);
const selectedReferenceType: Ref<number> = ref(1);
const isNew: Ref<boolean> = ref(false);

interface IdName {
  id: number;
  name: string;
}

interface ReferenceTableData {
  type: string;
  reference: string;
  requiredType: string;
}

const headers: TableHeader[] = [
  {
    text: 'Type',
    align: 'left',
    value: 'type',
    sortable: false,
  },
  {
    text: 'Reference',
    align: 'left',
    sortable: false,
    value: 'reference',
  },
  {
    text: 'Requirement',
    align: 'left',
    value: 'requiredType',
    sortable: false,
  },
];

const availableReferenceLocations: IdName[] = [
  {
    id: 1,
    name: 'Job',
  },
  {
    id: 2,
    name: 'Stop',
  },
];

const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

const availableReferenceRequirementTypes = computed(() => {
  const selectedType = selectedReferenceType.value;
  return referenceRequirementTypes.filter((x: ReferenceRequirementType) =>
    selectedType === 1 ? x.isJobReference : x.isPudReference,
  );
});

const availableReferenceTypes: ComputedRef<ReferenceTypes[]> = computed(() => {
  const locationReferences =
    selectedReferenceType.value === 1
      ? props.clientDetails.references.mainJobScreen
      : props.clientDetails.references.pudScreen;

  const existingReferenceTypeIds: Array<number | null> = locationReferences.map(
    (x: ClientReferenceDetails) => x.referenceTypeId,
  );

  if (!isNew.value) {
    const currentViewingReferenceTypeId = referenceDetails.value
      ? referenceDetails.value.referenceTypeId
      : null;
    const currentlyEditingIndex = existingReferenceTypeIds.findIndex(
      (x: number | null) => x === currentViewingReferenceTypeId,
    );

    if (currentlyEditingIndex !== -1) {
      existingReferenceTypeIds.splice(currentlyEditingIndex, 1);
    }
  }
  if (selectedReferenceType.value === 1) {
    return referenceTypes.filter(
      (x: ReferenceTypes) =>
        x.isAvailableAdhoc && !existingReferenceTypeIds.includes(x.id),
    );
  } else {
    return referenceTypes.filter(
      (x: ReferenceTypes) =>
        x.isAvailableAdhoc && !existingReferenceTypeIds.includes(x.id),
    );
  }
});

const referenceTableData: ComputedRef<ReferenceTableData[]> = computed(() => {
  const jobReferences = props.clientDetails.references.mainJobScreen.map(
    (x: ClientReferenceDetails) => {
      const referenceType = referenceTypes.find(
        (refType: ReferenceTypes) => refType.id === x.referenceTypeId,
      );
      const referenceRequirement = referenceRequirementTypes.find(
        (ref: ReferenceRequirementType) => ref.id === x.requiredTypeId,
      );

      return {
        type: 'Job',
        referenceTypeId: x.referenceTypeId,
        reference: referenceType ? referenceType.longName : '-',
        // mask: x.referenceMask !== '' ? x.referenceMask : 'N/A',
        requiredType: referenceRequirement
          ? referenceRequirement.longName
          : '-',
      };
    },
  );

  const pudReferences = props.clientDetails.references.pudScreen.map(
    (x: ClientReferenceDetails) => {
      const referenceType = referenceTypes.find(
        (refType: ReferenceTypes) => refType.id === x.referenceTypeId,
      );
      const referenceRequirement = referenceRequirementTypes.find(
        (ref: ReferenceRequirementType) => ref.id === x.requiredTypeId,
      );

      return {
        type: 'Stop',
        referenceTypeId: x.referenceTypeId,
        reference: referenceType ? referenceType.longName : '-',
        // mask: x.referenceMask !== '' ? x.referenceMask : 'N/A',
        requiredType: referenceRequirement
          ? referenceRequirement.longName
          : '-',
      };
    },
  );

  return jobReferences.concat(pudReferences);
});

const newReferenceDetails = () => {
  referenceDetails.value = new ClientReferenceDetails();
  referenceDetailsDialogIsOpen.value = true;
  isNew.value = true;
};

const cancelReferenceDetails = () => {
  referenceDetails.value = null;
  referenceDetailsDialogIsOpen.value = false;
  isNew.value = false;
};

const updateReferenceDetails = () => {
  if (!referenceDetails.value || !validate.value) {
    return;
  }

  if (selectedReferenceType.value === 1) {
    const indexToUpdate =
      props.clientDetails.references.mainJobScreen.findIndex(
        (x: ClientReferenceDetails) =>
          x.referenceTypeId === referenceDetails.value!.referenceTypeId,
      );
    props.clientDetails.references.mainJobScreen.splice(
      indexToUpdate,
      1,
      referenceDetails.value,
    );
  } else {
    const indexToUpdate = props.clientDetails.references.pudScreen.findIndex(
      (x: ClientReferenceDetails) =>
        x.referenceTypeId === referenceDetails.value!.referenceTypeId,
    );
    props.clientDetails.references.pudScreen.splice(
      indexToUpdate,
      1,
      referenceDetails.value,
    );
  }
  cancelReferenceDetails();
  emit('saveParentDocument');
};

const addReferenceDetails = () => {
  if (!referenceDetails.value || !validate.value) {
    return;
  }
  if (selectedReferenceType.value === 1) {
    props.clientDetails.references.mainJobScreen.push(referenceDetails.value);
  } else {
    props.clientDetails.references.pudScreen.push(referenceDetails.value);
  }
  cancelReferenceDetails();
  emit('saveParentDocument');
};

const editReferenceDetails = (type: string, referenceTypeId: number) => {
  isNew.value = false;
  referenceDetails.value = null; // Reset referenceDetails
  let reference: ClientReferenceDetails | undefined;
  if (type === 'Job') {
    selectedReferenceType.value = 1;
    reference = props.clientDetails.references.mainJobScreen.find(
      (x: ClientReferenceDetails) => x.referenceTypeId === referenceTypeId,
    );
  } else {
    selectedReferenceType.value = 2;
    reference = props.clientDetails.references.pudScreen.find(
      (x: ClientReferenceDetails) => x.referenceTypeId === referenceTypeId,
    );
  }

  if (reference) {
    referenceDetails.value = JSON.parse(JSON.stringify(reference));
    referenceDetailsDialogIsOpen.value = true;
  }
};

const removeReference = (): void => {
  if (!referenceDetails.value) {
    return;
  }
  if (selectedReferenceType.value === 1) {
    const indexToRemove =
      props.clientDetails.references.mainJobScreen.findIndex(
        (x: ClientReferenceDetails) =>
          x.referenceTypeId === referenceDetails.value!.referenceTypeId,
      );
    if (indexToRemove !== -1) {
      props.clientDetails.references.mainJobScreen.splice(indexToRemove, 1);
    }
  } else {
    const pudIndexToRemove = props.clientDetails.references.pudScreen.findIndex(
      (x: ClientReferenceDetails) =>
        x.referenceTypeId === referenceDetails.value!.referenceTypeId,
    );
    if (pudIndexToRemove !== -1) {
      props.clientDetails.references.pudScreen.splice(pudIndexToRemove, 1);
    }
  }
  cancelReferenceDetails();
  emit('saveParentDocument');
};

watch(selectedReferenceType, () => {
  if (
    isNew.value &&
    referenceDetails.value &&
    referenceDetailsDialogIsOpen.value
  ) {
    referenceDetails.value.requiredTypeId = 1;
  }
});
</script>

<style scoped>
.add-reference-btn {
  position: absolute;
  top: 40px;
  right: 10px;
  z-index: 10 !important;
}
</style>
