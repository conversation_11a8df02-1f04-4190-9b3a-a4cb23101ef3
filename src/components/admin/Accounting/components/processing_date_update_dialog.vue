<template>
  <v-layout>
    <v-dialog
      v-model="dialogIsActive"
      width="450px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <template v-slot:activator="{ on }">
        <v-list-tile
          v-on="on"
          @click="() => null"
          style="width: 100%"
          :disabled="!isAuthorised()"
        >
          <v-list-tile-action>
            <v-icon :disabled="!isAuthorised()" size="18"
              >fal fa-calendar-alt</v-icon
            >
          </v-list-tile-action>
          <v-list-tile-content>
            <v-list-tile-title>
              <span style="text-transform: none; font-weight: 400"
                >Edit Processing Date</span
              >
            </v-list-tile-title>
          </v-list-tile-content>
        </v-list-tile>
      </template>
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <div>
            <span class="mr-2">Invoice Processing Date</span>

            <InformationTooltip :right="true" :tooltipType="HealthLevel.INFO">
              <v-layout slot="content" row wrap>
                <v-flex md12>
                  <p class="mb-1" slot="content">
                    The processing date is utilised against the trading terms
                    when calculating the due date for each invoice.
                  </p>
                </v-flex>
              </v-layout>
            </InformationTooltip>
          </div>
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogIsActive = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-layout
              row
              wrap
              class="body-scrollable--65 body-min-height--65"
              pa-3
            >
              <v-flex md12>
                <v-layout pa-2 wrap>
                  <v-flex md12 v-if="isDraft" class="mb-2">
                    <v-alert type="warning" :value="true">
                      <span
                        >You are about to update the processing date on this
                        draft run. Please note:</span
                      >

                      <ul>
                        <li>
                          <span>
                            The processing date will impact the due date for
                            each client invoice.
                          </span>
                        </li>
                      </ul>
                    </v-alert>
                  </v-flex>
                  <v-flex md12>
                    <DatePickerBasic
                      @setEpoch="setEditedProcessingDate"
                      :hideIcon="true"
                      labelName="Select Processing Date"
                      :boxInput="true"
                      :epochTime="editedProcessingDate"
                      :hintText="'The processing date for this invoice run.'"
                      :showHint="true"
                    >
                    </DatePickerBasic>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-spacer></v-spacer>
              <v-btn depressed color="green" @click="confirm">
                <span v-if="!isDraft">confirm</span>
                <span v-if="isDraft">confirm & Update Draft</span>
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import { ref, Ref, watch } from 'vue';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { returnEndOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';

const props = withDefaults(
  defineProps<{
    processingDate?: number;
    isDraft?: boolean;
    isLoadingUserActionRequest?: boolean;
  }>(),
  {
    processingDate: 0,
    isDraft: false,
    isLoadingUserActionRequest: false,
  },
);

const editedProcessingDate: Ref<number> = ref(props.processingDate);
const dialogIsActive: Ref<boolean> = ref(false);

const emit = defineEmits(['updateProcessingDate', 'closeMenuOptions']);

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

function confirm(): void {
  emit('updateProcessingDate', editedProcessingDate.value);
  emit('closeMenuOptions');
  dialogIsActive.value = false;
}

function setEditedProcessingDate(epoch: number): void {
  if (epoch !== null) {
    editedProcessingDate.value = returnEndOfDayFromEpoch(epoch);
  }
  return;
}

// Watcher for processingDate to match selected Week Ending Date
watch(
  () => props.processingDate,
  (newProcessingDate) => {
    editedProcessingDate.value = newProcessingDate;
  },
);
</script>
