<template>
  <v-layout>
    <v-dialog
      v-model="dialogIsActive"
      width="450px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Problems Found</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogIsActive = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-layout
              row
              wrap
              class="body-scrollable--65 body-min-height--65"
              pa-3
            >
              <v-flex md12>
                <v-alert type="warning" :value="true">
                  <span
                    >There appears to be problems with data for one or more
                    clients. Please find issues via the exclamation icon within
                    the table row.</span
                  >
                </v-alert>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-spacer></v-spacer>
              <v-btn depressed color="blue" @click="dialogIsActive = false">
                Okay
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import { WritableComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    showDialog?: boolean;
  }>(),
  {
    showDialog: false,
  },
);

const emit = defineEmits(['setDialog']);

const dialogIsActive: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.showDialog;
  },
  set(value: boolean): void {
    emit('setDialog', value);
  },
});
</script>
