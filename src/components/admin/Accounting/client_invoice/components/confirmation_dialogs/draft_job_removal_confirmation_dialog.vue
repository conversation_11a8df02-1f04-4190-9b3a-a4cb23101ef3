<template>
  <v-layout>
    <v-dialog
      v-model="dialogIsActive"
      width="450px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <template v-slot:activator="{ on }">
        <v-list-tile
          v-on="on"
          :disabled="selectedJobIds.length === 0 || !isAuthorised()"
          @click="() => null"
        >
          <v-list-tile-action>
            <v-icon
              size="18"
              class="pr-2"
              :disabled="selectedJobIds.length === 0 || !isAuthorised()"
              >far fa-minus-circle</v-icon
            >
          </v-list-tile-action>
          <v-list-tile-content>
            <v-list-tile-title class="pr-2 ma-0" style="width: 200px">
              <span class="pr-2" style="text-transform: none; font-weight: 400"
                >Remove Selected</span
              >
            </v-list-tile-title>
          </v-list-tile-content>
        </v-list-tile>
      </template>
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Remove Selected Jobs From Draft</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogIsActive = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-layout
              row
              wrap
              class="body-scrollable--65 body-min-height--65"
              pa-3
            >
              <v-flex md12>
                <v-alert type="warning" :value="true">
                  <span>{{ alertWarningMessage }}</span>
                  <ul>
                    <li
                      v-for="(listItem, index) of dotPointListItems"
                      :key="index"
                    >
                      <span>
                        {{ listItem }}
                      </span>
                    </li>
                  </ul>
                </v-alert>
                <v-layout pa-2>
                  <span>
                    {{ confirmationMessage }}
                  </span>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn color="error" outline @click="dialogIsActive = false">
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn depressed color="blue" @click="confirm">
                {{ buttonText }}
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import { ComputedRef, ref, Ref, computed } from 'vue';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';

const props = withDefaults(
  defineProps<{
    selectedJobIds?: number[] | string[];
    totalNumberOfJobsInDraft?: number;
    isContracts?: boolean;
  }>(),
  {
    selectedJobIds: () => [],
    isContracts: false,
    totalNumberOfJobsInDraft: 0,
  },
);

const dialogIsActive: Ref<boolean> = ref(false);

const emit = defineEmits([
  'cancelDraftRun',
  'removeSelectedJobsFromDraft',
  'closeMenuOptions',
]);

const isDraftCancel = computed(() => {
  return props.selectedJobIds.length === props.totalNumberOfJobsInDraft;
});

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

// return what the user will be removing as type string. For Client and RCTI the user will be removing Jobs. For Equipment hire the user will be removing contracts
const removalType: ComputedRef<string> = computed(() => {
  let removalType: string = '';
  if (!props.isContracts) {
    removalType = props.selectedJobIds.length > 1 ? 'jobs' : 'job';
  } else {
    removalType = props.selectedJobIds.length > 1 ? 'contracts' : 'contract';
  }
  return removalType;
});

const alertWarningMessage: ComputedRef<string> = computed(() => {
  let alertWarningMessage: string =
    'You are about to remove ' +
    props.selectedJobIds.length +
    ' ' +
    removalType.value +
    ' from the current draft run.';

  if (dotPointListItems.value.length > 0) {
    alertWarningMessage += ' Please Note:';
  }
  return alertWarningMessage;
});

const confirmationMessage: ComputedRef<string> = computed(() => {
  let confirmationMessage: string =
    'Are you sure you have selected the correct ' +
    props.selectedJobIds.length +
    ' ' +
    removalType.value +
    ' and wish to ';

  confirmationMessage += isDraftCancel.value
    ? ' cancel the draft run?'
    : 'remove them from the draft?';

  return confirmationMessage;
});

const dotPointListItems: ComputedRef<string[]> = computed(() => {
  if (!isDraftCancel.value) {
    return [];
  }
  return ['The draft will be cancelled as it will be empty.'];
});

const buttonText: ComputedRef<string> = computed(() => {
  if (!isDraftCancel.value) {
    return (
      'Confirm and Remove ' +
      props.selectedJobIds.length +
      ' ' +
      removalType.value
    );
  }
  return 'Confirm and Cancel Draft';
});

function confirm() {
  if (isDraftCancel.value) {
    emit('cancelDraftRun');
  } else {
    emit('removeSelectedJobsFromDraft');
  }
  emit('closeMenuOptions');
  dialogIsActive.value = false;
}
</script>
