<v-layout>
  <v-flex md12>
    <v-data-table
      :item-key="'_id'"
      :headers="tableHeaders"
      :items="ledgerTableList"
      hide-actions
      :loading="isLoading"
      class="default-table-dark ledger-table gd-dark-theme"
      :class="[{'ledger-table-selected-row': selectedLedgerId}, {'search-ledger-table': isSearchLedger}, {'ledger-table-dialog' : isInDialog}]"
    >
      <template v-slot:items="props">
        <tr
          @click="setSelectedLedger(props.item._id, props.item.jobIds, props.item.type); props.expanded = !props.expanded;"
          v-show="selectedLedgerId === null || selectedLedgerId === props.item._id"
        >
          <td class="inner-table__cell text-left ledger-weekEnding-column-cell">
            {{ props.item.weekEndingDate }}
          </td>
          <td class="inner-table__cell text-left">
            {{ props.item.emailedDate }}
          </td>
          <td class="inner-table__cell text-left">
            {{ props.item.invoiceNumber }}
          </td>
          <td class="inner-table__cell text-left">
            {{ props.item.typeDisplayName }}
          </td>
          <td class="inner-table__cell text-left" v-if="!isClientPortal">
            {{props.item.recipientName}}
          </td>
          <td class="inner-table__cell text-left">{{ props.item.dueDate}}</td>
          <td class="inner-table__cell text-right">
            <span>{{ props.item.debit }}</span>
          </td>
          <td class="inner-table__cell text-right">
            <span>{{ props.item.credit }}</span>
          </td>
          <td class="inner-table__cell string-type" style="text-align: right">
            <div class="pr-2">
              <v-tooltip bottom class="pr-3">
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn
                    v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  'DOWNLOAD',
                )
              "
                    flat
                    @click.native.stop="viewInvoiceDocument(props.item._id, props.item.type, 'DOWNLOAD')"
                    icon
                    color="accent"
                    v-on="{ ...tooltip }"
                    class="ma-0"
                    :disabled="generatingInvoices"
                  >
                    <v-icon size="25"> downloading </v-icon>
                  </v-btn>
                </template>
                <span>Download Invoice</span>
              </v-tooltip>
              <v-tooltip bottom class="pr-2">
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn
                    v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  'EMAIL',
                )
              "
                    flat
                    @click.native.stop="viewInvoiceDocument(props.item._id, props.item.type, 'EMAIL')"
                    icon
                    color="orange"
                    v-on="{ ...tooltip }"
                    class="ma-0"
                    :disabled="generatingInvoices"
                  >
                    <v-icon size="25"> forward_to_inbox </v-icon>
                  </v-btn>
                </template>
                <span>Email Preview</span>
              </v-tooltip>
            </div>
          </td>
        </tr>
      </template>
      <template v-slot:expand="props" v-if="expandableTable">
        <div class="inner-table__container" id="inner-table__container">
          <v-layout class="inner-table__container--row first-row">
            <v-flex md12>
              <jobListTable
                :jobList="jobList"
                :isInnerTable="true"
                :selectionIsEnabled="false"
                :isClientNameVisible="true"
              />
            </v-flex>
          </v-layout>
        </div>
      </template>
    </v-data-table>

    <v-layout justify-center>
      <Pagination
        @pageIncrement="pageIncrement"
        :pagination="pagination"
        @change="getLedgerDetailsList"
        :disabled="selectedLedgerId !== null"
        :rowsPerPage.sync="rowsPerPage"
      />
    </v-layout>
  </v-flex>

  <JobDetailsDialog
    v-if="showJobDetailsDialog && selectedJobDetails"
    :jobDetails="selectedJobDetails"
    :showJobDetailsDialog="showJobDetailsDialog"
    :isJobSearchScreen="true"
  ></JobDetailsDialog>
</v-layout>
