<template>
  <div class="sales-management">
    <v-layout class="top-panel" justify-end>
      <GButton
        icon="fal fa-plus"
        :iconRight="true"
        @click="showAddHistoryDialog"
        small
        v-if="isAuthorised()"
        >Add New Record</GButton
      >
    </v-layout>
    <v-layout justify-space-between class="px-2 pt-2 pb-1">
      <v-flex md2 px-1>
        <v-select
          solo
          flat
          label="Search By"
          v-model="selectedSearchByOption"
          :items="searchByOptions"
          item-value="id"
          item-text="longName"
          background-color="blue"
          hint="Search by"
          @change="resetTextInputField"
          persistent-hint
        ></v-select>
      </v-flex>
      <v-flex md3 px-1>
        <v-form ref="form">
          <SelectEntity
            v-if="selectedSearchByOption === 'CLIENT'"
            :entityTypes="[EntityType.CLIENT]"
            :id.sync="searchByOptionValue"
            :rules="[validate.required]"
            :customLabel="'Client Select'"
            :requiredMarker="false"
            :disabled="isEditingExistingHistory"
        /></v-form>
        <v-autocomplete
          v-if="selectedSearchByOption === 'USER'"
          solo
          flat
          v-model="searchByOptionValue"
          :items="userSelectList"
          item-value="_id"
          item-text="firstName"
          class="v-solo-custom"
          color="light-blue"
          label="User Select"
          clearable
        >
          <template v-slot:selection="{ item }">
            <span>{{ item.firstName }} {{ item.lastName }}</span>
          </template>
          <template v-slot:item="{ item }">
            <span>{{ item.firstName }} {{ item.lastName }}</span>
          </template>
        </v-autocomplete>
      </v-flex>
      <v-flex md2 px-1>
        <DatePickerBasic
          @setEpoch="setStartDate"
          :soloInput="true"
          :labelName="'Start Date'"
          :yearOnly="false"
          :epochTime="startDate"
          :isRequired="true"
        />
      </v-flex>

      <v-flex md2 px-1>
        <DatePickerBasic
          @setEpoch="setEndDate"
          :soloInput="true"
          :labelName="'End Date'"
          :yearOnly="false"
          :epochTime="endDate"
          :isRequired="true"
        />
      </v-flex>
      <v-spacer></v-spacer>
      <v-flex md3 px-3>
        <v-btn
          outline
          color="white"
          block
          @click="searchWithCurrentCriteria"
          :loading="isRequestingTableData"
          class="view-details-button"
          >Search</v-btn
        >
      </v-flex>
    </v-layout>

    <v-divider />

    <v-layout>
      <v-flex md12 pa-3>
        <v-data-table
          :headers="tableHeaders"
          :items="salesManagementHistoryList"
          class="gd-dark-theme accounting-data-table"
          item-key="id"
          :rows-per-page-items="[50, 100]"
        >
          <v-progress-linear
            model-value="progress"
            color="#ffa000"
            indeterminate
          ></v-progress-linear>
          <template v-slot:items="props">
            <tr class="sales-management-table-row" style="position: relative">
              <td>
                {{ returnFormattedDate(props.item.historyDetails.createdTime) }}
              </td>
              <td>{{ props.item.clientName }}</td>
              <td>{{ props.item.userName }}</td>
              <td>
                {{
                  returnFormattedDate(
                    props.item.historyDetails.commissionStartDate,
                  )
                }}
              </td>
              <td>
                {{
                  returnFormattedDate(
                    props.item.historyDetails.commissionEndDate,
                  )
                }}
              </td>
              <td>
                {{
                  props.item.historyDetails.additionalNotes
                    ? props.item.historyDetails.additionalNotes
                    : '-'
                }}
              </td>
              <td>
                <v-icon
                  :color="props.item.hasAttachment ? 'success' : 'grey'"
                  size="16"
                  class="pl-3"
                >
                  {{
                    props.item.hasAttachment ? 'fas fa-check' : 'fal fa-times'
                  }}
                </v-icon>

                <v-tooltip top v-if="props.item.hasAttachment">
                  <template #activator="{ on, attrs }">
                    <v-icon
                      v-bind="attrs"
                      v-on="on"
                      size="24"
                      @click="
                        downloadSelectedAttachment(
                          props.item.historyDetails.supportingDocumentation.id,
                        )
                      "
                      color="info"
                      class="pl-3"
                    >
                      downloading
                    </v-icon>
                  </template>
                  <span>Download Attachment</span>
                </v-tooltip>
              </td>

              <td>
                <v-tooltip top v-if="isAuthorised()">
                  <template #activator="{ on, attrs }">
                    <v-icon
                      v-bind="attrs"
                      v-on="on"
                      size="22"
                      @click="showEditHistoryDialog(props.item.id)"
                      color="orange"
                      class="pl-2"
                    >
                      edit_square
                    </v-icon>
                  </template>
                  <span>Edit Details</span>
                </v-tooltip>
              </td>
            </tr>
          </template>
        </v-data-table>
      </v-flex>
    </v-layout>

    <ContentDialog
      :showDialog.sync="dialogController"
      :title="
        !isEditingExistingHistory
          ? 'Add New Sales Record'
          : 'Edit Record Details'
      "
      width="800px"
      contentPadding="pa-0"
      @cancel="dialogController = false"
      @confirm="saveSalesManagementHistory"
      :isLoading="isAwaitingSaveResponse"
      :confirmBtnText="
        !isEditingExistingHistory ? `Save New Record` : `Save Changes`
      "
      :showActionButton="isEditingExistingHistory && isDeleteActionAvailable"
      @action="setRecordToInactive"
      actionBtnText="Delete Record"
      :actionRequiresConfirmation="true"
      actionConfirmationMessage="You are about to remove a BDM sales record. This action cannot be undone. Do you wish to proceed?"
    >
      <v-layout
        class="body-scrollable--75 body-min-height--65 pa-3"
        justify-center
      >
        <v-flex offset-md-1 md10>
          <v-form ref="salesManagementDialogForm">
            <v-layout row wrap v-if="currentSalesManagementHistory !== null">
              <v-flex md12>
                <v-alert
                  class="mb-3"
                  :value="formHasErrors.length > 0"
                  type="warning"
                >
                  <ul>
                    <li v-for="(error, index) in formHasErrors" :key="index">
                      {{ error }}
                    </li>
                  </ul>
                </v-alert>
              </v-flex>
              <v-flex
                md3
                class="pt-2 subheader--faded text-md-right form-field-required-marker"
                >Select a Client
              </v-flex>
              <v-flex md9 pl-3>
                <SelectEntity
                  :entityTypes="[EntityType.CLIENT]"
                  :id.sync="selectedClientInDialog"
                  :rules="[validate.required]"
                  :customLabel="'Select a Client'"
                  :emitSelected="true"
                  :disabled="isEditingExistingHistory || isAwaitingSaveResponse"
                  @selected="requestHistoryForClient"
                  :requiredMarker="false"
                />
              </v-flex>
              <v-flex
                md3
                class="pt-2 subheader--faded text-md-right form-field-required-marker"
                >Start Budget {{ isEditingExistingHistory ? 'Date' : 'Period' }}
              </v-flex>
              <v-flex md9 pl-3>
                <DatePickerBasic
                  @setEpoch="setStartCommissionDate"
                  :soloInput="true"
                  :labelName="
                    isEditingExistingHistory
                      ? ''
                      : 'Budget period lasts one (1) year from selected date'
                  "
                  :yearOnly="false"
                  :epochTime="currentSalesManagementHistory.commissionStartDate"
                  :isRequired="true"
                  :hideIcon="true"
                  :validate="validate"
                  :formDisabled="
                    (isEditingExistingHistory && !isAuthorisedAdmin()) ||
                    isAwaitingSaveResponse
                  "
                />
              </v-flex>
              <v-flex
                md3
                class="pt-2 subheader--faded text-md-right form-field-required-marker"
                v-if="isEditingExistingHistory"
                >End Budget Date
              </v-flex>
              <v-flex md9 pl-3 v-if="isEditingExistingHistory">
                <DatePickerBasic
                  @setEpoch="setEndCommissionDate"
                  :soloInput="true"
                  :yearOnly="false"
                  :epochTime="currentSalesManagementHistory.commissionEndDate"
                  :isRequired="true"
                  :hideIcon="true"
                  :validate="validate"
                  :formDisabled="isAwaitingSaveResponse"
                />
              </v-flex>
              <v-flex md12 px-2 pb-2><v-divider></v-divider></v-flex>
              <v-flex
                md3
                class="pt-2 subheader--faded text-md-right form-field-required-marker"
                >Select a BDM
              </v-flex>
              <v-flex md9 pl-3>
                <v-autocomplete
                  solo
                  flat
                  v-model="currentSalesManagementHistory.userId"
                  :items="userSelectList"
                  item-value="_id"
                  item-text="firstName"
                  color="orange"
                  :rules="[validate.required]"
                  label="Select a User"
                  :disabled="
                    !isValidClientSelection ||
                    isEditingExistingHistory ||
                    isAwaitingSaveResponse
                  "
                  class="v-solo-custom"
                  clearable
                >
                  <template v-slot:selection="{ item }">
                    <span>{{ item.firstName }} {{ item.lastName }}</span>
                  </template>
                  <template v-slot:item="{ item }">
                    <span>{{ item.firstName }} {{ item.lastName }}</span>
                  </template>
                </v-autocomplete>
              </v-flex>

              <v-flex md3 class="pt-2 subheader--faded text-md-right"
                >Additional Notes
              </v-flex>
              <v-flex md9 pl-3>
                <v-textarea
                  placeholder="Please add a note..."
                  class="v-solo-custom"
                  v-model="currentSalesManagementHistory.additionalNotes"
                  solo
                  flat
                  rows="3"
                  color="light-blue"
                  :disabled="!isValidClientSelection || isAwaitingSaveResponse"
                ></v-textarea>
              </v-flex>
              <v-flex md3 class="pt-2 subheader--faded text-md-right"
                >Supporting Documentation
              </v-flex>
              <v-flex md8 pl-3 mb-2>
                <file-upload
                  imageLabel="Upload Documentation"
                  :attachmentSingle="true"
                  :documentTypeId="27"
                  :attachment="supportingDocumentation"
                  :formDisabled="
                    !isValidClientSelection ||
                    isEditingExistingHistory ||
                    isAwaitingSaveResponse
                  "
                  :isLoading.sync="isLoading"
                >
                </file-upload>
              </v-flex>
              <v-flex md1 v-if="isEditingExistingHistory"></v-flex>
              <v-flex
                md3
                class="pt-2 subheader--faded text-md-right"
                v-if="isEditingExistingHistory"
                >Created On
              </v-flex>
              <v-flex md9 pl-3 pt-2 v-if="isEditingExistingHistory">
                <span class="font-italic">
                  {{
                    returnFormattedDate(
                      currentSalesManagementHistory.createdTime,
                      'DD/MM/YY HH:mm a',
                    )
                  }}
                  by
                  {{ currentSalesManagementHistory.createdBy }}
                </span>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import FileUpload from '@/components/common/file-upload/index.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { downloadAttachment } from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  hasAdminOrHeadOfficeRole,
  hasAdminRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { SearchByOption } from '@/interface-models/Generic/SearchByOption';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import SalesManagementHistory from '@/interface-models/SalesManagement/SalesManagementHistory';
import SearchSalesManagementHistoryRequest from '@/interface-models/SalesManagement/SearchSalesManagementHistoryRequest';
import {
  CompanyUserDetails,
  CompanyUserWithAuthDetails,
} from '@/interface-models/User/CompanyUserDetails';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useUserActivityStore } from '@/store/modules/UserActivityStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { computed, onMounted, Ref, ref } from 'vue';

enum SearchCategoryOptions {
  USER = 'USER',
  CLIENT = 'CLIENT',
}

interface SalesManagementHistoryListItem {
  id: string;
  clientName: string;
  userName: string;
  hasAttachment: boolean;
  historyDetails: SalesManagementHistory;
}

const componentTitle: string = 'Sales Management';
const isLoading: Ref<boolean> = ref(false);
const isRequestingTableData: Ref<boolean> = ref(false);
const isAwaitingSaveResponse: Ref<boolean> = ref(false);
const isViewingAddEditDialog: Ref<boolean> = ref(false);
const isEditingExistingHistory: Ref<boolean> = ref(false);
const isDeleteActionAvailable: Ref<boolean> = ref(false);

const companyDetailsStore = useCompanyDetailsStore();
const userManagementStore = useUserManagementStore();
const userActivityStore = useUserActivityStore();

const startDate: Ref<number> = ref(
  moment
    .tz(companyDetailsStore.userLocale)
    .startOf('day')
    .subtract(3, 'months')
    .valueOf(),
);
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());

const salesManagementDialogForm = ref<any>(null);

const salesManagementHistoryList: Ref<SalesManagementHistoryListItem[]> = ref(
  [],
);
const currentSalesManagementHistory: Ref<SalesManagementHistory | null> =
  ref(null);

const searchByOptionValue: Ref<string> = ref('');
const selectedClientInDialog: Ref<string> = ref('');

const currentCommissionPeriodsForClient: Ref<number[][]> = ref([]);

const supportingDocumentation: Ref<Attachment> = ref(new Attachment());
const validate = validationRules;

// Selections from DROP DOWNS
const selectedSearchByOption: Ref<SearchCategoryOptions> = ref(
  SearchCategoryOptions.USER,
);

const searchByOptions: SearchByOption[] = [
  {
    id: SearchCategoryOptions.USER,
    longName: 'Team Member',
  },
  {
    id: SearchCategoryOptions.CLIENT,
    longName: 'Client',
  },
];

const tableHeaders: TableHeader[] = [
  {
    text: 'Created Time',
    align: 'left',
    sortable: true,
    value: 'historyDetails.createdTime',
  },

  {
    text: 'Client',
    align: 'left',
    sortable: true,
    value: 'historyDetails.clientId',
  },
  {
    text: 'BDM',
    align: 'left',
    sortable: true,
    value: 'userName',
  },
  {
    text: 'Start Date',
    align: 'left',
    sortable: true,
    value: 'historyDetails.commissionStartDate',
  },
  {
    text: 'End Date',
    align: 'left',
    sortable: true,
    value: 'historyDetails.commissionEndDate',
  },
  {
    text: 'Notes',
    align: 'left',
    sortable: false,
    value: 'additionalNotes',
  },
  {
    text: 'Supp. Doc',
    align: 'left',
    sortable: true,
    value: 'hasAttachment',
  },
  {
    text: 'Action',
    align: 'left',
    sortable: true,
    value: 'edit',
  },
];

const isValidClientSelection = computed(() => {
  const isSelected = selectedClientInDialog.value !== '';
  return isSelected && formHasErrors.value.length === 0;
});

// Check if there is overlap for the selected client for the selected dates with past records for that client, and constructs a list of errors to be displayed in a UI alert
const formHasErrors = computed(() => {
  const errors: string[] = [];

  if (
    currentCommissionPeriodsForClient.value.length > 0 &&
    currentSalesManagementHistory.value !== null
  ) {
    const salesHistory = currentSalesManagementHistory.value;

    // Check if there is overlap with epoch ranges fetched from search API
    const hasOverlap: boolean = currentCommissionPeriodsForClient.value
      .filter((c) => c.length === 2 && c[0] !== undefined && c[1] !== undefined)
      .some((c) => {
        const start = salesHistory.commissionStartDate;
        const end = salesHistory.commissionEndDate;
        const min = c[0];
        const max = c[1];
        return (start >= min && start <= max) || (end >= min && end <= max);
      });

    if (hasOverlap) {
      errors.push('This client is currently allocated to a BDM');
    }
  }

  return errors;
});
// Controls dialog visibility, and resets local working variables on close
const dialogController = computed({
  get: () => isViewingAddEditDialog.value,
  set: (value: boolean) => {
    isViewingAddEditDialog.value = value;
    if (!value) {
      currentSalesManagementHistory.value = null;
      selectedClientInDialog.value = '';
      supportingDocumentation.value = new Attachment();
      currentCommissionPeriodsForClient.value = [];
      isEditingExistingHistory.value = false;
      isDeleteActionAvailable.value = false;
    }
  },
});
// Show the 'Add New' dialog. Create a brand new object and set the dates to the default values
function showAddHistoryDialog() {
  currentSalesManagementHistory.value = new SalesManagementHistory();
  setStartEndCommissionDates(
    currentSalesManagementHistory.value,
    moment().valueOf(),
  );
  isEditingExistingHistory.value = false;
  isDeleteActionAvailable.value = false;
  dialogController.value = true;
}
// Accepts id of record to edit, copies selected item to working copy, and
// sets local working variables with values from the selected record.
function showEditHistoryDialog(id: string) {
  const foundHistory = salesManagementHistoryList.value.find(
    (h) => h.id === id,
  );
  if (!foundHistory) {
    showAppNotification('Something went wrong. Could not edit selected item.');
    return;
  }
  selectedClientInDialog.value = foundHistory.historyDetails.clientId;
  currentSalesManagementHistory.value = Object.assign(
    new SalesManagementHistory(),
    foundHistory.historyDetails,
  );
  supportingDocumentation.value = foundHistory.historyDetails
    .supportingDocumentation
    ? Object.assign(
        new Attachment(),
        foundHistory.historyDetails.supportingDocumentation,
      )
    : new Attachment();
  // Show the button for deleting the record
  isDeleteActionAvailable.value = true;
  isEditingExistingHistory.value = true;
  dialogController.value = true;
}

// Handle startDate selection from date picker
function setStartDate(startDateEpoch: number) {
  startDate.value = startDateEpoch;
}
// Handle endDate selection from date picker
function setEndDate(endDateEpoch: number) {
  endDate.value = endDateEpoch;
}
// Handle endDate selection from date picker
function setStartCommissionDate(startDate: number) {
  if (currentSalesManagementHistory.value !== null) {
    setStartEndCommissionDates(currentSalesManagementHistory.value, startDate);
  }
}
// Handle endDate selection from date picker
function setEndCommissionDate(endDate: number) {
  if (currentSalesManagementHistory.value !== null) {
    endDate = returnEndOfDayFromEpoch(endDate);
    currentSalesManagementHistory.value.commissionEndDate = endDate;
  }
}
// Set the currently edited record to inactive. This sets the dates to 0,
// meaning it will no longer show up in searches (but will still be stored in
// mongo). This is only available on the day that records were created, such
// that reporting isn't impacted by removing valid records that could have
// paid a BDM
async function setRecordToInactive() {
  if (currentSalesManagementHistory.value === null) {
    showAppNotification('Something went wrong. Record could not be removed.');
    return;
  }

  const history: SalesManagementHistory = {
    ...currentSalesManagementHistory.value,
  };
  history.commissionStartDate = 0;
  history.commissionEndDate = 0;

  // Send request and handle response
  isAwaitingSaveResponse.value = true;
  const saveResult =
    await userActivityStore.saveSalesManagementHistory(history);
  if (saveResult) {
    showAppNotification('Update successful.', HealthLevel.SUCCESS);
    dialogController.value = false;
    searchWithCurrentCriteria();
  } else {
    showAppNotification(GENERIC_ERROR_MESSAGE, HealthLevel.ERROR);
  }
  isAwaitingSaveResponse.value = false;
}

// Send search request that fetches ALL records for a given client (does not
// use date). This is used to check what date ranges are available, such that
// there are no overlapping dates for new records
async function requestHistoryForClient(clientId: string) {
  if (!clientId) {
    return;
  }
  const request: SearchSalesManagementHistoryRequest = {
    company: sessionManager.getCompanyId(),
    division: sessionManager.getDivisionId(),
    clientId,
  };
  // Send search request and handle results
  isRequestingTableData.value = true;
  const results = await userActivityStore.searchSalesManagementHistory(request);
  processSalesHistoryForClient(results ?? []);
  isRequestingTableData.value = false;
}

// Accepts SalesManagementHistory and start date. Sets start date to the respective startOfDay time, and sets the end date to a year from that date (end of day)
function setStartEndCommissionDates(
  salesHistory: SalesManagementHistory,
  startDate: number,
) {
  const startOfToday = returnStartOfDayFromEpoch(startDate);
  const oneYearLater =
    moment(startDate)
      .tz(companyDetailsStore.userLocale)
      .add(1, 'years')
      .valueOf() - 1;

  salesHistory.commissionStartDate = startOfToday;
  salesHistory.commissionEndDate = oneYearLater;
}

// Fetch full size attachment for id
async function downloadSelectedAttachment(id: string) {
  const result = await useAttachmentStore().getAttachmentById(id);
  if (result) {
    downloadAttachment(result);
  }
}

function resetTextInputField() {
  searchByOptionValue.value = '';
}
// Send search request with current parameters
async function searchWithCurrentCriteria(): Promise<void> {
  const request: SearchSalesManagementHistoryRequest = {
    company: '',
    division: '',
    clientId: null,
    userId: null,
    startEpoch: null,
    endEpoch: null,
  };
  request.company = sessionManager.getCompanyId();
  request.division = sessionManager.getDivisionId();
  request.startEpoch = returnStartOfDayFromEpoch(startDate.value);
  request.endEpoch = returnEndOfDayFromEpoch(endDate.value);
  // Conditionally set the appropriate field in request object depending on
  // which option is selected in the UI dropdown select
  const searchValue: string = searchByOptionValue.value;
  switch (selectedSearchByOption.value) {
    case SearchCategoryOptions.CLIENT:
      request.clientId = searchValue ? searchValue : null;
      break;
    case SearchCategoryOptions.USER:
      request.userId = searchValue ? searchValue : null;
      break;
  }
  // Send search request and handle response
  isRequestingTableData.value = true;
  const results = await userActivityStore.searchSalesManagementHistory(request);
  processSalesHistoryForTable(results ?? []);
  isRequestingTableData.value = false;
}
// Save the SalesManagementHistory object
async function saveSalesManagementHistory() {
  if (!salesManagementDialogForm.value.validate()) {
    return;
  }
  if (currentSalesManagementHistory.value === null) {
    return;
  }
  const request: SalesManagementHistory = currentSalesManagementHistory.value;
  // If it is a new object, we should additional information about create time, company/division etc.
  if (!isEditingExistingHistory.value) {
    request.company = sessionManager.getCompanyId();
    request.division = sessionManager.getDivisionId();
    request.createdTime = moment().valueOf();
    request.createdBy = sessionManager.getActiveUser();
    request.clientId = selectedClientInDialog.value;
    // If an image has been selected (with valid id) then add that to payload. Otherwise set to null so we're not saving empty Attachment object
    if (
      supportingDocumentation.value.id &&
      supportingDocumentation.value.name
    ) {
      request.supportingDocumentation = supportingDocumentation.value;
    } else {
      request.supportingDocumentation = null;
    }
  }
  // Dispatch request and close dialog
  isAwaitingSaveResponse.value = true;
  const saveResult =
    await userActivityStore.saveSalesManagementHistory(request);
  dialogController.value = false;
  if (saveResult) {
    showAppNotification('Save successful.', HealthLevel.SUCCESS);
    searchWithCurrentCriteria();
  } else {
    showAppNotification(GENERIC_ERROR_MESSAGE, HealthLevel.ERROR);
  }
  isAwaitingSaveResponse.value = false;
}

// Handles response from search API for client. Gets the current epoch time ranges in existing documents and uses it to check if there is any overly with the current date selection
function processSalesHistoryForClient(historyList: SalesManagementHistory[]) {
  const timeMap = historyList.map((i) => {
    return [i.commissionStartDate, i.commissionEndDate];
  });
  currentCommissionPeriodsForClient.value = timeMap;
}
// Handles response for search API for table. Uses the results to construct SalesManagementHistoryListItem items, which contain additional information useful for the table
function processSalesHistoryForTable(historyList: SalesManagementHistory[]) {
  const isNotEmpty = (s: string | undefined): boolean =>
    ![null, undefined, ''].includes(s);
  const listItems: SalesManagementHistoryListItem[] = historyList
    .filter((h) => isNotEmpty(h._id))
    .map((h) => {
      const id = h._id ? h._id : '';
      const foundClient = useClientDetailsStore().clientSummaryList.find(
        (c) => c.clientId === h.clientId,
      );
      const historyDetails = h;
      const clientName = foundClient ? foundClient.clientDisplayName : '-';
      const hasAttachment =
        h.supportingDocumentation !== null &&
        h.supportingDocumentation !== undefined &&
        isNotEmpty(h._id);
      const foundUser = userSelectList.value.find((u) => u._id === h.userId);
      const userName = foundUser
        ? `${foundUser.firstName} ${foundUser.lastName}`
        : '-';
      return {
        id,
        clientName,
        userName,
        hasAttachment,
        historyDetails,
      };
    });
  salesManagementHistoryList.value = listItems;
}

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

const userSelectList = computed<CompanyUserDetails[]>(() => {
  return (
    userManagementStore.companyUserWithAuthDetailsList?.map(
      (x: CompanyUserWithAuthDetails) => x.companyUser,
    ) ?? []
  );
});

// Only Admin and Head Office have access to save/edit APIs
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}
// Only Admin are able to edit both dates after the record has been created
function isAuthorisedAdmin(): boolean {
  return hasAdminRole();
}

onMounted(() => {
  userManagementStore.getCompanyUserWithAuthDetailsList();
});
</script>

<style scoped lang="scss">
.sales-management {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-y: hidden;
  padding-top: 67px;

  .sales-management-form__label {
    text-transform: uppercase;
    color: rgb(177, 177, 177);
    font-weight: 600;
  }
  .sales-management-table-row {
    .sales-management-table-row__menu {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .view-details-button {
    min-height: 46px !important;
    border: 1px solid var(--primary-light);
    border-radius: $border-radius-sm;
    background-color: var(--primary) !important;
    margin: 1px;
    &:hover {
      cursor: pointer;
      transition: 0.3s;
      background-color: var(--primary-dark) !important;
    }
  }
}

.top-panel {
  position: fixed;
  transition: 0.2s;
  background-color: var(--background-color-400);
  top: 40px;
  height: 49px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  border-bottom: 1px solid $translucent;
  padding: 0 8px;
  display: flex;
  align-items: center;
}
</style>
