<template>
  <div class="safety-checklist-history px-3">
    <div class="top-panel"></div>
    <v-layout align-center class="banner-custom" px-2>
      <v-layout column>
        <h3>Driver Checklist History</h3>
        <h4>Search for Driver compliance checklists</h4>
      </v-layout>
    </v-layout>
    <v-divider class="mt-1 mb-2"></v-divider>
    <v-form ref="safetyChecklistHistoryForm">
      <v-layout justify-end align-center class="banner-custom" px-2 pb-2>
        <v-flex md4 class="mr-1">
          <v-layout align-center>
            <span class="pr-2">Driver:</span>

            <v-flex md10>
              <SelectEntity
                :entityTypes="[EntityType.DRIVER]"
                :id.sync="selectedDriverId"
                :hideDetails="true"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md4>
          <v-layout align-center>
            <span class="pr-2">Fleet:</span>

            <v-flex md10>
              <SelectEntity
                :entityTypes="[EntityType.FLEET_ASSET]"
                :id.sync="selectedFleetAssetId"
                :hideDetails="true"
                label="Fleet"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-divider vertical class="mx-2"></v-divider>
        <v-flex md5 class="mr-1">
          <v-layout align-center>
            <v-flex mr-1>
              <DateTimeInputs
                :epochTime.sync="startRangeEpoch"
                :enableValidation="true"
                :type="DateTimeType.DATE_START_OF_DAY"
                datePrefix="From: "
                :soloInput="true"
                :boxInput="false"
                :hintTextType="HintTextType.FORMATTED_SELECTION"
                :hide-details="true"
                :clearable="true"
                :isRequired="!!endRangeEpoch"
                :readOnly="isAwaitingResponse"
                @dateTimeUpdated="sendSearchRequest(1)"
                @clear="
                  endRangeEpoch = undefined;
                  sendSearchRequest(1);
                "
              ></DateTimeInputs>
            </v-flex>
            <v-flex>
              <DateTimeInputs
                :epochTime.sync="endRangeEpoch"
                :enableValidation="true"
                :type="DateTimeType.DATE_END_OF_DAY"
                datePrefix="To: "
                :soloInput="true"
                :boxInput="false"
                :hintTextType="HintTextType.FORMATTED_SELECTION"
                :hide-details="true"
                :clearable="true"
                :isRequired="!!startRangeEpoch"
                :readOnly="isAwaitingResponse"
                @dateTimeUpdated="sendSearchRequest(1)"
                @clear="
                  startRangeEpoch = undefined;
                  sendSearchRequest(1);
                "
              ></DateTimeInputs>
            </v-flex> </v-layout
        ></v-flex>
      </v-layout>
    </v-form>
    <v-data-table
      class="gd-dark-theme bordered"
      :headers="tableHeaders"
      :items="tableItems"
      hide-actions
      :loading="isAwaitingResponse"
      :pagination.sync="paginationTable"
    >
      <template v-slot:items="data">
        <tr>
          <td class="text-xs-left">{{ data.item.date }}</td>
          <td class="text-xs-left">{{ data.item.time }}</td>
          <td class="text-xs-left">{{ data.item.driverName }}</td>
          <td class="text-xs-left">{{ data.item.mobile }}</td>
          <td class="text-xs-left">{{ data.item.csrAssignedId }}</td>
          <td class="text-xs-left">{{ data.item.checklistType }}</td>
          <td class="text-xs-right">
            <v-btn
              flat
              color="light-blue"
              @click="viewChecklistInDialog(data.item)"
              class="v-btn-confirm-custom"
              >View</v-btn
            >
          </td>
        </tr>
      </template>
    </v-data-table>
    <v-layout>
      <Pagination
        @pageIncrement="pageIncrement"
        :pagination="pagination"
        @change="sendSearchRequest"
        :rowsPerPage.sync="pagination.rowsPerPage"
        :showTotalCount="false"
        :currentPageCount="tableItems.length"
        :rowsPerPageList="[10]"
    /></v-layout>

    <ContentDialog
      :showDialog.sync="isViewingSafetyChecklistDialog"
      :title="
        viewingSafetyChecklist
          ? `${viewingSafetyChecklist.checklistType} Checklist - submitted ${viewingSafetyChecklist.date} ${viewingSafetyChecklist.time}`
          : 'View Checklist'
      "
      width="40%"
      contentPadding="pa-3"
      @cancel="isViewingSafetyChecklistDialog = false"
      :showActions="false"
    >
      <v-layout v-if="viewingSafetyChecklist">
        <v-flex md10 offset-md1>
          <v-layout align-center class="banner-custom" px-2>
            <v-layout column>
              <h3>
                {{ viewingSafetyChecklist.driverName.trim() }} -
                {{ viewingSafetyChecklist.csrAssignedId.trim() }}
              </h3>
              <h4>
                Completed: {{ viewingSafetyChecklist.date }} at
                {{ viewingSafetyChecklist.time }}
              </h4>
            </v-layout>

            <div class="tags-chip">
              {{ viewingSafetyChecklist.checklistType }} Checklist
            </div>
          </v-layout>
          <v-divider class="my-2 mb-1"></v-divider>
          <!-- Iterate over question groups -->
          <v-layout
            v-for="questionGroup in viewingSafetyChecklist.questionGroups"
            :key="questionGroup.id"
            row
            wrap
            px-2
          >
            <v-flex md12 pt-2>
              <v-layout>
                <span class="title--bold"> {{ questionGroup.title }} </span>
              </v-layout>
            </v-flex>

            <!-- Iterate over questions within each group -->
            <v-flex
              md12
              v-for="question in questionGroup.questions"
              :key="question.id"
              class="detail-item--vertical"
            >
              <v-layout
                justify-space-between
                align-center
                v-if="question.inputType !== ChecklistQuestionInputType.IMAGE"
              >
                <span class="title-text">{{ question.title }}</span>
                <span class="value-text"> {{ question.answer }} </span>
              </v-layout>
              <v-layout justify-space-between align-start v-else>
                <span class="title-text">{{ question.title }}</span>
                <span class="value-text">
                  <AttachmentThumbnailItem
                    :attachment="question.attachment"
                    v-if="question.attachment"
                  >
                  </AttachmentThumbnailItem>
                  <span v-else> N/A </span>
                </span>
              </v-layout>
            </v-flex>
          </v-layout>

          <template v-if="!!viewingSafetyChecklist.signature">
            <v-divider class="mt-1 mb-2"></v-divider>
            <v-layout justify-end align-start pt-3>
              <span class="title-text pr-3">Signed:</span>
              <span class="value-text">
                <AttachmentThumbnailItem
                  :attachment="viewingSafetyChecklist.signature"
                  :isFullSizeSignature="true"
                  v-if="viewingSafetyChecklist.signature"
                ></AttachmentThumbnailItem>
              </span>
            </v-layout>
          </template>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import AttachmentThumbnailItem from '@/components/common/attachment_thumbnail_item/attachment_thumbnail_item.vue';
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import Pagination from '@/components/common/pagination/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ChecklistQuestionInputType } from '@/interface-models/Generic/SafetyChecklist/ChecklistQuestionInputType';
import { SafetyChecklist } from '@/interface-models/Generic/SafetyChecklist/SafetyChecklist';
import { SafetyChecklistHistoryRequest } from '@/interface-models/Generic/SafetyChecklist/SafetyChecklistHistoryRequest';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import moment from 'moment';
import { computed, onMounted, Ref, ref } from 'vue';

interface SafetyChecklistDetails extends SafetyChecklist {
  driverName: string;
  mobile: string;
  csrAssignedId: string;
  date: string;
  time: string;
  checklistType: string;
}

const props = withDefaults(
  defineProps<{
    driverId?: string;
    fleetAssetId?: string;
  }>(),
  {
    driverId: '',
    fleetAssetId: '',
  },
);

// Define component data and properties
const isAwaitingResponse: Ref<boolean> = ref(false);

const page: Ref<number> = ref(1);
const rowsPerPage: number = 10;

const viewingSafetyChecklist: Ref<SafetyChecklistDetails | null> = ref(null);

const localDriverId: Ref<string | undefined> = ref('');
const localFleetAssetId: Ref<string | undefined> = ref('');

// Methods from DateTimeHelpers for formatting dates
const startRangeEpoch: Ref<number | undefined> = ref(
  returnStartOfDayFromEpoch(moment().subtract(1, 'month').valueOf()),
);
const endRangeEpoch: Ref<number | undefined> = ref(returnEndOfDayFromEpoch());

// Array to store Checklist message data
const tableItems: Ref<SafetyChecklistDetails[]> = ref([]);

const safetyChecklistHistoryForm = ref<any>(null);

const paginationTable: VPagination = {
  descending: true,
  page: 1,
  rowsPerPage: 10,
  sortBy: 'timestamp',
};

const selectedDriverId = computed<string | undefined>({
  get: () => props.driverId ?? localDriverId.value,
  set: (value) => {
    localDriverId.value = value;
    sendSearchRequest(1);
  },
});

const selectedFleetAssetId = computed<string | undefined>({
  get: () => props.fleetAssetId ?? localFleetAssetId.value,
  set: (value) => {
    localFleetAssetId.value = value;
    sendSearchRequest(1);
  },
});

// Define table headers for Checklist message history
const tableHeaders: TableHeader[] = [
  { text: 'Date', value: 'date', align: 'left' },
  { text: 'Time', value: 'time', align: 'left' },
  {
    text: 'Driver',
    value: 'driverName',
    align: 'left',
  },
  {
    text: 'Mobile',
    value: 'mobile',
    align: 'left',
  },
  {
    text: 'Fleet ID',
    value: 'csrAssignedId',
    align: 'left',
  },
  {
    text: 'Checklist Type',
    value: 'checklistType',
    align: 'left',
  },
  { text: 'Actions', value: '', align: 'right', sortable: false },
];

// Define pagination settings
const pagination = computed(() => ({
  ...paginationTable,
  page: page.value,
  rowsPerPage: rowsPerPage,
}));

const isViewingSafetyChecklistDialog = computed<boolean>({
  get: () => viewingSafetyChecklist.value !== null,
  set: (value) => {
    if (!value) {
      viewingSafetyChecklist.value = null;
    }
  },
});

function viewChecklistInDialog(checklist: SafetyChecklistDetails) {
  viewingSafetyChecklist.value = checklist;
}

// Method to increment or decrement page and trigger search
function pageIncrement(value: number) {
  page.value += value;
  sendSearchRequest();
}

// Method to send search request to fetch Checklist message history
async function sendSearchRequest(pageNumber?: number) {
  if (!!pageNumber) {
    page.value = pageNumber;
  }
  if (!safetyChecklistHistoryForm.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'Checklist Message History',
    });
    return;
  }
  if (
    (!startRangeEpoch.value && !!endRangeEpoch.value) ||
    (!!startRangeEpoch.value && !endRangeEpoch.value)
  ) {
    showNotification(
      'Please enter both Start and End dates to search by date range.',
      {
        title: 'Checklist Message History',
        type: HealthLevel.INFO,
      },
    );
    return;
  }
  const request: SafetyChecklistHistoryRequest = {
    page: page.value,
    size: rowsPerPage,
    startEpoch: startRangeEpoch.value,
    endEpoch: endRangeEpoch.value,
    driverId: selectedDriverId.value,
    fleetAssetId: selectedFleetAssetId.value,
  };

  // Send request and handle response
  isAwaitingResponse.value = true;
  const results =
    await useDriverDetailsStore().searchSafetyChecklistHistory(request);
  handleHistoryResponse(results ?? []);
  if (tableItems.value.length === 0) {
    // Display error notification if no results found
    showNotification('No results found for the selected search criteria.', {
      title: 'Driver Checklist History',
      type: HealthLevel.INFO,
    });
  }
  isAwaitingResponse.value = false;
}

// Handles response from 'getSafetyChecklistHistoryResponse' response id from
// websocket. Initialise the list in the payload and sets to the tableItems
// list to be displayed in HTML.
function handleHistoryResponse(responseList: SafetyChecklist[]) {
  const fleetAssetStore = useFleetAssetStore();
  // Initialise items and set to tableItems
  tableItems.value = responseList.map((r) => {
    const driver = useDriverDetailsStore().getDriverFromDriverId(r.driverId);
    const fleet = fleetAssetStore.getFleetAssetFromFleetAssetId(r.fleetAssetId);
    // NOTE: This is a temporary solution until we add the checklist id to the
    // SafetyChecklist object before sending from mobile
    const isCraneChecklist =
      r.questionGroups[0].questions[0].title.includes('crane');
    // NOTE: This is a temporary solution until we can change the titles on
    // the mobile device. This will probably all change when we do the
    // compliance checklist changes anyway, at which time we can remove
    r.questionGroups.forEach((q) => {
      if (isCraneChecklist) {
        if (q.title === 'Rigid Vehicle Checks') {
          q.title = 'Crane Checks';
        }
      } else {
        if (q.title === 'Safe to work') {
          q.title = 'Odometer';
        }
      }
    });
    return {
      ...r,
      driverName: driver?.displayName ?? '-',
      mobile: driver?.mobile ? formatPhoneNumber(driver.mobile) : '-',
      csrAssignedId: fleet?.csrAssignedId ?? '-',
      date: returnFormattedDate(r.timestamp),
      time: returnFormattedTime(r.timestamp),
      checklistType: isCraneChecklist ? 'Crane' : 'Truck',
    };
  });
}

// Set store subscription and send default request on component mount
onMounted(() => {
  sendSearchRequest();
});
</script>

<style scoped lang="scss">
.top-panel {
  position: fixed;
  transition: 0.2s;
  border-bottom: 1px solid $translucent;
  background-color: var(--background-color-400);
  top: 40px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  padding: 0 8px;
}

.safety-checklist-history {
  padding-top: 58px;
}
</style>
