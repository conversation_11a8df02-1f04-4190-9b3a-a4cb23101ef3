<template>
  <div class="sms-message-history px-3">
    <div class="top-panel"></div>

    <v-form ref="smsMessageHistoryForm">
      <v-layout align-center class="banner-custom" px-2>
        <v-layout column>
          <GTitle
            title="SMS Message History"
            subtitle="Search for outgoing SMS messages"
            :divider="false"
          />
        </v-layout>

        <div class="mx-2">
          <v-text-field
            class="v-solo-custom"
            solo
            flat
            color="light-blue"
            label="Recipient Phone #"
            v-model.trim="receiverId"
            prefix="Ph.# "
            hide-details
            clearable
            @blur="sendSearchRequest(1)"
            @keyup.enter="sendSearchRequest(1)"
            @click:clear="
              receiverId = '';
              sendSearchRequest(1);
            "
            v-mask="'0### ### ###'"
          >
            <template v-slot:prepend-inner>
              <span class="pr-2">
                <v-icon size="16">fal fa-search</v-icon>
              </span>
            </template>
          </v-text-field>
        </div>
        <v-flex md6 px-2>
          <v-layout align-center>
            <v-flex ml-1 mr-1>
              <DateTimeInputs
                :epochTime.sync="startRangeEpoch"
                :enableValidation="true"
                :type="DateTimeType.DATE_START_OF_DAY"
                datePrefix="From: "
                :soloInput="true"
                :boxInput="false"
                :hintTextType="HintTextType.FORMATTED_SELECTION"
                :hide-details="true"
                :clearable="true"
                :isRequired="!!endRangeEpoch"
                @dateTimeUpdated="sendSearchRequest(1)"
                @clear="
                  endRangeEpoch = undefined;
                  sendSearchRequest(1);
                "
              ></DateTimeInputs>
            </v-flex>
            <v-flex>
              <DateTimeInputs
                :epochTime.sync="endRangeEpoch"
                :enableValidation="true"
                :type="DateTimeType.DATE_END_OF_DAY"
                datePrefix="To: "
                :soloInput="true"
                :boxInput="false"
                :hintTextType="HintTextType.FORMATTED_SELECTION"
                :hide-details="true"
                :clearable="true"
                :isRequired="!!startRangeEpoch"
                @dateTimeUpdated="sendSearchRequest(1)"
                @clear="
                  startRangeEpoch = undefined;
                  sendSearchRequest(1);
                "
              ></DateTimeInputs>
            </v-flex>
          </v-layout>
        </v-flex>
        <!-- <v-flex md2 class="tags-chip mr-1">
      </v-flex> -->
      </v-layout>
    </v-form>
    <v-divider class="mt-1 mb-1"></v-divider>
    <v-data-table
      class="gd-dark-theme bordered"
      :headers="tableHeaders"
      :items="tableItems"
      hide-actions
      :loading="isAwaitingResponse"
      :pagination.sync="paginationTable"
    >
      <template v-slot:items="props">
        <tr>
          <td class="text-xs-left">
            {{
              returnFormattedDate(props.item.timestamp, 'DD/MM/YYYY HH:mm a')
            }}
          </td>
          <td class="text-xs-left">{{ props.item.receiverId }}</td>
          <td class="text-xs-right">
            {{ props.item.jobId ? props.item.jobId : '-' }}
          </td>
          <td class="text-xs-left">{{ props.item.content }}</td>
        </tr>
      </template>
    </v-data-table>
    <v-layout>
      <Pagination
        @pageIncrement="pageIncrement"
        :pagination="pagination"
        @change="sendSearchRequest"
        :rowsPerPage.sync="rowsPerPage"
        :showTotalCount="false"
        :currentPageCount="tableItems.length"
        :rowsPerPageList="[10]"
    /></v-layout>
  </div>
</template>

<script setup lang="ts">
// Import necessary modules and components
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import Pagination from '@/components/common/pagination/index.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import SMSMessage from '@/interface-models/Generic/SmsMessage/SMSMessage';
import SMSMessageHistoryRequest from '@/interface-models/Generic/SmsMessage/SMSMessageHistoryRequest';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { useUserActivityStore } from '@/store/modules/UserActivityStore';
import moment from 'moment';
import { computed, onMounted, ref, Ref } from 'vue';
// Define component data and properties

const rowsPerPage: number = 10;
const isAwaitingResponse: Ref<boolean> = ref(false);

const page: Ref<number> = ref(1);

const smsMessageHistoryForm = ref<any>(null);

const receiverId: Ref<string> = ref('');

const startRangeEpoch: Ref<number | undefined> = ref(
  returnStartOfDayFromEpoch(moment().subtract(1, 'month').valueOf()),
);
const endRangeEpoch: Ref<number | undefined> = ref(returnEndOfDayFromEpoch());

// Array to store SMS message data
const tableItems: Ref<SMSMessage[]> = ref([]);

const paginationTable: VPagination = {
  descending: true,
  page: 1,
  rowsPerPage: 10,
  sortBy: 'timestamp',
};

// Define table headers for SMS message history
const tableHeaders: TableHeader[] = [
  { text: 'Send At', value: 'timestamp', align: 'left' },
  { text: 'Receiver', value: 'receiverId', align: 'left' },
  {
    text: 'Job',
    value: 'jobId',
    align: 'right',
  },
  { text: 'Content', value: 'content', align: 'left' },
];

// Define pagination settings
const pagination = computed(() => {
  return {
    ...paginationTable,
    page: page.value,
    rowsPerPage: rowsPerPage,
  };
});

// Method to increment or decrement page and trigger search
function pageIncrement(value: number) {
  page.value += value;
  sendSearchRequest();
}

// Method to send search request to fetch SMS message history
async function sendSearchRequest(pageNumber?: number): Promise<void> {
  if (!!pageNumber) {
    page.value = pageNumber;
  }
  if (!smsMessageHistoryForm.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'SMS Message History',
    });
    return;
  }
  if (
    (!startRangeEpoch.value && !!endRangeEpoch.value) ||
    (!!startRangeEpoch.value && !endRangeEpoch.value)
  ) {
    showNotification(
      'Please enter both Start and End dates to search by date range.',
      {
        title: 'SMS Message History',
        type: HealthLevel.INFO,
      },
    );
    return;
  }
  const request: SMSMessageHistoryRequest = {
    page: page.value,
    size: rowsPerPage,
    startEpoch: startRangeEpoch.value,
    endEpoch: endRangeEpoch.value,
    receiverId: receiverId.value,
  };

  isAwaitingResponse.value = true;
  // Send request and handle response
  const result = await useUserActivityStore().searchSmsMessageHistory(request);
  tableItems.value = result ?? [];
  // Display error notification if no results found
  if (tableItems.value.length === 0) {
    showNotification('No results found for the selected search criteria.', {
      title: 'SMS Message History',
      type: HealthLevel.INFO,
    });
  }
  isAwaitingResponse.value = false;
}

// Set store subscription and send default request on component mount
onMounted(() => {
  sendSearchRequest();
});
</script>

<style scoped lang="scss">
.top-panel {
  position: fixed;
  transition: 0.2s;
  border-bottom: 1px solid $translucent;
  background-color: var(--background-color-400);
  top: 40px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  padding: 0 8px;
}

.sms-message-history {
  padding-top: 58px;
}
</style>
