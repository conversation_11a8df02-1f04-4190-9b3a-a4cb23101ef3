<template>
  <div class="national-client-id-maintenance">
    <div class="top-panel px-3">
      <v-layout justify-space-between align-center style="height: 40px">
        <InformationTooltip :bottom="true">
          <v-layout slot="content"
            >A national client identifier allows the grouping of clients
            irrespective of division. When a national client identifier is
            created it can be assigned to a client from the client details page.
          </v-layout>
        </InformationTooltip>
        <GButton
          small
          icon="fal fa-plus"
          :iconRight="true"
          @click="newNationalClientIdDetails"
          >Create New</GButton
        >
      </v-layout>
    </div>

    <v-data-table
      class="gd-dark-theme accounting-data-table"
      :headers="headers"
      :items="nationalClientIdDetailsTableData"
      hide-actions
    >
      <template v-slot:items="props">
        <tr @click="editNationalDetails(props.item)" style="cursor: pointer">
          <td class="pl-4">{{ props.item.name }}</td>
          <td class="text-left">
            <span class="client-count-total">
              {{ getClientCount(props.item._id).total }}
            </span>
          </td>
          <td class="text-left">
            <div
              v-if="getClientCount(props.item._id).total > 0"
              class="client-chips-container"
            >
              <v-chip
                v-if="getClientCount(props.item._id).active > 0"
                x-small
                color="success lighten-3"
                text-color="success darken-2"
                class="client-chip"
              >
                {{ getClientCount(props.item._id).active }} active
              </v-chip>
              <v-chip
                v-if="getClientCount(props.item._id).inactive > 0"
                x-small
                color="grey lighten-3"
                text-color="grey darken-2"
                class="client-chip"
              >
                {{ getClientCount(props.item._id).inactive }} inactive
              </v-chip>
            </div>
          </td>
        </tr>
      </template>
    </v-data-table>

    <!-- National Client Edit & Associated Clients Dialog -->
    <v-dialog
      v-if="nationalClientIdDetails"
      v-model="maintenanceDialogIsOpen"
      content-class="v-dialog-custom"
      width="900px"
      persistent
    >
      <!-- Confirmation Dialog for Duplicate National Client Identifier -->
      <ConfirmationDialog
        v-if="showDuplicateConfirmDialog"
        message="This national client identifier already exists, please confirm that you wish to continue."
        title="Duplicate National Client Identification Found"
        @confirm="onConfirmDuplicateSave"
        @closeDialog="onCancelDuplicateSave"
        :confirmationButtonText="'Confirm'"
        :isCheckboxList="false"
        :dialogIsActive="showDuplicateConfirmDialog"
        :requiresButtonToOpen="false"
      />
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span v-if="nationalClientIdDetails._id"
          >Clients for {{ selectedNationalClientName }}</span
        >
        <span v-else> Create New National Client </span>
        <div
          class="app-theme__center-content--closebutton"
          @click="cancelMaintenance"
        >
          <v-icon
            :disabled="awaitingSave"
            class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <div class="app-theme__center-content--body dialog-content">
        <v-form ref="nationalClientIdForm">
          <v-layout pa-3>
            <v-flex md12>
              <v-layout>
                <v-flex md2>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Name:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8 class="align-center">
                  <v-text-field
                    ref="nameInput"
                    v-model="nationalClientIdDetails.name"
                    solo
                    flat
                    color="light-blue"
                    :rules="[validate.required]"
                    class="form-field-required v-solo-custom 'solo-input-disable-display'"
                    :disabled="!editMode"
                    autofocus
                  />
                </v-flex>
                <v-flex md2>
                  <v-btn
                    v-if="isAuthorised()"
                    icon
                    @click="toggleEditModeOrSave"
                    class="name-btn"
                    :class="!editMode ? 'edit' : 'save'"
                    :loading="isLoadingClients"
                    :disabled="isLoadingClients"
                  >
                    <v-icon v-if="!editMode">edit</v-icon>
                    <v-icon v-else>save</v-icon>
                  </v-btn>
                </v-flex>
              </v-layout>
              <v-layout
                wrap
                v-if="
                  didYouMeanResults.length > 0 && !nationalClientIdDetails._id
                "
              >
                <v-flex md12 class="mb-2">
                  <span class="did-you-mean-text">Did you mean?</span>
                </v-flex>
                <v-flex md12>
                  <v-list dense>
                    <v-list-tile
                      v-for="(name, nameIndex) of didYouMeanResults"
                      :key="name + nameIndex"
                      @click="selectExistingNationalId"
                    >
                      <v-list-tile-content>
                        <v-list-tile-title>{{ name }}</v-list-tile-title>
                      </v-list-tile-content>
                      <v-list-tile-action>
                        <v-icon size="12">fas fa-hand-pointer</v-icon>
                      </v-list-tile-action>
                    </v-list-tile>
                  </v-list>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-form>
        <div
          class="associated-clients-section"
          v-if="nationalClientIdDetails._id"
        >
          <div class="associated-clients-content">
            <!-- Loading State -->
            <div v-if="isLoadingClients" class="text-center py-8">
              <v-progress-circular
                indeterminate
                color="primary"
                size="64"
              ></v-progress-circular>
              <div class="mt-4 text-h6">Loading associated clients...</div>
              <div class="caption text--secondary">
                Fetching client details from database
              </div>
            </div>

            <!-- No Clients Found -->
            <div
              v-if="!isLoadingClients && associatedClients.length === 0"
              class="text-center py-8"
            >
              <v-icon size="64" color="grey lighten-1"
                >mdi-account-group-off</v-icon
              >
              <div class="text-h6 grey--text mt-2">
                No associated clients found
              </div>
              <div class="caption grey--text">
                This national client identifier is not assigned to any clients
                yet.
              </div>
            </div>

            <!-- Clients Table -->
            <div v-if="!isLoadingClients && associatedClients.length > 0">
              <AssociatedClientsTable :clientList="associatedClients" />
            </div>
          </div>
        </div>
        <v-divider />
        <v-layout justify-end mt-4>
          <v-btn
            color="error"
            depressed
            outline
            @click="cancelMaintenance"
            :disabled="
              (editMode || awaitingSave) && !!nationalClientIdDetails._id
            "
          >
            <span>Close</span>
          </v-btn>
        </v-layout>
      </div>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import AssociatedClientsTable from '@/components/common/associated_clients_table.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { NationalClientDetails } from '@/interface-models/Client/NationalClientDetails';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import Fuse from 'fuse.js';
import {
  computed,
  ComputedRef,
  nextTick,
  onMounted,
  Ref,
  ref,
  watch,
} from 'vue';

const componentTitle: string = 'National Client Identifier';
const clientDetailsStore = useClientDetailsStore();

const maintenanceDialogIsOpen: Ref<boolean> = ref(false);
const awaitingSave: Ref<boolean> = ref(false);
const selectedNationalClientName: Ref<string> = ref('');
const nationalClientIdDetails: Ref<NationalClientDetails | null> = ref(null);
const nationalClientIdDetailsTableData: Ref<NationalClientDetails[]> = ref([]);
const validate = validationRules;

// for form validation
const nationalClientIdForm = ref<any>(null);
// to focus on name input
const nameInput = ref<any>(null);

const editMode: Ref<boolean> = ref(false);
const associatedClients: Ref<ClientSearchSummary[]> = ref([]);
const isLoadingClients: Ref<boolean> = ref(false);
const showDuplicateConfirmDialog: Ref<boolean> = ref(false);
const originalName: Ref<string> = ref('');

const headers: TableHeader[] = [
  {
    text: 'National Client Identifier',
    align: 'left',
    value: 'name',
    sortable: false,
  },
  {
    text: 'Total Clients',
    align: 'left',
    value: 'totalClients',
    sortable: false,
  },
  {
    text: 'Status',
    align: 'left',
    value: 'clientStatus',
    sortable: false,
  },
];

function newNationalClientIdDetails() {
  nationalClientIdDetails.value = {
    _id: undefined,
    name: '',
    company: sessionManager.getCompanyId(),
  };
  associatedClients.value = [];
  showDuplicateConfirmDialog.value = false;
  maintenanceDialogIsOpen.value = true;
  editMode.value = true;
  nextTick(() => {
    // Focus the input field when creating new national client
    if (nameInput.value) {
      nameInput.value.focus();
    }
  });
}

function cancelMaintenance() {
  nationalClientIdDetails.value = null;
  maintenanceDialogIsOpen.value = false;
  selectedNationalClientName.value = '';
  editMode.value = false;
  originalName.value = ''; // Reset original name
}

function getClientsForNationalId(nationalClientId: string | undefined) {
  if (!nationalClientId) {
    return [];
  }
  return clientDetailsStore.clientSummaryList.filter(
    (client) => client.nationalClientId === nationalClientId,
  );
}

function editNationalDetails(nationalClientIdDetail: NationalClientDetails) {
  nationalClientIdDetails.value = nationalClientIdDetail;
  originalName.value = nationalClientIdDetail.name; // Store original name
  showDuplicateConfirmDialog.value = false;
  maintenanceDialogIsOpen.value = true;
  associatedClients.value = [];
  isLoadingClients.value = true;
  selectedNationalClientName.value = nationalClientIdDetail.name;
  const summaries = getClientsForNationalId(nationalClientIdDetail._id);
  associatedClients.value = summaries;
  isLoadingClients.value = false;
}

const enteredName: ComputedRef<string | null> = computed(() => {
  return nationalClientIdDetails.value
    ? nationalClientIdDetails.value.name
    : null;
});

const isDuplicate: ComputedRef<boolean> = computed(() => {
  if (!nationalClientIdDetails.value) {
    return false;
  }
  const nationalClientId = nationalClientIdDetails.value.name;
  const mongoId = nationalClientIdDetails.value._id;
  return nationalClientIdDetailsTableData.value.some(
    (x) => x.name === nationalClientId && x._id !== mongoId,
  );
});

const didYouMeanResults: ComputedRef<string[]> = computed(() => {
  if (!enteredName.value) {
    return [];
  }
  const fuse = new Fuse(nationalClientIdDetailsTableData.value, {
    keys: [{ name: 'name', weight: 2 }],
    minMatchCharLength: 2,
    threshold: 0.5,
  });
  return fuse.search(enteredName.value).map((x) => x.item.name);
});

function selectExistingNationalId(): void {
  maintenanceDialogIsOpen.value = false;
  nationalClientIdDetails.value = null;
  showAppNotification(
    'National Client Identifier Already Exists.',
    HealthLevel.INFO,
  );
}

/**
 * Request national clientDetailsList and set to local variable on response
 */
async function requestNationalClientDetailsList() {
  const ncdList = await clientDetailsStore.requestNationalClientDetailsList();
  nationalClientIdDetailsTableData.value = ncdList ?? [];
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

/**
 * Get client count information for a national client ID
 * @param nationalClientId - The national client ID to get counts for
 * @returns Object containing total, active, and inactive client counts
 */
function getClientCount(nationalClientId: string | undefined) {
  const clients = getClientsForNationalId(nationalClientId);
  const active = clients.filter(
    (client) => !client.statusList.includes(13),
  ).length; // 13 = RETIRED
  const inactive = clients.length - active;
  return {
    total: clients.length,
    active,
    inactive,
  };
}

/**
 * Toggle between edit mode and save mode, or handle duplicate detection
 */
function toggleEditModeOrSave() {
  if (!editMode.value) {
    editMode.value = true;
    nextTick(() => {
      // Focus the input field when entering edit mode
      if (nameInput.value) {
        nameInput.value.focus();
      }
    });
  } else {
    // Check for duplicates before saving
    if (isDuplicate.value) {
      showDuplicateConfirmDialog.value = true;
      return;
    }
    // Save logic if no duplicate
    saveNationalClientName();
  }
}

/**
 * Handle confirmation of duplicate save - proceed with saving the duplicate name
 */
function onConfirmDuplicateSave() {
  showDuplicateConfirmDialog.value = false;
  saveNationalClientName();
}

/**
 * Handle cancellation of duplicate save - revert to original name and exit edit mode
 */
function onCancelDuplicateSave() {
  showDuplicateConfirmDialog.value = false;
  // Revert the name back to the original value
  if (nationalClientIdDetails.value) {
    if (nationalClientIdDetails.value._id) {
      // For editing existing items, revert to the original name
      nationalClientIdDetails.value.name = originalName.value;
    } else {
      // For new items, clear the name
      nationalClientIdDetails.value.name = '';
    }
  }
  // Exit edit mode
  editMode.value = false;
}

/**
 * Save the national client name to the database
 */
async function saveNationalClientName() {
  if (!nationalClientIdDetails.value) {
    return;
  }
  if (!nationalClientIdDetails.value.name.trim()) {
    showAppNotification('Name cannot be empty');
    return;
  }
  isLoadingClients.value = true;
  try {
    // Save the updated name
    const updated = {
      ...nationalClientIdDetails.value,
      name: nationalClientIdDetails.value.name.trim(),
    };
    const result = await clientDetailsStore.saveNationalClientDetails(updated);
    if (result) {
      // Update the table data
      const idx = nationalClientIdDetailsTableData.value.findIndex(
        (x) => x._id === result._id,
      );
      if (idx >= 0) {
        nationalClientIdDetailsTableData.value[idx] = result;
      }
      nationalClientIdDetails.value = result;
      showAppNotification('National Client Name updated', HealthLevel.INFO);
      editMode.value = false;
    } else {
      showAppNotification('Failed to update name', HealthLevel.ERROR);
    }
  } catch (error) {
    console.error('Error saving national client name:', error);
    showAppNotification('Failed to update name', HealthLevel.ERROR);
  } finally {
    isLoadingClients.value = false;
  }
}

// Watch for dialog close to reset edit mode
watch(maintenanceDialogIsOpen, (newValue) => {
  if (!newValue) {
    editMode.value = false;
  }
});

onMounted(async () => {
  await requestNationalClientDetailsList();
  if (clientDetailsStore.clientSummaryList.length === 0) {
    await clientDetailsStore.requestClientSummaryList();
  }
});
</script>

<style scoped lang="scss">
.top-panel {
  position: fixed;
  transition: 0.2s;
  border-bottom: 1px solid $translucent;
  background-color: var(--background-color-400);
  top: 40px;
  height: 49px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  padding: 0 8px;
  display: flex;
  align-items: center;
}

.national-client-id-maintenance {
  padding-top: 67px;
}

.did-you-mean-text {
  font-weight: 600;
  font-size: $font-size-15;
}

.client-count-active {
  color: #43a047;
}

.client-count-inactive {
  color: #757575;
}

.client-count-total {
  color: inherit;
}

.client-chips-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.client-chip {
  margin: 0;
}

.name-btn {
  border-radius: 50px;
  margin-left: 12px;
  &.edit {
    background-color: $primary;
  }
  &.save {
    background-color: $success;
  }
}

.associated-clients-title {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 12px;
}

.associated-clients-content {
  margin-top: 12px;
}

.associated-clients-section {
  background: var(--background-color-300);
  padding: 12px 8px 12px 8px;
  margin-bottom: 0;
}
</style>
