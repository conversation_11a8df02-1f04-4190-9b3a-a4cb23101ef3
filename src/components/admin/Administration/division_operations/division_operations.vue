<template>
  <div class="division-details-key-information px-3">
    <div class="top-panel">
      <v-layout>
        <v-btn
          small
          color="error"
          outline
          @click="setEdited(false)"
          v-if="isEdited"
          depressed
        >
          Cancel
        </v-btn>
        <v-btn
          small
          outline
          depressed
          v-if="!isEdited"
          @click="setEdited(true)"
          :disabled="!isAuthorised()"
        >
          Edit
        </v-btn>
        <v-spacer></v-spacer>
        <v-btn
          small
          color="blue"
          depressed
          :disabled="!isEdited"
          :loading="awaitingSaveResponse"
          @click="saveDivisionOperations"
        >
          Save
        </v-btn>
      </v-layout>
    </div>

    <form-card class="mt-6">
      <div slot="heading">Division Operations</div>
      <div slot="subheading"></div>
      <div slot="top-right">
        <v-layout> </v-layout>
      </div>
      <div slot="content">
        <v-layout wrap>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    Default Pickup Load Duration:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  label="Default Pickup Load Duration"
                  v-model.number="defaultPickupLoadDuration"
                  :disabled="!isEdited"
                  class="v-solo-custom"
                  min="0"
                  :rules="[validate.nonNegative]"
                  number
                  type="number"
                  solo
                  suffix="MINUTES"
                  hint="When this is blank the division default pickup load duration will be utilised."
                  persistent-hint
                  flat
                />
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    Default Dropoff Load Duration:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  label="Default Dropoff Load Duration"
                  m
                  v-model.number="defaultDropoffLoadDuration"
                  :disabled="!isEdited"
                  class="v-solo-custom"
                  :rules="[validate.nonNegative]"
                  solo
                  min="0"
                  type="number"
                  suffix="MINUTES"
                  hint="When this is blank the division default dropoff load duration will be utilised."
                  persistent-hint
                  flat
                  color="light-blue"
                />
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </form-card>
  </div>
</template>

<script setup lang="ts">
import FormCard from '@/components/common/ui-elements/form_card.vue';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { DivisionOperationDetails } from '@/interface-models/Company/DivisionCustomConfig/Operations/DivisionOperationDetails';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import Mitt from '@/utils/mitt';
import { computed, onBeforeMount, onBeforeUnmount, ref, Ref } from 'vue';

const companyDetailsStore = useCompanyDetailsStore();
const isEdited: Ref<boolean> = ref(false);

const awaitingSaveResponse: Ref<boolean> = ref(false);
const operations: Ref<DivisionOperationDetails | null> = ref(null);

const validate = validationRules;

function setEdited(value: boolean) {
  isEdited.value = value;
  if (!value) {
    setDivisionOperations();
  }
}

const defaultPickupLoadDuration = computed({
  get: () => {
    if (!operations.value || operations.value.pickupLoadDuration === null) {
      return null;
    }
    return operations.value.pickupLoadDuration / 60000;
  },
  set: (loadDuration: number | null) => {
    if (!operations.value) {
      return;
    }
    if (loadDuration === null) {
      operations.value.pickupLoadDuration = null;
      return;
    }
    operations.value.pickupLoadDuration = loadDuration * 60000;
  },
});

const defaultDropoffLoadDuration = computed({
  get: () => {
    if (!operations.value || operations.value.dropoffLoadDuration === null) {
      return null;
    }
    return operations.value.dropoffLoadDuration / 60000; // Convert from milliseconds to minutes
  },
  set: (loadDuration: number | null) => {
    if (!operations.value) {
      return;
    }
    if (loadDuration === null) {
      operations.value.dropoffLoadDuration = null; // Reset to null if loadDuration is null
      return;
    }
    operations.value.dropoffLoadDuration = loadDuration * 60000; // Convert from minutes to milliseconds
  },
});

/**
 * Dispatch request to save the current DivisionOperationDetails object
 */
async function saveDivisionOperations() {
  if (operations.value) {
    awaitingSaveResponse.value = true;
    const result = await companyDetailsStore.saveDivisionOperationDetails(
      operations.value,
    );
    handleResponse(result);
    awaitingSaveResponse.value = false;
  }
}

/**
 * Handles the response of division operation settings.
 *
 * This method processes the response from a saved division operation
 * settings, either from the current user or from another user (through mitt
 * listener). It shows notifications based on the outcome: success, failure,
 * or if the settings were externally modified.
 *
 * @param {DivisionOperationDetails | null} divisionOperationDetails - The
 * details of the division operation settings, or null if the save operation
 * failed.
 */
function handleResponse(
  divisionOperationDetails: DivisionOperationDetails | null,
) {
  // Check if the response is awaited after a save attempt.
  if (awaitingSaveResponse.value) {
    // Handle the case where the save operation failed.
    if (!divisionOperationDetails) {
      showNotification(GENERIC_ERROR_MESSAGE, {
        title: 'Division Operational Settings',
      });
    } else {
      // Handle successful save operation.
      showNotification('Saved successfully.', {
        title: 'Division Operational Settings',
        type: HealthLevel.INFO,
      });
    }
  } else if (isEdited.value) {
    // Handle the case where settings were edited by another user.
    showNotification(
      `These settings have been updated by another user. Your changes have been discarded.`,
      {
        title: 'Division Operational Settings',
        type: HealthLevel.WARNING,
      },
    );
  }
  // Update operations with the latest details.
  if (divisionOperationDetails) {
    operations.value = divisionOperationDetails;
  }

  // Reset the edited flag regardless of the outcome.
  isEdited.value = false;
}

/**
 * Sets the working variable from divisionDetails in the store. Called on
 * component mount.
 */
function setDivisionOperations() {
  if (!companyDetailsStore.divisionDetails) {
    return;
  } else if (
    !companyDetailsStore.divisionDetails.customConfig ||
    !companyDetailsStore.divisionDetails.customConfig.operations
  ) {
    operations.value = new DivisionOperationDetails();
  } else {
    operations.value =
      companyDetailsStore.divisionDetails.customConfig.operations;
  }
}

// mitt on divisionOperationDetails for before component is mounted
onBeforeMount(() => {
  setDivisionOperations();
  Mitt.on('divisionOperationDetails', handleResponse);
});

// mitt off divisionOperationDetails before component is unmounted
onBeforeUnmount(() => {
  Mitt.off('divisionOperationDetails', handleResponse);
});

// check if logged in user has admin or head office role
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}
</script>
