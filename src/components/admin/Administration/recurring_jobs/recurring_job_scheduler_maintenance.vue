<template>
  <div class="recurring-job-scheduler-maintenance px-3">
    <div class="top-panel"></div>
    <v-layout align-center class="banner-custom" px-2>
      <v-layout column>
        <h2>Planned Runs</h2>
        <h4>{{ runHistoryList.length }} Results</h4>
      </v-layout>
      <span class="mr-1">
        <DatePickerBasic
          @setEpoch="setStartRange"
          :labelName="'Start Date'"
          :epochTime="startRangeEpoch"
          :hide-details="true"
          :box-input="false"
          :soloInput="true"
        >
        </DatePickerBasic>
      </span>
      <span>
        <DatePickerBasic
          @setEpoch="setEndRange"
          :labelName="'End Date'"
          :epochTime="endRangeEpoch"
          :hide-details="true"
          :box-input="false"
          :soloInput="true"
        >
        </DatePickerBasic>
      </span>
    </v-layout>
    <v-divider class="mt-1 mb-1"></v-divider>
    <v-data-table
      class="accounting-data-table"
      :headers="tableHeaders"
      :items="runHistoryList"
      hide-actions
    >
      <template v-slot:items="props">
        <tr>
          <td>{{ props.item.weekRunId }}</td>
          <td>
            {{
              returnFormattedDate(
                props.item.readyForAllocationDate,
                `dddd
          DD/MM/YY`,
              )
            }}
          </td>
          <td>
            <span v-if="props.item.noOfDaysRun === 1">
              {{
                props.item.plannedRunDate
                  ? returnJobBookingDate(props.item.plannedRunDate)
                  : '-'
              }}
            </span>
            <span v-else>
              <v-layout column py-2>
                <span v-for="day in props.item.noOfDaysRun" :key="day">
                  {{
                    props.item.plannedRunDate
                      ? returnJobBookingDate(props.item.plannedRunDate, day)
                      : '-'
                  }}
                </span>
              </v-layout>
            </span>
          </td>
          <td>
            {{
              props.item.completedTime
                ? returnFormattedDate(
                    props.item.completedTime,
                    'DD/MM/YY HH:mm',
                  )
                : '-'
            }}
          </td>

          <td>{{ props.item.runBy ? props.item.runBy : '-' }}</td>
          <td class="text-xs-right">
            <span v-if="props.item.status !== 'COMPLETED'"> - </span>
            <span v-else-if="props.item.createdJobIdsList.length > 0">
              {{ props.item.createdJobIdsList.length }} Job(s)
            </span>
            <span v-else> No Jobs </span>
          </td>
          <td v-if="props.item.status === 'COMPLETED'"></td>
          <td v-else class="text-xs-right">
            <v-menu left>
              <template v-slot:activator="{ on }">
                <v-icon size="16" class="action-icon" v-on="on"
                  >fas fa-ellipsis-v
                </v-icon>
              </template>
              <v-list class="v-list-custom" dense>
                <v-list-tile
                  @click="prepareForTrigger(props.item)"
                  :disabled="!isAuthorised()"
                >
                  <v-list-tile-title class="pr-2"
                    >Commence Run</v-list-tile-title
                  >
                </v-list-tile>
              </v-list>
            </v-menu>
          </td>
        </tr>
      </template>
    </v-data-table>
    <v-dialog
      v-model="showTriggerConfirmationDialog"
      width="500px"
      content-class="v-dialog-custom"
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Commence Permanent Job creation run?</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="cancelConfirmationOfRunTrigger()"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>

      <v-layout class="app-theme__center-content--body">
        <v-flex md12>
          <v-layout
            row
            wrap
            class="side-column"
            v-if="currentPendingJobRun"
            pa-3
          >
            <v-flex md12 py-1 px-3>
              <v-alert type="error" class="ma-0" :value="true">
                <span>Please note the following:</span>
                <ul>
                  <li>This action is irreversible</li>
                  <li>
                    This action may only be executed ONCE. You cannot re-run
                    this action once it has been executed.
                  </li>
                  <li>
                    Only Permanent Jobs CURRENTLY scheduled for this day will be
                    created. After executing this action, any future Permanent
                    Jobs scheduled for this day will be invalid.
                  </li>
                </ul>
              </v-alert>
            </v-flex>
            <v-flex md12 px-2>
              <v-layout pt-2>
                You are about to manually trigger the Permanent Job creation
                scheduled for
                {{
                  returnFormattedDate(
                    currentPendingJobRun.readyForAllocationDate,
                  )
                }}.
              </v-layout>
              <v-layout py-2>
                This will create Jobs that have been scheduled for the following
                date(s):
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout
                class="tags-chip"
                v-for="day in currentPendingJobRun.noOfDaysRun"
                :key="day"
                justify-center
              >
                <span>
                  {{
                    currentPendingJobRun.plannedRunDate
                      ? returnJobBookingDate(
                          currentPendingJobRun.plannedRunDate,
                          day,
                        )
                      : '-'
                  }}
                </span>
              </v-layout>
            </v-flex>
          </v-layout>
          <v-divider class="mb-1 mt-3"></v-divider>
          <v-layout justify-end>
            <v-btn flat color="red" @click="cancelConfirmationOfRunTrigger()"
              >Cancel</v-btn
            >
            <v-spacer></v-spacer>
            <v-btn
              depressed
              color="blue"
              :loading="isAwaitingManualTriggerResponse"
              @click="triggeredRecurringJobRun()"
              class="v-btn-confirm-custom"
              >Save</v-btn
            >
          </v-layout>
        </v-flex>
      </v-layout>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { StartAndEndDate } from '@/interface-models/Generic/StartAndEndDate';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { RecurringJobRunDetails } from '@/interface-models/Jobs/RecurringJob/RecurringJobRunDetails';
import { RecurringJobRunStatus } from '@/interface-models/Jobs/RecurringJob/RecurringJobRunStatus';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import { useMittListener } from '@/utils/useMittListener';
import moment from 'moment-timezone';
import { Ref, onBeforeMount, ref } from 'vue';

const recurringJobStore = useRecurringJobStore();

const runHistoryList: Ref<RecurringJobRunDetails[]> = ref([]);
const showTriggerConfirmationDialog: Ref<boolean> = ref(false);
const currentPendingJobRun: Ref<RecurringJobRunDetails | null> = ref(null);
const isAwaitingManualTriggerResponse: Ref<boolean> = ref(false);

const startRangeEpoch: Ref<number> = ref(
  returnStartOfDayFromEpoch(moment().subtract(2, 'weeks').valueOf()),
);
const endRangeEpoch: Ref<number> = ref(
  returnEndOfDayFromEpoch(moment().add(3, 'weeks').valueOf()),
);

const tableHeaders: TableHeader[] = [
  { text: 'Run ID', value: 'schedulerRunId', align: 'left' },
  { text: 'Day to Allocate', value: 'readyForAllocationDate', align: 'left' },
  {
    text: 'Job Booking Date(s)',
    value: 'readyForAllocationDate',
    align: 'left',
  },
  {
    text: 'Scheduler Run Time',
    value: 'completedTime',
    align: 'left',
    sortable: false,
  },
  { text: 'Run By', value: 'runBy', align: 'left', sortable: false },
  { text: 'Jobs', value: '', align: 'right', sortable: false },
  { text: 'Action', value: 'complete', align: 'right', sortable: false },
];

function returnJobBookingDate(plannedRunDate: number, multiple: number = 1) {
  const milliseconds = 86400000 * multiple;
  return returnFormattedDate(plannedRunDate + milliseconds, 'dddd DD/MM/YY');
}

// Set currently editing jobRun to selected and show dialog
function prepareForTrigger(jobRun: RecurringJobRunDetails) {
  const nextAvailableRunId = returnNextAvailablePlannedRunId();
  if (nextAvailableRunId === jobRun._id) {
    showTriggerConfirmationDialog.value = true;
    currentPendingJobRun.value = jobRun;
  } else {
    showNotification(
      'All previous Scheduler Runs must be completed before future runs.',
    );
  }
  return;
}

// Cancel dialog and reset
function cancelConfirmationOfRunTrigger() {
  showTriggerConfirmationDialog.value = false;
  currentPendingJobRun.value = null;
}

// Send request to trigger scheduler run for plannedRunDate
async function triggeredRecurringJobRun() {
  if (currentPendingJobRun.value === null) {
    return;
  }
  isAwaitingManualTriggerResponse.value = true;
  const runDetails = await recurringJobStore.triggerRecurringJobScheduler(
    currentPendingJobRun.value.plannedRunDate,
  );
  isAwaitingManualTriggerResponse.value = false;
  if (runDetails !== null) {
    if (runDetails.status === RecurringJobRunStatus.IN_PROGRESS) {
      showNotification(
        'A Permanent Job run is already in progress. Please wait a few minutes and try again.',
        { title: 'Permanent Job Run Already Started' },
      );
      return;
    }
  } else {
    showNotification(GENERIC_ERROR_MESSAGE);
  }
  cancelConfirmationOfRunTrigger();
}

// Dispatch request for RecurringJobRunDetails list using current selected date parameters
async function requestRecurringJobRunDetails() {
  if (startRangeEpoch.value > endRangeEpoch.value) {
    showNotification('Start date cannot be after end date');
    return;
  }
  const request: StartAndEndDate = {
    startDate: startRangeEpoch.value,
    endDate: endRangeEpoch.value,
  };

  // Request run history and handle response
  const response = await recurringJobStore.getRecurringJobRunDetails(request);
  runHistoryList.value = response ?? [];
}

/**
 * Handles emits from DatePickerBasic. Validates if the value is different from
 * the current startRangeEpoch value and sets the startRangeEpoch value to the new
 * value.
 * @param value - The new value to set the startRangeEpoch to
 */
function setStartRange(value: number) {
  if (value === startRangeEpoch.value) {
    return;
  }
  startRangeEpoch.value = value;
  requestRecurringJobRunDetails();
}

/**
 * Handles emits from DatePickerBasic. Validates if the value is different from
 * the current endRangeEpoch value and sets the endRangeEpoch value to the new
 * value.
 * @param value - The new value to set the endRangeEpoch to
 */
function setEndRange(value: number) {
  value = returnEndOfDayFromEpoch(value);
  if (value === endRangeEpoch.value) {
    return;
  }
  endRangeEpoch.value = value;
  requestRecurringJobRunDetails();
}

/**
 * Returns the ID of the next available RecurringJobRunDetails object
 */
function returnNextAvailablePlannedRunId(): string {
  const runList = runHistoryList.value;
  // Sort the runHistoryList list in ascending chronological order
  // and find the first where the complete is false
  const firstAvailableRun: RecurringJobRunDetails | undefined = runList
    .sort((a, b) => a.plannedRunDate - b.plannedRunDate)
    .find(
      (r) =>
        r.status === RecurringJobRunStatus.NOT_ACTIONED ||
        r.status === RecurringJobRunStatus.IN_PROGRESS,
    );
  return firstAvailableRun ? firstAvailableRun._id : '';
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}

/**
 * Mitt listener for completedManuallyTriggeredScheduler event. Handles the
 * response from the event and updates the runHistoryList with the new
 * RecurringJobRunDetails
 * @param payload - The RecurringJobRunDetails object to update the list with
 * or null if the request failed
 */
function handleTriggerResponse(payload: RecurringJobRunDetails | null) {
  if (payload === null) {
    return;
  }
  const index = runHistoryList.value.findIndex((r) => r._id === payload._id);
  if (index !== -1) {
    runHistoryList.value.splice(index, 1, payload);
  }
  // Close the dialog in case another user was about to run this one
  if (showTriggerConfirmationDialog.value) {
    cancelConfirmationOfRunTrigger();
  }
}

onBeforeMount(async () => {
  requestRecurringJobRunDetails();
});

useMittListener('completedManuallyTriggeredScheduler', handleTriggerResponse);
</script>
<style scoped lang="scss">
.top-panel {
  position: fixed;
  transition: 0.2s;
  border-bottom: 1px solid $translucent;
  background-color: var(--background-color-400);
  top: 40px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  padding: 0 8px;
}

.recurring-job-scheduler-maintenance {
  padding-top: 58px;
}

.side-column {
  .side-column__label {
    font-size: $font-size-12;
    text-transform: uppercase;
    font-weight: 600;
    color: rgb(186, 188, 209) !important;
    padding-right: 12px;
    padding-top: 3px;
    letter-spacing: 0.02em;
  }

  .side-column__value {
    font-size: $font-size-12;
    font-weight: 400;
    padding-top: 3px;
  }
}

.action-icon {
  color: var(--text-color) !important;
}
</style>
