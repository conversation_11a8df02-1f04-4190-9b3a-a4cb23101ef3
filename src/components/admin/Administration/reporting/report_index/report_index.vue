<template>
  <v-layout wrap class="reports-container px-3">
    <div class="top-panel">
      <v-spacer></v-spacer>
    </div>
    <v-flex
      md12
      class="app-bgcolor--400 app-borderside--a-2x app-bordercolor--600 mb-3"
    >
      <div class="pa-2 header-text">Report Selection</div>
      <v-divider class="ma-0 pa-0" />
      <v-form class="pa-3">
        <v-select
          v-model="selectedReportCategory"
          :items="
            Object.keys(ReportCategory).map((e) =>
              ReportCategory[e].replaceAll('_', ' '),
            )
          "
          label="Report Category"
          hint="Please select a report category"
          persistent-hint
          color="primary"
          solo
          flat
          class="v-solo-custom mb-2"
          @change="setReportCategory"
        >
        </v-select>
        <v-autocomplete
          v-model="selectedReport"
          :items="filteredReports"
          item-value="name"
          item-text="name"
          color="primary"
          solo
          flat
          hint="Please select a report"
          label="Please select a report"
          persistent-hint
          :rules="[]"
          class="form-field-required v-solo-custom mb-2"
          browser-autocomplete="off"
          auto-select-first
        >
        </v-autocomplete>
      </v-form>
    </v-flex>

    <v-flex md12 v-if="reportDetails" class="report-info">
      <ReportInformation>
        <div slot="name" class="">{{ reportDetails.name }}</div>

        <div slot="description">
          <span v-if="reportDetails.description">{{
            reportDetails.description
          }}</span>
          <span v-else>Description unavailable.</span>
        </div>

        <div slot="audience">
          <span v-if="reportDetails.audience">
            {{ reportDetails.audience }}</span
          >
          <span v-else>Audience unavailable.</span>
        </div>

        <div slot="query">
          <ul>
            <li
              v-for="(query, index) of getReportQuery(reportDetails.query)"
              :key="index"
            >
              {{ query }}
            </li>
          </ul>
        </div>

        <div slot="filename">{{ getFileName(reportDetails.fileName) }}</div>

        <div slot="permissions">
          {{ getRoleNames(reportDetails.permissions) }}
        </div>

        <div slot="content">
          <EmailDailyJobsReport
            v-if="selectedReport === 'Email Daily Jobs Report'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <DailyJobsReport
            v-if="selectedReport === 'Daily Jobs Report'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <StandbyInvoiceReport
            v-if="selectedReport === 'Standby Invoice'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <YardDelayReport
            v-if="selectedReport === 'Yard Delay'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <KpiCostPerTonne
            v-if="selectedReport === 'KPI - Cost Per Tonne'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <ClientKpiDailyReport
            v-if="selectedReport === 'KPI - Daily'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <ClientKPITimeRateReport
            v-if="selectedReport === 'KPI - Time Rate'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <MarginReport
            v-if="selectedReport === 'Margin Report'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <BranchManagerKpiReport
            v-if="selectedReport === 'Branch Manager KPI'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <SalesByClientReport
            v-if="selectedReport === 'Sales By Client'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <RankSalesByRevenue
            v-if="selectedReport === 'Rank Sales By Revenue'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <RankSalesByRevenueBySalesPerson
            v-if="selectedReport === 'Rank Sales By Revenue By Sales Person'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <DriverPayReport
            v-if="selectedReport === 'Driver Pay'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <SalesBySalesPersonReport
            v-if="selectedReport === 'Sales By Sales Person'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <DeliveryPerformanceReport
            v-if="selectedReport === 'Delivery Performance'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <ServiceTotalsReport
            v-if="selectedReport === 'Service Totals'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <ServiceFailuresReport
            v-if="selectedReport === 'Service Failures'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <OperatorPerformanceReport
            v-if="selectedReport === 'Operator Performance'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <RemittanceSummaryReport
            v-if="selectedReport === 'Remittance Summary'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <InvoiceSummaryReport
            v-if="selectedReport === 'Invoice Summary'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <InvoiceFinancesSummary
            v-if="selectedReport === 'Invoice Finance Summary'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <OperationalKpiReport
            v-if="selectedReport === 'Operational KPI'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <SubcontractorBankDetails
            v-if="selectedReport === 'Subcontractor Bank Details'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <SubcontractorComplianceReport
            v-if="selectedReport === 'Subcontractor Compliance'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <FleetAssetComplianceReport
            v-if="selectedReport === 'Fleet Asset Compliance'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <DriverComplianceReport
            v-if="selectedReport === 'Driver Compliance'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <ServiceRatesReport
            v-if="selectedReport === 'Service Rates'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <ClientDetailsReport
            v-if="selectedReport === 'Client Details Summary'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <ClientContactsReport
            v-if="selectedReport === 'Client Contacts'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <SubcontractorContactDetailsReport
            v-if="selectedReport === 'Subcontractor Details'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />
          <DriverContactDetailsReport
            v-if="selectedReport === 'Driver Contact Details'"
            :isLoading.sync="isLoading"
            @generateReport="generateReport"
          />

          <JobDetailsReport
            v-if="selectedReport === 'Job Details'"
            :isLoading.sync="isLoading"
            :isDialog="false"
            @generateReport="generateReport"
          />
        </div>

        <div slot="columns">
          <ReportColumnsTable :columns="reportDetails.columns" />
        </div>
      </ReportInformation>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import ReportColumnsTable from '@/components/admin/Administration/reporting/components/report_columns_table/report_columns_table.vue';
import ReportInformation from '@/components/admin/Administration/reporting/components/report_information/report_information.vue';
import BranchManagerKpiReport from '@/components/admin/Administration/reporting/reports/branch_manager_kpi_report.vue';
import ClientContactsReport from '@/components/admin/Administration/reporting/reports/client_contacts_report.vue';
import ClientDetailsReport from '@/components/admin/Administration/reporting/reports/client_details_summary_report.vue';
import ClientKpiDailyReport from '@/components/admin/Administration/reporting/reports/client_kpi_daily_report.vue';
import ClientKPITimeRateReport from '@/components/admin/Administration/reporting/reports/client_kpi_time_rate_report.vue';
import DailyJobsReport from '@/components/admin/Administration/reporting/reports/daily_jobs_report.vue';
import DeliveryPerformanceReport from '@/components/admin/Administration/reporting/reports/delivery_performance_report.vue';
import DriverComplianceReport from '@/components/admin/Administration/reporting/reports/driver_compliance.vue';
import DriverContactDetailsReport from '@/components/admin/Administration/reporting/reports/driver_contact_details_report.vue';
import DriverPayReport from '@/components/admin/Administration/reporting/reports/driver_pay_report.vue';
import EmailDailyJobsReport from '@/components/admin/Administration/reporting/reports/email_daily_jobs_report.vue';
import FleetAssetComplianceReport from '@/components/admin/Administration/reporting/reports/fleet_asset_compliance.vue';
import InvoiceFinancesSummary from '@/components/admin/Administration/reporting/reports/invoice_finances_summary.vue';
import InvoiceSummaryReport from '@/components/admin/Administration/reporting/reports/invoice_summary_report.vue';
import JobDetailsReport from '@/components/admin/Administration/reporting/reports/job_details_report.vue';
import KpiCostPerTonne from '@/components/admin/Administration/reporting/reports/kpi_cost_per_tonne.vue';
import MarginReport from '@/components/admin/Administration/reporting/reports/margin_report.vue';
import OperationalKpiReport from '@/components/admin/Administration/reporting/reports/operational_kpi_report.vue';
import OperatorPerformanceReport from '@/components/admin/Administration/reporting/reports/operator_performance_report.vue';
import RankSalesByRevenue from '@/components/admin/Administration/reporting/reports/rank_sales_by_revenue.vue';
import RankSalesByRevenueBySalesPerson from '@/components/admin/Administration/reporting/reports/rank_sales_by_revenue_by_sales_person.vue';
import RemittanceSummaryReport from '@/components/admin/Administration/reporting/reports/remittance_summary_report.vue';
import SalesByClientReport from '@/components/admin/Administration/reporting/reports/sales_by_client_report.vue';
import SalesBySalesPersonReport from '@/components/admin/Administration/reporting/reports/sales_by_sales_person.vue';
import ServiceFailuresReport from '@/components/admin/Administration/reporting/reports/service_failures_report.vue';
import ServiceRatesReport from '@/components/admin/Administration/reporting/reports/service_rates_report.vue';
import ServiceTotalsReport from '@/components/admin/Administration/reporting/reports/service_totals.vue';
import StandbyInvoiceReport from '@/components/admin/Administration/reporting/reports/standby_invoice_report.vue';
import SubcontractorBankDetails from '@/components/admin/Administration/reporting/reports/subcontractor_bank_details.vue';
import SubcontractorComplianceReport from '@/components/admin/Administration/reporting/reports/subcontractor_compliance.vue';
import SubcontractorContactDetailsReport from '@/components/admin/Administration/reporting/reports/subcontractor_contact_details_report.vue';
import YardDelayReport from '@/components/admin/Administration/reporting/reports/yard_delay_report.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import {
  ReportCategory,
  ReportDetails,
  reports,
} from '@/interface-models/Reporting/ReportType';
import { UserRoleType } from '@/interface-models/Roles/UserRoles';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import moment from 'moment-timezone';
import { computed, onBeforeUnmount, Ref, ref } from 'vue';

const isLoading: Ref<boolean> = ref(false);
const selectedReportCategory: Ref<string> = ref(
  ReportCategory.CLIENT_REPORTING.replace('_', ' '),
);
const selectedReport: Ref<string | null> = ref(null);

const filteredReports = computed((): ReportDetails[] => {
  return reports.filter(
    (x: ReportDetails) =>
      x.category === selectedReportCategory.value.replaceAll(' ', '_') &&
      x.permissions.some((role) => sessionManager.getRoles().includes(role)),
  );
});

// returns the role names from the enum list of roles with ROLE removed from the UserRoleType value.
function getRoleNames(roles: UserRoleType[]) {
  return roles
    .map((x: UserRoleType) => x.replaceAll('_', ' ').replaceAll('ROLE', ''))
    .join(', ');
}

// sets the selected report to null when the main report category is updated.
function setReportCategory() {
  selectedReport.value = null;
}

// get the selected reports details
const reportDetails = computed(() => {
  return reports.find((x) => x.name === selectedReport.value);
});

// request for the report. Emitted from report component
async function generateReport(request: GenerateReportRequest) {
  if (!reportDetails.value) {
    return;
  }
  if (reportDetails.value.endPoint === 'generateAndEmailDailyJobReports') {
    await useRootStore().generateClientDailyJobReportsAndSendEmail(request);
    isLoading.value = false;
  } else {
    setResponseListener(true);
    const result = await useRootStore().generateReport(
      request,
      reportDetails.value.endPoint,
    );

    if (result?.isSuccessful) {
      showNotification('Completed Successfully', {
        title: `${request.accessType} Report`,
        type: HealthLevel.SUCCESS,
      });
    } else {
      showNotification('Error Cannot Complete', {
        title: `${request.accessType} Report`,
        type: HealthLevel.ERROR,
      });
    }
    return result;
  }
}

// Takes in a string array of file names that will be concatenated together.
// Returns the file name
function getFileName(fileNames: string[]) {
  try {
    const companyId: string = sessionManager.getCompanyId();
    const divisionId: string = sessionManager.getDivisionId();
    const date = returnFormattedDate(moment().valueOf(), 'DD-MM-YYYY');
    return fileNames
      .join(' ')
      .replaceAll('company', companyId)
      .replaceAll('division', divisionId)
      .replaceAll('runDate', date)
      .trim()
      .replaceAll('_', '-')
      .replaceAll(' ', '-')
      .toLowerCase();
  } catch (e) {
    return '';
  }
}

// Takes in a string array of file names that will be concatenated together. Returns the file name
function getReportQuery(query: string[]) {
  try {
    return query;
  } catch (e) {
    return [];
  }
}

/**
 * Callback for when the report request fails
 */
function handleErrorResponse(): void {
  isLoading.value = false;
}

/**
 * Callback for mitt listener
 */
function handleReportResponse(): void {
  isLoading.value = false;
  setResponseListener(false);
}
/**
 * When we make the request to download POD, we turn on the mitt listener so
 * we can reset the loader when the response comes in
 * @param value boolean value to set the listener
 */
function setResponseListener(value: boolean) {
  if (value) {
    Mitt.on('encodedReport', handleReportResponse);
    Mitt.on('errorCode', handleErrorResponse);
  } else {
    Mitt.off('encodedReport', handleReportResponse);
    Mitt.off('errorCode', handleErrorResponse);
  }
}
/**
 * When the component is destroyed, we remove the mitt listener
 */
onBeforeUnmount(() => {
  setResponseListener(false);
});
</script>

<style scoped lang="scss">
.top-panel {
  position: fixed;
  transition: 0.2s;
  background-color: var(--background-color-400);
  top: 40px;
  height: 49px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  border-bottom: 1px solid $translucent;
  padding: 0 8px;
  display: flex;
  align-items: center;
  z-index: 100;
}

.reports-container {
  padding-top: 67px;
  padding-bottom: 28px;
}

.header-text {
  font-size: $font-size-14;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.02em;
  color: $pud-flag;
}

.report-info {
  div {
    color: var(--text-color);
  }
}
</style>
