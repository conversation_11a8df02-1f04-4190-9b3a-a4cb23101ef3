<template>
  <div class="adjustment-charges-container px-3">
    <v-layout column fill-height justify-end>
      <v-flex>
        <v-layout align-center>
          <v-spacer />
        </v-layout>
      </v-flex>
    </v-layout>

    <v-dialog
      v-model="showAddEditAdjustmentChargeDialog"
      width="600px"
      class="remittance-dialog"
      content-class="v-dialog-custom"
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header"
      >
        <span>Adjustment Charge Maintenance</span>

        <div class="app-theme__center-content--closebutton" @click="cancelEdit">
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout class="app-theme__center-content--body">
        <v-flex md12>
          <v-layout wrap pa-3>
            <v-flex md12 class="pb-3" v-if="showAddEditAdjustmentChargeDialog">
              <v-alert :value="validationOptions.info.length > 0" type="info">
                <ul>
                  <li
                    v-for="(info, index) in validationOptions.info"
                    :key="index"
                  >
                    {{ info }}
                  </li>
                </ul>
              </v-alert>
              <v-alert
                :value="
                  !validationOptions.canSave &&
                  validationOptions.warnings.length > 0
                "
                type="error"
              >
                <p
                  class="ma-0"
                  v-for="(error, index) in validationOptions.warnings"
                  :key="index"
                >
                  <span>&#8226;</span> {{ error }}
                </p>
              </v-alert>
              <v-alert
                :value="
                  validationOptions.canSave &&
                  validationOptions.warnings.length > 0
                "
                type="warning"
              >
                <ul>
                  <li
                    v-for="(error, index) in validationOptions.warnings"
                    :key="index"
                  >
                    {{ error }}
                  </li>
                </ul>
              </v-alert>
            </v-flex>

            <v-flex md6 px-1>
              <v-select
                box
                label="Category"
                v-model="adjustmentCharge.categoryId"
                :items="chargeCategoryList"
                :disabled="!validationOptions.categorySelectsAvailable"
                item-text="longName"
                item-value="categoryId"
              />
            </v-flex>
            <v-flex md6 px-1>
              <v-select
                box
                label="Option Type"
                v-model="adjustmentCharge.subCategoryId"
                :items="selectedCategoryOptions"
                :disabled="!validationOptions.categorySelectsAvailable"
                item-text="longName"
                item-value="subCategoryId"
              />
            </v-flex>
            <v-flex md6 px-1>
              <v-text-field
                box
                type="number"
                label="Amount ($)"
                hint="Not including GST"
                v-model.number="adjustmentCharge.amount"
                @focus="$event.target.select()"
              />
            </v-flex>
            <v-flex md6 px-1>
              <v-select
                box
                label="Billed"
                v-model="adjustmentCharge.billedMultiplier"
                :items="billingCycles"
                item-text="longName"
                item-value="id"
              />
            </v-flex>
            <v-flex md6 px-1>
              <v-select
                box
                label="Status"
                :items="statusListSelect"
                v-model="selectedStatus"
                item-value="sid"
                item-text="text"
              >
              </v-select>
            </v-flex>
            <v-flex md6 px-1 v-if="isEditingCustom || isNewCustom">
              <v-autocomplete
                box
                label="Applies To"
                v-model="adjustmentCharge.appliesTo"
                :items="appliesToList"
                :cache-items="false"
                item-text="value"
                item-value="id"
                clearable
                :hint="appliesToLabel"
                persistent-hint
                multiple
              >
                <template v-slot:selection="data">
                  <v-chip
                    :selected="data.selected"
                    close
                    class="chip--select-multi"
                    @input="removeEntity(data.item)"
                  >
                    {{ data.item.value }}
                  </v-chip>
                </template>
              </v-autocomplete>
            </v-flex>
          </v-layout>
          <v-layout row wrap>
            <v-flex md12>
              <v-divider></v-divider>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-btn color="red" flat @click="cancelEdit"> Cancel </v-btn>
                <v-spacer></v-spacer>
                <v-btn
                  :disabled="!validationOptions.canSave"
                  color="blue"
                  depressed
                  @click="saveAdjustmentCharge"
                  >Save Charge
                </v-btn>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout></v-dialog
    >
    <form-card color="orange">
      <div slot="heading">Adjustment Charge List</div>
      <div slot="subheading">All default and custom adjustment charges</div>
      <div slot="top-right">
        <v-layout> </v-layout>
      </div>
      <div slot="content">
        <v-layout>
          <v-flex md4 pr-3>
            <v-select
              outline
              label="Category"
              v-model="categoryIdFilter"
              :items="chargeCategoryList"
              :disabled="!validationOptions.categorySelectsAvailable"
              item-text="longName"
              item-value="categoryId"
              class="v-solo-custom"
            />
          </v-flex>
          <v-switch label="Show Inactive" v-model="showInactiveCharges">
          </v-switch>
          <v-spacer></v-spacer>
        </v-layout>
        <v-flex v-if="sortedAdjustmentCharges">
          <v-layout
            v-for="subcategory in sortedAdjustmentCharges?.subcategoryList"
            :key="subcategory.key"
            row
            wrap
          >
            <v-flex md12 class="table-main-header">
              <v-layout align-center>
                <b class="table-header-txt">{{
                  subcategory.subCategoryName
                }}</b>
                <v-spacer></v-spacer>
                <v-btn
                  color="orange"
                  depressed
                  @click="
                    createNewCharge(
                      categoryIdFilter,
                      subcategory.id,
                      subcategory.hasExistingDefaultCharge,
                    )
                  "
                  class="ma-0"
                  solo
                  flat
                  :disabled="!isAuthorised()"
                >
                  Add New Charge
                  <v-icon right dark small>fas fa-plus</v-icon>
                </v-btn>
              </v-layout>
            </v-flex>
            <v-flex md12 mb-3>
              <v-data-table
                :headers="headers"
                :items="subcategory.chargeList"
                hide-actions
                item-key="id"
                class="gd-dark-theme"
                no-data-text="No default charges or custom charges currently exist."
              >
                <template v-slot:items="props">
                  <tr>
                    <td :class="!props.item.isDefault ? 'pl-3' : ''">
                      <span v-if="!props.item.isDefault" class="pr-1"
                        >&mdash;</span
                      >
                      <span v-if="props.item.isDefault">Default Rate</span>
                      <span v-else>Custom</span>
                    </td>
                    <td>
                      {{
                        props.item.amount
                          ? DisplayCurrencyValue(props.item.amount)
                          : '-'
                      }}
                    </td>

                    <td>{{ props.item.billingCycleName }}</td>
                    <td>
                      <v-icon small v-if="props.item.isDefault">
                        fal fa-check
                      </v-icon>
                      <span v-else> - </span>
                    </td>
                    <td>
                      <span v-if="props.item.isDefault">All</span>
                      <span v-else>{{ appliedTo(props.item) }}</span>
                    </td>
                    <td>{{ namedStatusList(props.item.statusList) }}</td>
                    <td class="text-xs-center">
                      <v-icon
                        small
                        @click="editAdjustmentCharge(props.item)"
                        :disabled="!isAuthorised()"
                        color="accent"
                      >
                        fa fa-edit
                      </v-icon>
                    </td>
                  </tr>
                </template>
                <template v-slot:no-results>
                  <v-alert :value="true" color="error" icon="warning">
                    Your search found no results.
                  </v-alert>
                </template>
              </v-data-table>
            </v-flex>
          </v-layout>
        </v-flex>
      </div>
    </form-card>
  </div>
</template>

<script setup lang="ts">
import FormCard from '@/components/common/ui-elements/form_card.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { isDeviceDeductionAdjustmentCharge } from '@/helpers/RateHelpers/AdjustmentChargeHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { AdjustmentChargeAssociationType } from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeAssociationType';
import {
  AdjustmentChargeBillingCycles,
  adjustmentChargeBillingCycles,
} from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeBillingCycles';
import { AdjustmentChargeCategory } from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeCategory';
import AdjustmentCharge, {
  initialiseAdjustmentCharge,
} from '@/interface-models/Generic/AllowanceAndDeductions/AdjustmentCharge';

// import { AdjustmentChargeCategoryType } from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeCategoryType';
import AssociatedDevice from '@/interface-models/Generic/AssociatedDevice/AssociatedDevice';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import StatusConfig from '@/interface-models/Status/StatusConfig';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useRootStore } from '@/store/modules/RootStore';
import { computed, ComputedRef, Ref, ref, watch } from 'vue';

interface AppliedTo {
  id: string;
  value: string;
}
interface ValidationOptions {
  warnings: string[];
  info: string[];
  canSave: boolean;
  categorySelectsAvailable: boolean;
  showSaveButton: boolean;
  showForm: boolean;
}

const fleetAssetStore = useFleetAssetStore();

const selectedStatus: Ref<number> = ref(4);

const isEditingDefault: Ref<boolean> = ref(false);
const isEditingCustom: Ref<boolean> = ref(false);
const isNewDefault: Ref<boolean> = ref(false);
const isNewCustom: Ref<boolean> = ref(false);
const showInactiveCharges: Ref<boolean> = ref(false);

const billingCycles: Ref<AdjustmentChargeBillingCycles[]> = ref(
  adjustmentChargeBillingCycles,
);
const adjustmentCharge: Ref<AdjustmentCharge> = ref(new AdjustmentCharge());
const adjustmentChargeStore = useAdjustmentChargeStore();
const categoryIdFilter: Ref<number> = ref(1);

const headers: TableHeader[] = [
  {
    text: 'Type',
    align: 'left',
    sortable: false,
    value: 'subCategoryId',
  },
  {
    text: 'Amount ($)',
    align: 'left',
    sortable: false,
    value: 'amount',
  },
  {
    text: 'Billed',
    align: 'left',
    sortable: false,
    value: 'billedMultiplier',
  },
  {
    text: 'Default',
    align: 'left',
    sortable: false,
    value: 'isDefault',
  },
  {
    text: 'Applied To',
    align: 'left',
    sortable: false,
    value: '',
  },
  {
    text: 'Status',
    align: 'left',
    sortable: false,
    value: 'status',
  },
  {
    text: 'Edit',
    align: 'center',
    sortable: false,
    value: 'edit',
  },
];

const chargeCategoryList = computed(() => {
  return adjustmentChargeStore.adjustmentChargeCategories.filter(
    (cat) => cat.allowRecurringCharges,
  );
});

const selectedCategoryOptions = computed(() => {
  const foundCategory = chargeCategoryList.value.find(
    (item: AdjustmentChargeCategory) =>
      item.categoryId === adjustmentCharge.value.categoryId,
  );

  if (foundCategory !== undefined) {
    return foundCategory.subCategories;
  } else {
    return [];
  }
});

// Controls the visibility of the edit dialog
const showAddEditAdjustmentChargeDialog = computed({
  get: () => validationOptions.value.showForm,
  set: (value: boolean) => {
    if (!value) {
      cancelEdit();
    }
  },
});

const statusListSelect = computed(() => {
  const fullList = useRootStore().statusTypeList;
  const applicableStatusList = fullList.filter(
    (status: StatusConfig) =>
      status.enumValue === 'ACTIVE' || status.enumValue === 'INACTIVE',
  );
  return applicableStatusList;
});

function createCustom(adjustmentCharge: AdjustmentCharge) {
  resetStateValues();
  adjustmentCharge = initialiseAdjustmentCharge(adjustmentCharge);
  adjustmentCharge._id = undefined;
  adjustmentCharge.isDefault = false;
  selectedStatus.value = 4;
  isNewCustom.value = true;
}

function appliedTo(adjustmentCharge: AdjustmentCharge) {
  const entities = adjustmentCharge.appliesTo;
  let textResult = '';

  if (adjustmentCharge.appliesTo.length > 2) {
    return 'Various';
  } else {
    switch (adjustmentCharge.unitId) {
      case AdjustmentChargeAssociationType.FLEET_ASSET:
        textResult = entities
          .map((id) => fleetAssetStore.getFleetAssetFromFleetAssetId(id))
          .filter((fa) => fa !== undefined)
          .map((fa) => fa!.csrAssignedId)
          .join(', ');
        break;
      case AdjustmentChargeAssociationType.FLEET_ASSET_OWNER:
        textResult = entities
          .map((id) => useFleetAssetOwnerStore().getOwnerFromOwnerId(id))
          .filter((fa) => fa !== undefined)
          .map((fa) => fa!.name)
          .join(', ');
        break;
      case AdjustmentChargeAssociationType.DRIVER:
        textResult = entities
          .map((id) => useDriverDetailsStore().getDriverFromDriverId(id))
          .filter((fa) => fa !== undefined)
          .map((fa) => fa!.displayName)
          .join(', ');
        break;
    }
  }

  return textResult;
}

const adjustmentCharges = computed(() => {
  const allCharges: AdjustmentCharge[] =
    useAdjustmentChargeStore().adjustmentCharges;
  return allCharges;
});

const sortedAdjustmentCharges = computed(() => {
  interface CategoryGroup {
    id: number;
    categoryName: string;
    subcategoryList: SubCategoryGroup[];
  }
  interface SubCategoryGroup {
    key: string;
    id: number;
    subCategoryName: string;
    hasExistingDefaultCharge: boolean;
    chargeList: AdjustmentCharge[];
  }

  const cat = adjustmentChargeStore.adjustmentChargeCategories.find(
    (c) => c.categoryId === categoryIdFilter.value,
  );
  if (!cat) {
    return;
  }
  const catId = cat.categoryId;
  const catName = cat.longName;
  const subCategories: SubCategoryGroup[] = [];
  // Find all the charges for category ID
  const chargesForCategory = adjustmentCharges.value.filter(
    (ac) => ac.categoryId === cat.categoryId,
  );
  // If more than one is found, we should split them into their subcategories
  cat.subCategories.forEach((subcat) => {
    // Filter for each subcategory type
    const chargesForSubcat = chargesForCategory.filter(
      (cfc) => cfc.subCategoryId === subcat.subCategoryId,
    );
    // If at least one is found, construct Group object and push into list
    const foundDefault = chargesForSubcat.find((c) => c.isDefault);
    if (foundDefault) {
      const subcatGroup: SubCategoryGroup = {
        key: `subcat-${catId}-${subcat.subCategoryId}`,
        id: subcat.subCategoryId,
        subCategoryName: subcat.longName,
        hasExistingDefaultCharge: true,
        chargeList: [foundDefault].concat(
          chargesForSubcat.filter((c) => !c.isDefault),
        ),
      };
      subCategories.push(subcatGroup);
    } else {
      const subcatGroup: SubCategoryGroup = {
        key: `subcat-${catId}-${subcat.subCategoryId}`,
        id: subcat.subCategoryId,
        subCategoryName: subcat.longName,
        hasExistingDefaultCharge: false,
        chargeList: [],
      };
      subCategories.push(subcatGroup);
    }
  });
  const catGroup: CategoryGroup = {
    id: catId,
    categoryName: catName,
    subcategoryList: subCategories,
  };
  return catGroup;
});

/**
 * Edits an existing adjustment charge.
 * @param {AdjustmentCharge} adjustmentCharge - The adjustment charge to edit.
 */
function editAdjustmentCharge(adjustmentCharges: AdjustmentCharge): void {
  adjustmentCharge.value = adjustmentCharges;
  if (adjustmentCharge.value.isDefault) {
    isEditingCustom.value = false;
    isEditingDefault.value = true;
  } else {
    isEditingCustom.value = true;
    isEditingDefault.value = false;
  }
  selectedStatus.value = adjustmentCharge.value.statusList[0];
}

/**
 * Creates a new adjustment charge.
 * @param {number} categoryId - The category ID for the new charge.
 * @param {number} subCategoryId - The subcategory ID for the new charge.
 * @param {boolean} hasExistingDefaultCharge - Indicates if there is an existing default charge.
 */
function createNewCharge(
  categoryId: number,
  subCategoryId: number,
  hasExistingDefaultCharge: boolean,
): void {
  adjustmentCharge.value = new AdjustmentCharge();
  adjustmentCharge.value.categoryId = categoryId;
  adjustmentCharge.value.subCategoryId = subCategoryId;
  isNewDefault.value = !hasExistingDefaultCharge;
  isNewCustom.value = hasExistingDefaultCharge;
  adjustmentCharge.value.isDefault = !hasExistingDefaultCharge;
  isEditingDefault.value = false;
  isEditingCustom.value = false;
  selectedStatus.value = 4;
}

/**
 * Returns a boolean indicating if a default charge already exists for the
 * currently selected Category, Option Type and Unit.
 */
const validateDefaultChargeExists = computed(() => {
  const defaultExists = adjustmentCharges.value.filter(
    (charge: AdjustmentCharge) =>
      charge.categoryId === adjustmentCharge.value.categoryId &&
      charge.subCategoryId === adjustmentCharge.value.subCategoryId &&
      charge.unitId === adjustmentCharge.value.unitId &&
      charge.isDefault === true,
  );
  return defaultExists.length > 0;
});

// validationOptions returns the current state for what is allowed and shown
const validationOptions: ComputedRef<ValidationOptions> = computed(() => {
  const validationOptions: ValidationOptions = {
    warnings: [],
    info: [],
    canSave: true,
    categorySelectsAvailable: true,
    showSaveButton: false,
    showForm: false,
  };

  if (isNewDefault.value) {
    validationOptions.showForm = true;
    validationOptions.showSaveButton = true;
    if (validateDefaultChargeExists.value) {
      validationOptions.canSave = false;
      validationOptions.warnings.push(
        'A default charge already exists for the currently selected Category, Option Type and Unit. Please change these values or find and edit the existing charge',
      );
    }
    validationOptions.info.push(
      'You are creating a DEFAULT charge. Any changes you make will apply to ALL Fleet Assets that do not have a custom rate applied.',
    );
    if (!adjustmentCharge.value.amount) {
      validationOptions.info.push(
        'This is currently a $0 DEFAULT charge. By default, Fleet Assets will NOT be invoiced for charge UNLESS a custom rate is applied to them.',
      );
    }
  } else if (isNewCustom.value) {
    validationOptions.categorySelectsAvailable = false;
    validationOptions.showSaveButton = true;
    validationOptions.showForm = true;
    validationOptions.info.push(
      'You are creating a new CUSTOM charge. Please complete the details, and select the assets the custom rate will apply to.',
    );
    if (adjustmentCharge.value.appliesTo.length === 0) {
      validationOptions.canSave = false;
      validationOptions.info.push(
        'A custom charge must have at least one Fleet Asset being applied to. ',
      );
    }
  } else if (isEditingDefault.value) {
    validationOptions.showForm = true;
    validationOptions.showSaveButton = true;
    validationOptions.categorySelectsAvailable = false;
    validationOptions.warnings.push(
      'You are editing a DEFAULT charge. Any changes you make will apply to all Fleet Assets that do not have custom rates applied.',
    );
  } else if (isEditingCustom.value) {
    validationOptions.showForm = true;
    validationOptions.showSaveButton = true;
    validationOptions.categorySelectsAvailable = false;
    // validationOptions.warnings.push(
    //   'You are editing a DEFAULT charge. Any changes you make will apply to all Fleet Assets that do not have custom rates applied.'
    // );
    if (!adjustmentCharge.value.amount) {
      validationOptions.info.push(
        'This is currently a $0 Custom charge. Fleet Assets selected for charge will not be invoiced.',
      );
    }
  } else {
    validationOptions.canSave = false;
    validationOptions.showSaveButton = false;
    validationOptions.showForm = false;
  }

  return validationOptions;
});

function resetStateValues(): void {
  isEditingDefault.value = false;
  isEditingCustom.value = false;
  isNewDefault.value = false;
  isNewCustom.value = false;
  adjustmentCharge.value = new AdjustmentCharge();
  selectedStatus.value = 4;
}

function cancelEdit(): void {
  resetStateValues();
}

/**
 * Returns a list of Fleet Assets that can be selected for the current
 * adjustment charge. If the adjustment charge is not a device deduction, then
 * all fleet assets are selectable. If the adjustment charge is a device
 * deduction, then only fleet assets that have the device type associated with
 * the adjustment charge are selectable.
 */
const fleetAssets: ComputedRef<FleetAssetSummary[]> = computed(() => {
  if (!isDeviceDeductionAdjustmentCharge(adjustmentCharge.value.categoryId)) {
    return fleetAssetStore.getAllFleetAssetList;
  } else {
    const fleetAssetIds: string[] = [];
    // Find fleet asset owners that have a device association with the
    // adjustment charge's device type and an
    for (const owner of allFleetAssetOwners.value) {
      const deviceFleetAssetAssociations: AssociatedDevice[] =
        owner.associatedDevices.filter(
          (device: AssociatedDevice) =>
            device.associationTypeId ===
              AdjustmentChargeAssociationType.FLEET_ASSET &&
            device.deviceTypeId === adjustmentCharge.value.subCategoryId &&
            (device.retiredEpoch === null || device.retiredEpoch === undefined),
        );

      for (const device of deviceFleetAssetAssociations) {
        fleetAssetIds.push(device.associationId);
      }
    }
    const selectableFleetAssets = fleetAssetIds
      .map((fid) => fleetAssetStore.getFleetAssetFromFleetAssetId(fid))
      .filter((fa) => fa !== undefined) as FleetAssetSummary[];

    return selectableFleetAssets;
  }
});

/**
 * Returns a list of drivers that can be selected for the current adjustment.
 * If the adjustment charge is not a device deduction, then all drivers are
 * selectable. If the adjustment charge is a device deduction, then only
 * drivers that have the device type associated with the adjustment charge are
 * selectable.
 */
const drivers: ComputedRef<DriverDetailsSummary[]> = computed(() => {
  const allDrivers = useDriverDetailsStore().getDriverList;
  if (!isDeviceDeductionAdjustmentCharge(adjustmentCharge.value.categoryId)) {
    return allDrivers;
  } else {
    const driverIds: string[] = [];
    for (const owner of allFleetAssetOwners.value) {
      const deviceDriverAssociations: AssociatedDevice[] =
        owner.associatedDevices.filter(
          (device: AssociatedDevice) =>
            device.associationTypeId ===
              AdjustmentChargeAssociationType.DRIVER &&
            device.deviceTypeId === adjustmentCharge.value.subCategoryId,
        );
      for (const device of deviceDriverAssociations) {
        driverIds.push(device.associationId);
      }
    }
    const selectableDrivers: DriverDetailsSummary[] = [];
    for (const id of driverIds) {
      const foundDriver: DriverDetailsSummary | undefined = allDrivers.find(
        (driver: DriverDetailsSummary) => driver.driverId === id,
      );

      if (foundDriver !== undefined) {
        selectableDrivers.push(foundDriver);
      }
    }
    return selectableDrivers;
  }
});

const allFleetAssetOwners: ComputedRef<FleetAssetOwnerSummary[]> = computed(
  () => {
    return useFleetAssetOwnerStore().getOwnerList;
  },
);

/**
 * Returns a list of fleet asset owners that can be selected for the current
 * adjustment charge. If the adjustment charge is not a device deduction, then
 * all fleet asset owners are selectable. If the adjustment charge is a device
 * deduction, then only fleet asset owners that have the device type associated
 * with the adjustment charge are selectable.
 */
const fleetAssetOwners: ComputedRef<FleetAssetOwnerSummary[]> = computed(() => {
  const allFleetAssetOwner = allFleetAssetOwners.value;
  if (!isDeviceDeductionAdjustmentCharge(adjustmentCharge.value.categoryId)) {
    return allFleetAssetOwner;
  } else {
    const ownerIds: string[] = [];
    for (const owner of allFleetAssetOwner) {
      const deviceOwnerAssociations: AssociatedDevice[] =
        owner.associatedDevices.filter(
          (device: AssociatedDevice) =>
            device.associationTypeId ===
              AdjustmentChargeAssociationType.FLEET_ASSET_OWNER &&
            device.deviceTypeId === adjustmentCharge.value.subCategoryId,
        );
      if (deviceOwnerAssociations.length > 0) {
        ownerIds.push(owner.ownerId);
      }
    }
    const selectableOwners: FleetAssetOwnerSummary[] = [];
    for (const id of ownerIds) {
      const foundOwner: FleetAssetOwnerSummary | undefined =
        allFleetAssetOwner.find(
          (owner: FleetAssetOwnerSummary) => owner.ownerId === id,
        );

      if (foundOwner !== undefined) {
        selectableOwners.push(foundOwner);
      }
    }
    return selectableOwners;
  }
});

/**
 * Returns the label to display in the applies to select based on the category
 * of the current adjustment charge. Used in template to display label.
 */
const appliesToLabel: ComputedRef<string> = computed(() => {
  return isDeviceDeductionAdjustmentCharge(adjustmentCharge.value.categoryId)
    ? 'Only entities containing the selected device type will be shown'
    : 'Select entities to apply this charge to';
  ('Select entities to apply charge to');
});

/**
 * Removes an entity from the list of entities that the current adjustment
 * charge can be applied to. Modelled to icon on chip in applies to select.
 * @param item - The entity to remove.
 */
function removeEntity(item: AppliedTo) {
  const index = adjustmentCharge.value.appliesTo.indexOf(item.id);
  if (index >= 0) {
    adjustmentCharge.value.appliesTo.splice(index, 1);
  }
}

/**
 * Returns a list of selectable entities that the current adjustment charge can
 * be applied to. Used in template in v-autocomplete.
 */
const appliesToList: ComputedRef<AppliedTo[]> = computed(() => {
  // Initialize an empty list to store the applicable entities
  let entityIdList: AppliedTo[] = [];

  // Determine the type of entity the charge applies to, and populate the full
  // entityIdList accordingly
  switch (adjustmentCharge.value.unitId) {
    case AdjustmentChargeAssociationType.FLEET_ASSET:
      entityIdList = fleetAssets.value.map((asset: FleetAssetSummary) => ({
        id: asset.fleetAssetId,
        value: `${asset.csrAssignedId} ${asset.registrationNumber}`,
      }));
      break;
    case AdjustmentChargeAssociationType.FLEET_ASSET_OWNER:
      entityIdList = fleetAssetOwners.value.map(
        (owner: FleetAssetOwnerSummary) => ({
          id: owner.ownerId,
          value: owner.name,
        }),
      );
      break;
    case AdjustmentChargeAssociationType.DRIVER:
      entityIdList = drivers.value.map((driver: DriverDetailsSummary) => ({
        id: driver.driverId,
        value: driver.displayName,
      }));
      break;
  }

  // Filter adjustment charges to find existing custom charges that are NOT
  // the default, and aren't the current charge we're adding/editing.
  const idsWithCustomCharges = adjustmentCharges.value
    .filter(
      (charge: AdjustmentCharge) =>
        charge.categoryId === adjustmentCharge.value.categoryId &&
        charge.subCategoryId === adjustmentCharge.value.subCategoryId &&
        charge.unitId === adjustmentCharge.value.unitId &&
        charge.isDefault === false &&
        charge._id !== adjustmentCharge.value._id,
    )
    .flatMap((charge: AdjustmentCharge) => charge.appliesTo);

  // Remove entities from the entityIdList that already have custom charges applies
  entityIdList = entityIdList.filter(
    (item: AppliedTo) => !idsWithCustomCharges.includes(item.id),
  );

  // Return the final entityIdList of applicable entities
  return entityIdList;
});

/**
 * Sends API request to save the current adjustment charge, displays a
 * notification and resets the component state.
 */
async function saveAdjustmentCharge(): Promise<void> {
  const result = await useAdjustmentChargeStore().saveAdjustmentCharge(
    adjustmentCharge.value,
  );
  // If save was successful, update adjustment charge list.
  if (result) {
    showNotification('Adjustment Charge Saved.', {
      type: HealthLevel.SUCCESS,
    });
  } else {
    showNotification('Error saving Adjustment Charge.', {
      type: HealthLevel.ERROR,
    });
  }

  resetStateValues();
}

function namedStatusList(statusList: number[]): string {
  let textResult = '';
  for (const statusId of statusList) {
    const foundStatus = statusListSelect.value.find(
      (status: StatusConfig) => status.sid === statusId,
    );

    if (foundStatus !== undefined) {
      textResult += foundStatus.text;
    }
  }
  return textResult;
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

watch(selectedStatus, (value) => {
  if (value !== null) {
    adjustmentCharge.value.statusList = [value];
  }
});
</script>

<style scoped lang="scss">
.table-main-header {
  padding: 2px 6px;
  margin-bottom: 2px;
  .table-header-txt {
    font-size: $font-size-16;
    color: var(--primary);
  }
}
.adjustment-charges-container {
  padding-top: 20px;
}
</style>
