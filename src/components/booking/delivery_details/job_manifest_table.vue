<template>
  <v-flex md12 class="manifest-table-container">
    <v-divider />
    <v-layout
      justify-space-between
      v-if="!formDisabled"
      class="header-container"
    >
      <div>
        <v-layout align-center>
          <h6 class="pr-2">Manifest Items</h6>
          <h6 class="pr-2 pl-2">( {{ props.manifestList.length }} )</h6>
        </v-layout>
      </div>
      <div class="unit-selectors">
        <div class="unit-group">
          <label for="lwhUnit">Dimensions Unit:</label>
          <div class="select-wrapper">
            <select v-model="lwhUnit" id="lwhUnit" :disabled="!!editedManifest">
              <option value="cm">Centimetres (cm)</option>
              <option value="m">Metres (m)</option>
            </select>
            <v-icon size="10" v-if="!editedManifest">
              keyboard_arrow_down
            </v-icon>
          </div>
        </div>
        <v-divider vertical />
        <div class="unit-group">
          <label for="weightUnit">Weight Unit:</label>
          <div class="select-wrapper">
            <select
              v-model="weightUnit"
              id="weightUnit"
              :disabled="!!editedManifest"
            >
              <option value="g">Grams (g)</option>
              <option value="kg">Kilograms (kg)</option>
              <option value="t">Tonnes (t)</option>
            </select>
            <v-icon size="10" v-if="!editedManifest">
              keyboard_arrow_down
            </v-icon>
          </div>
        </div>
      </div>
      <v-spacer></v-spacer>
      <div v-if="props.pudItem?.legTypeFlag === 'P'">
        <v-btn
          solo
          @click="addManifest"
          :disabled="formDisabled"
          class="manifest-btn add"
        >
          <v-icon pr-2 size="16"> add </v-icon>Add Manifest Item
        </v-btn>
      </div>
      <!-- v-select for selecting manifests when legTypeFlag = 'D' -->
    </v-layout>
    <div v-if="!formDisabled && props.pudItem?.legTypeFlag === 'D'">
      <v-flex md5 d-flex mb-2>
        <v-select
          v-model="selectedManifestController"
          :items="pickupManifestItems"
          item-text="description"
          item-value="id"
          label="Select Manifest"
          class="v-solo-custom"
          multiple
          small-chips
          flat
          solo
          outline
          color="orange"
          hide-details
        ></v-select>
      </v-flex>
    </div>

    <!-- Table for displaying manifest items -->
    <v-flex md12 class="table-container custom-scrollbar">
      <table
        v-if="tableData !== null"
        class="simple-data-table"
        :class="formDisabled ? 'inSummary' : ''"
      >
        <thead>
          <tr>
            <th>Description</th>
            <th>Length ({{ lwhUnit }})</th>
            <th>Width ({{ lwhUnit }})</th>
            <th>Height ({{ lwhUnit }})</th>
            <th>Weight ({{ weightUnit }})</th>
            <th>Quantity</th>
            <th>Barcode</th>
            <th v-if="!formDisabled">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(manifestItem, index) in tableData"
            v-show="!isMinimised || index < 4"
            :key="manifestItem.id"
          >
            <template
              v-if="editedManifest?.id && editedManifest.id === manifestItem.id"
            >
              <td md4>
                <v-text-field
                  v-model="editedManifest.description"
                  label="Description"
                  maxlength="100"
                  flat
                  solo
                  hide-details
                  :rules="[validate.required]"
                  class="v-solo-custom form-field-required"
                  :disabled="formDisabled || props.pudItem?.legTypeFlag === 'D'"
                ></v-text-field>
              </td>
              <td md1>
                <v-text-field
                  v-model.number="editedManifest.length"
                  :label="`L (${lwhUnit})`"
                  type="number"
                  :min="0"
                  :disabled="formDisabled || props.pudItem?.legTypeFlag === 'D'"
                  flat
                  solo
                  hide-details
                  class="v-solo-custom"
                ></v-text-field>
              </td>
              <td md1>
                <v-text-field
                  v-model.number="editedManifest.width"
                  :label="`W (${lwhUnit})`"
                  type="number"
                  :min="0"
                  :disabled="formDisabled || props.pudItem?.legTypeFlag === 'D'"
                  flat
                  solo
                  hide-details
                  class="v-solo-custom"
                ></v-text-field>
              </td>
              <td md1>
                <v-text-field
                  v-model.number="editedManifest.height"
                  :label="`H (${lwhUnit})`"
                  type="number"
                  :min="0"
                  :disabled="formDisabled || props.pudItem?.legTypeFlag === 'D'"
                  flat
                  solo
                  hide-details
                  class="v-solo-custom"
                ></v-text-field>
              </td>
              <td md1>
                <v-text-field
                  v-model.number="editedManifest.weight"
                  :label="`Weight (${weightUnit})`"
                  type="number"
                  :min="0"
                  :disabled="formDisabled || props.pudItem?.legTypeFlag === 'D'"
                  flat
                  solo
                  hide-details
                  class="v-solo-custom"
                ></v-text-field>
              </td>
              <td md1 v-if="props.pudItem?.legTypeFlag === 'P'">
                <v-text-field
                  v-model.number="editedManifest.quantity"
                  label="Quantity"
                  type="number"
                  :min="manifestMap.get(manifestItem.id)?.totalDropQuantity"
                  :disabled="formDisabled"
                  flat
                  solo
                  hide-details
                  :rules="[validate.required]"
                  class="v-solo-custom form-field-required"
                ></v-text-field>
              </td>
              <td md2 v-else>
                <div class="qty-input">
                  <v-text-field
                    v-model.number="editedManifest.quantity"
                    :max="remainingQuantity(editedManifest)"
                    :min="0"
                    type="number"
                    label="Enter Quantity"
                    flat
                    solo
                    hide-details
                    class="v-solo-custom"
                    :rules="[validate.required]"
                  ></v-text-field>
                  <!-- Remaining Quantity  -->
                  <span>
                    Remaining: {{ remainingQuantity(editedManifest) }}
                  </span>
                </div>
              </td>
              <td md2>
                <v-text-field
                  v-model="editedManifest.barcode"
                  label="Barcode"
                  :disabled="formDisabled || props.pudItem?.legTypeFlag === 'D'"
                  solo
                  flat
                  hide-details
                  class="v-solo-custom"
                ></v-text-field>
              </td>
              <td v-if="!formDisabled" md1>
                <v-layout align-center justify-top>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on }">
                      <v-btn
                        solo
                        icon
                        @click="saveManifest()"
                        class="manifest-btn save"
                        v-on="on"
                      >
                        <v-icon size="18"> save </v-icon>
                      </v-btn>
                    </template>
                    <span>Save Manifest Item</span>
                  </v-tooltip>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on }">
                      <v-btn
                        solo
                        icon
                        @click="editedManifest = null"
                        class="manifest-btn cancel"
                        v-on="on"
                        color="white"
                      >
                        <v-icon size="18">close</v-icon>
                      </v-btn>
                    </template>
                    <span>Discard Changes</span>
                  </v-tooltip>
                </v-layout>
              </td>
            </template>
            <template v-else>
              <td md4>
                <span>{{ manifestItem.description || '-' }}</span>
              </td>
              <td md1>
                <span
                  >{{
                    parseDimensionFromMetres(manifestItem.length, lwhUnit) ||
                    '-'
                  }}
                  {{ lwhUnit }}</span
                >
              </td>
              <td md1>
                <span
                  >{{
                    parseDimensionFromMetres(manifestItem.width, lwhUnit) || '-'
                  }}
                  {{ lwhUnit }}</span
                >
              </td>
              <td md1>
                <span
                  >{{
                    parseDimensionFromMetres(manifestItem.height, lwhUnit) ||
                    '-'
                  }}
                  {{ lwhUnit }}</span
                >
              </td>
              <td md1>
                <span
                  >{{
                    parseWeightFromKilograms(manifestItem.weight, weightUnit) ||
                    '-'
                  }}
                  {{ weightUnit }}</span
                >
              </td>
              <td md1>
                <span v-if="props.pudItem?.legTypeFlag === 'D'">
                  {{ manifestItem.quantity }} /
                  {{
                    manifestMap.get(manifestItem.id)?.totalPickupQuantity || 0
                  }}
                </span>
                <span v-else>{{ manifestItem.quantity }}</span>
              </td>
              <td md2>
                <span>{{ manifestItem.barcode || '-' }}</span>
              </td>
              <td v-if="!formDisabled" md1>
                <v-layout align-center justify-end>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on }">
                      <v-btn
                        solo
                        icon
                        @click="editManifestItem(manifestItem)"
                        class="manifest-btn edit"
                        v-on="on"
                      >
                        <v-icon size="18"> edit </v-icon>
                      </v-btn>
                    </template>
                    <span>Edit Manifest Item</span>
                  </v-tooltip>
                  <v-tooltip bottom v-if="props.pudItem?.legTypeFlag === 'P'">
                    <template v-slot:activator="{ on }">
                      <v-btn
                        solo
                        icon
                        @click="removeManifest(manifestItem.id)"
                        class="manifest-btn delete"
                        v-on="on"
                        flat
                        :disabled="isPickupManifestInUse(manifestItem.id)"
                      >
                        <v-icon size="18">delete_forever</v-icon>
                      </v-btn>
                    </template>
                    <span>Remove Manifest Item</span>
                  </v-tooltip>
                </v-layout>
              </td>
            </template>
          </tr>
        </tbody>
      </table>
      <v-layout class="view-more-btn" v-if="tableData.length > 4">
        <v-btn
          v-if="isMinimised"
          block
          flat
          plain
          color="accent"
          @click="setIsMinimised(false)"
        >
          view All
          <v-icon>arrow_drop_down</v-icon></v-btn
        >
        <v-btn
          v-if="!isMinimised"
          block
          flat
          plain
          color="accent"
          @click="setIsMinimised(true)"
        >
          View Less ({{ tableData.length - 4 }})
          <v-icon>arrow_drop_up</v-icon></v-btn
        >
      </v-layout>
    </v-flex>
  </v-flex>
</template>

<script setup lang="ts">
import {
  formatDimensionToMetres,
  formatWeightToKilograms,
  parseDimensionFromMetres,
  parseWeightFromKilograms,
} from '@/helpers/DimensionsHelpers/DimensionsHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { Manifest } from '@/interface-models/Jobs/Manifest';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { v4 as uuidv4 } from 'uuid';
import {
  computed,
  ComputedRef,
  Ref,
  ref,
  toRef,
  watch,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    pudItem: PUDItem | null;
    formDisabled?: boolean;
    manifestList: Manifest[];
  }>(),
  {
    formDisabled: false,
    manifestList: () => [],
    pudItem: null,
  },
);

const emit = defineEmits<{
  (event: 'addOrUpdateManifestItem', payload: Manifest): void;
  (event: 'deleteManifestItem', payload: string): void;
  (event: 'update-edited-manifest', payload: Manifest | null): void;
}>();

const editedManifest: Ref<Manifest | null> = ref(null);
const validate = validationRules;
const isMinimised: Ref<boolean> = ref(true);

const fleetAssetStore = useFleetAssetStore();

// manifest table unit selections
const lwhUnit = toRef(fleetAssetStore, 'lwhUnit');
const weightUnit = toRef(fleetAssetStore, 'weightUnit');

/**
 * Returns list of table items to be displayed in the template. This a read-only
 * property - any saves or updates should be done via the 'saveManifest'
 * function and emits
 */
const tableData: ComputedRef<Manifest[]> = computed(() => {
  const manifests = [...props.manifestList];
  if (
    editedManifest.value &&
    !manifests.map((item) => item.id).includes(editedManifest.value.id)
  ) {
    manifests.push(editedManifest.value);
  }
  // If isMinimized is true, return only the first 4 items
  return isMinimised.value
    ? manifests.reverse().slice(0, 5)
    : manifests.reverse();
});

/**
 * Modelled to multi select for delivery, when we're selecting from available
 * pickup manifests to add. Returns a list of selected manifest IDs.
 */
const selectedManifestController: WritableComputedRef<string[]> = computed({
  get(): string[] {
    return tableData.value.map((item) => item.id);
  },
  set(newIds: string[]): void {
    const oldIds = tableData.value.map((item) => item.id);
    if (props.pudItem?.legTypeFlag === 'D') {
      // Add newly selected manifests to the table
      editedManifest.value = {
        ...new Manifest(),
        id: uuidv4(),
      };
      newIds.forEach((manifestId) => {
        if (!tableData.value.some((item) => item.id === manifestId)) {
          const manifestToAdd = pickupManifestItems.value.find(
            (item) => item.id === manifestId,
          );
          if (manifestToAdd) {
            // Clean description to remove the appended "(Total Qty: X)"
            const cleanedDescription = manifestToAdd.description
              .replace(/\(Total Qty:.*\)/, '')
              .trim();
            const newDeliveryManifest: Manifest = {
              ...manifestToAdd,
              description: cleanedDescription,
              quantity: 0, // Set the quantity based on user input
              volume: null,
              instructions: '',
            };
            editedManifest.value = newDeliveryManifest;
          }
        }
      });

      // Remove deselected manifests from the table
      oldIds.forEach((manifestId) => {
        if (!newIds.includes(manifestId)) {
          removeManifest(manifestId); // Remove manifest from the table
          editedManifest.value = null;
        }
      });
    }
  },
});

/**
 * Computed property to generate a list of manifest items from pickup PUDs (legTypeFlag === 'P').
 * appends the total pickup quantity to the description for display purposes.
 * @returns {Array<Object>} - An array of manifest items from pickup PUDs, each containing relevant details
 * including description with total quantity appended for display.
 */
const pickupManifestItems = computed(() => {
  return props.jobDetails.pudItems
    .filter((pud) => pud.legTypeFlag === 'P')
    .flatMap((pud) => pud.manifest)
    .map((item) => {
      // Fetch the total quantity for each manifest from the manifestMap
      const totalQty = manifestMap.value.get(item.id)?.totalPickupQuantity || 0;
      return {
        id: item.id,
        description: `${item.description} (Total Qty: ${totalQty})`,
        quantity: item.quantity,
        barcode: item.barcode,
        // Convert dimensions using the current lwhUnit:
        length: parseDimensionFromMetres(item.length, lwhUnit.value),
        width: parseDimensionFromMetres(item.width, lwhUnit.value),
        height: parseDimensionFromMetres(item.height, lwhUnit.value),
        // Convert weight using the current weightUnit:
        weight: parseWeightFromKilograms(item.weight, weightUnit.value),
      };
    });
});

// Expand Collapse Manifest Table
function setIsMinimised(minimised: boolean) {
  isMinimised.value = minimised;
}

/**
 * Sets the editedManifest to a new manifest item when the 'Add Manifest Item'
 * button is clicked. This is only called for pickups.
 */
function addManifest() {
  if (props.pudItem?.legTypeFlag === 'P') {
    const manifest: Manifest = {
      ...new Manifest(),
      id: uuidv4(),
    };
    editedManifest.value = manifest;
  }
}

/**
 * Saves the manifest item that's currently being edited. Emits the updated
 * manifest item to parent
 */
function saveManifest() {
  const manifest = editedManifest.value;
  if (manifest) {
    // manifest.quantity = inputQuantities[manifest.id] || manifest.quantity;
    if (
      props.pudItem?.legTypeFlag === 'P' &&
      manifest.description.trim() === ''
    ) {
      showNotification('Description is required');
      return; // Early exit if description is missing
    }
    if (manifest.quantity === null || manifest.quantity <= 0) {
      showNotification('Quantity must be greater than zero');
      return; // Early exit if quantity is not valid
    }

    // Convert dimensions to metres only if the selected unit is not metres
    if (lwhUnit.value === 'cm') {
      manifest.length = formatDimensionToMetres(manifest.length, 'cm');
      manifest.width = formatDimensionToMetres(manifest.width, 'cm');
      manifest.height = formatDimensionToMetres(manifest.height, 'cm');
    }

    // Convert weight to kilograms only if the selected unit is not kg
    if (weightUnit.value === 'g' || weightUnit.value === 't') {
      manifest.weight = formatWeightToKilograms(
        manifest.weight,
        weightUnit.value,
      );
    }

    // Emit the updated manifest item to the parent
    emit('addOrUpdateManifestItem', manifest);
    editedManifest.value = null;
  }
}

/**
 * Function to remove a manifest from the table and update selectedManifest
 * @param {string} manifestId - The ID of the manifest item to remove
 */
function removeManifest(manifestId: string) {
  emit('deleteManifestItem', manifestId);
}

/**
 * Map to track manifest items and their total quantities for pickup and drop.
 * Keys are manifest IDs, and values contain the total pickup and drop quantities.
 */
const manifestMap = ref(
  new Map<
    string,
    {
      description: string;
      totalPickupQuantity: number;
      totalDropQuantity: number;
    }
  >(),
);
props.jobDetails.pudItems.forEach((pud) => {
  pud.manifest.forEach((manifest) => {
    if (manifestMap.value.has(manifest.id)) {
      const existingManifest = manifestMap.value.get(manifest.id);
      if (existingManifest) {
        if (pud.legTypeFlag === 'P') {
          existingManifest.totalPickupQuantity += manifest.quantity || 0;
        } else if (pud.legTypeFlag === 'D') {
          existingManifest.totalDropQuantity += manifest.quantity || 0;
        }
      }
    } else {
      manifestMap.value.set(manifest.id, {
        description: manifest.description,
        totalPickupQuantity:
          pud.legTypeFlag === 'P' ? manifest.quantity || 0 : 0,
        totalDropQuantity: pud.legTypeFlag === 'D' ? manifest.quantity || 0 : 0,
      });
    }
  });
});

/**
 * Function to calculate the remaining quantity of a manifest item.
 * The remaining quantity is derived by subtracting the total drop quantity from the total pickup quantity
 * using data from the `manifestMap`.
 * @param {Manifest} item - The manifest item for which the remaining quantity is calculated.
 * @returns {number} - The remaining quantity of the manifest item. If no remaining quantity exists, returns 0.
 */
const remainingQuantity = computed(() => (item: Manifest) => {
  if (item) {
    // Get total pickup quantity from the manifestMap
    const totalPickupQuantity =
      manifestMap.value.get(item.id)?.totalPickupQuantity || 0;
    // Get total drop quantity, including input (item.quantity)
    const totalDropQuantity =
      manifestMap.value.get(item.id)?.totalDropQuantity || 0;
    // Calculate remaining quantity as total pickup quantity minus total drop quantity
    const remainingQty = totalPickupQuantity - totalDropQuantity;
    // Ensure remaining quantity is not negative
    return remainingQty >= 0 ? remainingQty : 0;
  }
  // Return 0 if no item is found
  return 0;
});

/**
 * Computed property to determine if a pickup manifest item is used in any drop
 */
const isPickupManifestInUse = (pickupId: string): boolean => {
  return props.jobDetails.pudItems.some(
    (pud) =>
      pud.legTypeFlag === 'D' &&
      pud.manifest.some((manifest) => manifest.id === pickupId),
  );
};

// copy manifest to edited manifest and covert unit to match selection
function editManifestItem(manifestItem: Manifest) {
  // Convert values to metres and kilograms for internal editing (backend storage)
  editedManifest.value = {
    ...manifestItem,
    length: parseDimensionFromMetres(manifestItem.length, lwhUnit.value), // convert to metres
    width: parseDimensionFromMetres(manifestItem.width, lwhUnit.value),
    height: parseDimensionFromMetres(manifestItem.height, lwhUnit.value),
    weight: parseWeightFromKilograms(manifestItem.weight, weightUnit.value), // convert to kilograms
  };
}

// Watch for changes to editedManifest and emit the event
watch(editedManifest, (newValue) => {
  emit('update-edited-manifest', newValue);
});
</script>

<style scoped lang="scss">
.manifest-table-container {
  margin-bottom: 8px;
  .table-container {
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    border-radius: 6px;
    margin-top: 8px;
  }
  .header-container {
    border-top: 2px double $border-color;
    padding-top: 10px;
    display: flex;
    flex-direction: row;
    align-items: end;
    justify-content: flex-start;
  }
  .simple-data-table {
    border-collapse: collapse;
    thead {
      th {
        background-color: var(--background-color-400);
        border-top: 1px solid var(--border-color);
        position: sticky;
        top: -1px;
        z-index: 1;
        padding: 4px 8px;
        text-align: center;
        font-weight: 500;
        font-size: $font-size-15;
        font-family: $font-sans;
        color: var(--light-text-color);

        @media (max-width: 1263px) {
          $primary-text-color-space: nowrap;
          font-size: $font-size-14;
        }
      }

      th:first-child {
        text-align: left;
      }
      th:last-child {
        text-align: right;
      }
    }
    tbody {
      background-color: var(--table-row);
      tr:nth-child(odd) {
        background-color: var(--table-row);
      }
      tr {
        &.section-start {
          border-top: 2px solid $border-color;
        }
        td {
          padding: 4px 8px;
          text-align: center;
          font-size: $font-size-15;
          font-family: $font-sans;
          font-weight: 500;
          @media (max-width: 1263px) {
            font-size: $font-size-12;
          }
        }

        td:first-child {
          text-align: left;
        }
        td:last-child {
          text-align: right;
        }
      }
    }
  }
  .manifest-btn {
    border-radius: 40px;
    color: white !important;
    &.add {
      position: relative;
      transform: translate(12px, 0px);
      border-radius: 14px;
      height: 32px;
      width: 180px;
      background-color: transparent !important;
      border: 1.5px solid var(--primary) !important;
      color: var(--primary) !important;
    }
    &.save {
      width: 38px;
      height: 30px;
      color: $success !important;
      background-color: $translucent-bg !important;
    }
    &.edit {
      width: 38px;
      height: 30px;
      color: $warning !important;
      background-color: $translucent-bg !important;
    }
    &.delete {
      width: 38px;
      height: 30px;
      color: $error !important;
      background-color: $translucent-bg !important;
    }
    &.cancel {
      width: 38px;
      height: 30px;
      color: var(--light-text-color) !important;
      background-color: $translucent-bg !important;
    }
  }
}

.qty-input {
  width: 100%;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  span {
    font-weight: 500;
    font-size: $font-size-16;
    color: var(--bg-light);
    width: 70%;
    display: inline-block;
  }
}

.view-more-btn {
  .v-btn {
    border-radius: 4px !important;
    height: 34px;
    color: var(--accent) !important;
    background-color: $translucent-bg !important;
    border-bottom: 1px solid var(--accent);
    &:hover {
      background-color: var(--background-color-300) !important;
    }
  }
}

.unit-selectors {
  display: flex;
  flex-direction: row;
  gap: 10px;
  margin-left: 18px;
  margin-bottom: 6px;
  color: var(--light-text-color);
  align-items: center; // Ensures labels and selects are aligned properly

  .unit-group {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  label {
    font-weight: 500;
    margin-top: 5px;
    color: var(--light-text-color);
    background: none;
    white-space: nowrap; // Prevents label text from wrapping
  }

  .select-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  select {
    width: fit-content;
    padding: 0 10px;
    height: 24px;
    border: 1px solid $translucent;
    border-radius: 10px;
    background: $app-dark-primary-600;
    transition: border-color 0.3s;

    &:focus {
      border-color: orange;
      outline: none;
    }

    &:disabled {
      background: lighten($app-dark-primary-300, 10%);
      border-color: lighten($border-color, 10%);
      color: darkgray;
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
}

// Responsive Styles
@media (max-width: 1500px) {
  .simple-data-table {
    thead {
      th {
        font-size: $font-size-11 !important;
      }
    }
    tbody {
      tr {
        td {
          font-size: $font-size-11 !important;
        }
      }
    }
  }
}

// Responsive Styles
@media (max-width: 1024px) {
  .simple-data-table {
    thead {
      th {
        font-size: $font-size-10 !important;
      }
    }
    tbody {
      tr {
        td {
          font-size: $font-size-10 !important;
        }
      }
    }
  }
}
</style>
