<template>
  <JobContentDialog
    :showDialog.sync="dialogController"
    :title="`Job Booking - ${isEditingExistingPud ? 'Edit Leg' : 'Add Leg'}`"
    width="90%"
    contentPadding="pa-2"
    @cancel="cancelChanges"
    :showActions="false"
    :isConfirmUnsaved="false"
    :isDisabled="editedPudItem?.status !== null || formDisabled"
    :isLoading="isLoading"
    confirmBtnText="confirm"
    :showActionButton="true"
    actionBtnText="Delete"
    :actionRequiresConfirmation="true"
    actionConfirmationMessage="Are you sure you wish to remove this stop?"
    :isContentDialog="true"
    class="v-dialog-custom"
    :jobDetails="jobDetails"
    :clientDetails="clientDetails"
    :driverId="jobDetails.driverId"
    :jobId="jobDetails.jobId"
    :pudId="selectedPudId"
  >
    <v-layout class="title-container" align-center justify-start md12>
      <span class="form-title-txt"
        >{{ isEditingExistingPud ? 'EDIT' : 'ADD' }}
        {{ props.pudItem?.legTypeFlag === 'P' ? 'PICKUP' : 'DELIVERY' }}
        <v-icon
          size="16"
          class="stop-icon"
          :color="props.pudItem?.legTypeFlag === 'P' ? '#55bf59' : '#f90070'"
          >fa fa-truck</v-icon
        >
      </span>
      <span>
        <v-tooltip bottom :disabled="!convertStopButtonConfig.tooltipText">
          <template v-slot:activator="{ on }">
            <span v-on="on">
              <v-btn
                v-if="convertStopButtonConfig.isVisible"
                depressed
                :class="[
                  props.pudItem?.legTypeFlag,
                  convertStopButtonConfig.isDisabled ? 'disabled' : '',
                ]"
                @click="handleLegChange()"
                :disabled="convertStopButtonConfig.isDisabled"
              >
                <v-icon size="20" class="mr-2">autorenew</v-icon> Convert to
                {{ props.pudItem?.legTypeFlag === 'P' ? 'DELIVERY' : 'PICKUP' }}
              </v-btn>
            </span>
          </template>
          {{ convertStopButtonConfig.tooltipText }}
        </v-tooltip>
        <v-tooltip bottom :disabled="!deleteStopButtonConfig.tooltipText">
          <template v-slot:activator="{ on }">
            <span v-on="on">
              <v-btn
                v-if="deleteStopButtonConfig.isVisible"
                depressed
                :disabled="deleteStopButtonConfig.isDisabled"
                class="delete-pud-btn"
                :class="deleteStopButtonConfig.isDisabled ? 'disabled' : ''"
                color="red"
                @click="openDialogForDelete()"
                >Delete Stop <v-icon size="20">delete_forever</v-icon>
              </v-btn>
            </span>
          </template>
          {{ deleteStopButtonConfig.tooltipText }}
        </v-tooltip>
      </span>
    </v-layout>
    <v-layout>
      <v-flex md12 class="body-scrollable--65 body-min-height--65 pa-4">
        <v-form ref="form" v-if="editedPudItem !== null">
          <v-layout wrap>
            <v-layout md12>
              <v-flex md5>
                <h6>
                  {{
                    props.pudItem?.legTypeFlag === 'P' ? 'PICKUP' : 'DELIVERY'
                  }}
                  Address Information
                </h6>
                <v-flex md12 mb-2 mt-2>
                  <v-combobox
                    label="Business / Site Name"
                    outline
                    class="v-solo-custom"
                    color="Orange"
                    flat
                    hint="Business / Site Name"
                    :items="filteredClientCommonAddressList"
                    :item-value="(item) => item._id"
                    :item-text="
                      (item) =>
                        `${item.addressNickname} - ${item.address.formattedAddress}`
                    "
                    clearable
                    :disabled="formDisabled"
                    hide-details
                    autofocus
                    auto-select-first
                    no-data-text=""
                    v-model="editedPudItem.customerDeliveryName"
                    :search-input.sync="searchInput"
                    persistent-hint
                    autocomplete="off"
                    browser-autocomplete="off"
                    @change="handleCommonAddressSelection"
                    @blur="handleSiteNameBlur"
                    @input="onClearSiteUpdate"
                  >
                  </v-combobox>
                  <v-layout
                    row
                    md12
                    mb-2
                    mt-2
                    @click="
                      (isNickNameSearch = false),
                        (selectCommonAddressDialog = true)
                    "
                  >
                    <span
                      v-if="
                        editedPudItem.customerDeliveryName &&
                        globalCommonAddressListByName &&
                        filteredGlobalCommonAddress.length > 0 &&
                        !globalCommonAddressListById?.some(
                          (item) => item._id === selectedGlobalAddressId,
                        )
                      "
                      class="global-common-address-search-txt"
                      >Did you Mean.....<span
                        v-if="!awaitingClientNames"
                        class="clickable-text"
                        >{{ globalCommonAddressListByName?.length }} Common
                        Address Found</span
                      >
                      <span v-else
                        ><v-progress-circular
                          color="accent"
                          indeterminate
                          :size="20"
                          :width="6"
                          class="pl-2"
                        ></v-progress-circular
                      ></span>
                    </span>
                  </v-layout>
                </v-flex>
                <v-flex md12 mb-3>
                  <AddressSearchAU
                    :hideHeadingRow="false"
                    :clientDetails="clientDetails"
                    :address="editedPudItem.address"
                    :nickNameList="clientCommonAddressList"
                    :commonAddressNickname="editedPudItem.customerDeliveryName"
                    label="Full Address"
                    :setFocus="false"
                    :enablePinDrop="!sessionManager.isClientPortal()"
                    :formDisabled="
                      rateTypeId === 4 ||
                      formDisabled ||
                      selectedAddress?.addressNickname ===
                        editedPudItem.customerDeliveryName
                    "
                    :switchToSearch="switchAddressToSearchEnabled"
                    :enableNicknamedAddress="false"
                    :enableAddNickNameAddress="true"
                    :enableSuburbSelect="!sessionManager.isClientPortal()"
                    :soloInput="true"
                    :numberOfExistingStops="jobDetails.pudItems.length"
                    :isClientPortal="sessionManager.isClientPortal()"
                    :enableReturnToDefaultDispatchAddress="true"
                    @nicknameAddressSelected="nicknameAddressSelected"
                    @addReturnToFirstPud="addReturnToFirstPud"
                    @addPreloadLeg="addPreloadLeg"
                    @addressSelected="handleAddressSelected"
                    @addReturnToDefaultDispatchLocation="
                      addReturnToDefaultDispatchLocation
                    "
                    :addressIsRequired="true"
                    :enableAddPreload="editedPudItem.legTypeFlag === 'D'"
                  >
                  </AddressSearchAU>
                  <v-layout
                    row
                    md12
                    mb-2
                    mt-2
                    @click="
                      (isNickNameSearch = true),
                        (selectCommonAddressDialog = true)
                    "
                  >
                    <span
                      v-if="
                        editedPudItem.address &&
                        globalCommonAddressListById &&
                        !globalCommonAddressListByName?.some(
                          (item) => item._id === selectedGlobalAddressId,
                        )
                      "
                      class="global-common-address-search-txt"
                      >Did you Mean.....<span
                        v-if="!awaitingClientNames"
                        class="clickable-text"
                        >{{ globalCommonAddressListById?.length }} Common
                        Address Found</span
                      >
                      <span v-else>
                        <v-progress-circular
                          color="accent"
                          indeterminate
                          :size="20"
                          :width="6"
                          class="pl-2"
                        ></v-progress-circular>
                      </span>
                    </span>
                  </v-layout>
                  <v-flex md12 mb-2>
                    <PudRelatedContactSelect
                      ref="pudSiteContactRef"
                      :clientId="
                        isCashSaleClient ? 'CS' : clientDetails?.clientId
                      "
                      :client_id="isCashSaleClient ? 'CS' : clientDetails?._id"
                      :commonAddress="selectedClientCommonAddress ?? null"
                      @addRelatedContactToLeg="
                        addRelatedContactToLeg(editedPudItem, $event)
                      "
                      :contactName="editedPudItem.siteContactName"
                      :contactMobileNumber="
                        editedPudItem.siteContactMobileNumber
                      "
                      :contactLandlineNumber="
                        editedPudItem.siteContactLandLineNumber
                      "
                      :hint="
                        editedPudItem.siteContactMobileNumber
                          ? `A message will be sent to ${editedPudItem.siteContactMobileNumber} when the driver is on the way.`
                          : undefined
                      "
                      :formDisabled="formDisabled"
                      :legType="props.pudItem?.legTypeFlag"
                      :clientName="clientDetails?.displayName"
                      :clientPersonIds="clientDetails?.clientPersonDispatchers"
                      :defaultDispatcherId="clientDetails?.defaultDispatcherId"
                    />
                  </v-flex>
                </v-flex>
              </v-flex>
              <v-divider class="vertical-divider" inset vertical></v-divider>
              <v-flex md4>
                <v-flex
                  ><h6>
                    {{
                      editedPudItem.legTypeFlag === 'P' ? 'Pickup' : 'Delivery'
                    }}
                    References ({{
                      editedPudItem.legTypeFlag === 'P'
                        ? editedPudItem.pickupReference.length
                        : editedPudItem.dropoffReference.length
                    }})
                  </h6>
                </v-flex>
                <v-flex md12 mb-2 mt-2>
                  <BookingReferences
                    :requirementsMet.sync="pudReferenceRequirementsMet"
                    :isPud="true"
                    :soloInput="true"
                    :isDropoff="editedPudItem.legTypeFlag === 'D'"
                    :jobReferences="
                      editedPudItem.legTypeFlag === 'P'
                        ? editedPudItem.pickupReference
                        : editedPudItem.dropoffReference
                    "
                    :clientReferences="
                      clientDetails ? clientDetails.references.pudScreen : []
                    "
                    :readOnlyView="formDisabled"
                  />
                </v-flex>
              </v-flex>
              <v-divider class="vertical-divider" inset vertical></v-divider>
              <v-flex md3>
                <v-flex
                  ><h6>Notes ({{ editedPudItem.notes.length }})</h6>
                </v-flex>
                <v-flex
                  md12
                  justify-center
                  class="notes-container custom-scrollbar"
                >
                  <v-layout row wrap>
                    <v-flex md12 mb-2>
                      <NotesList
                        v-if="editedPudItem.notes.length > 0"
                        :isBookingScreen="false"
                        :allowDelete="!editedPudItem.pudId"
                        :communications="editedPudItem.notes"
                        :allowEdit="true"
                        :showVisibilityTypeName="true"
                        :showAddToJobType="true"
                        @removeNote="handleRemoveNote"
                        :detailedView="true"
                        @editNote="handleEditNote($event)"
                      >
                      </NotesList>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-btn
                  block
                  depressed
                  plain
                  large
                  @click="showNotesEdit = !showNotesEdit"
                  class="notes-btn"
                >
                  <v-icon class="pl-1 pr-2" size="16">note_add</v-icon>
                  Add Notes
                </v-btn>
              </v-flex>
            </v-layout>
            <v-flex md12 class="manifest-table">
              <JobManifestTable
                :manifestList="editedPudItem.manifest"
                :formDisabled="formDisabled"
                :jobDetails="jobDetails"
                :pudItem="editedPudItem"
                @addOrUpdateManifestItem="addOrUpdateManifestItem"
                @deleteManifestItem="deleteManifestItem"
                @update-edited-manifest="handleEditedManifestUpdate"
              ></JobManifestTable>
              <v-divider v-if="editedPudItem.manifest.length < 0"></v-divider>
            </v-flex>
          </v-layout>
          <v-divider></v-divider>
          <v-layout justify-space-between align-center>
            <v-layout md12>
              <v-flex md6 class="time-container">
                <LegTimeDefinitionSelector
                  :currentPudItem="editedPudItem"
                  :clientDetails="clientDetails"
                  :editingExistingPUDItem="isEditingExistingPud"
                  :editingPudIndex="indexOfEditedPud"
                  :pudItems="jobDetails.pudItems"
                  :isRecurringJob="false"
                  :soloInput="false"
                  :formDisabled="editedPudItem.status !== null || formDisabled"
                  :allowAsapTimeDefinition="
                    recurrenceType !== JobRecurrenceType.PERMANENT
                  "
                  :initWithEpochTime="jobDate"
                  @update:pudTime="updateCurrentPudItemPudTime"
                  @update:pudDate="setSelectedJobDate"
                ></LegTimeDefinitionSelector>
              </v-flex>
              <v-divider class="mx-3" inset vertical></v-divider>
              <v-flex md6 mt-4 class="payload-header">
                <h6
                  :class="[{ 'form-field-required-marker': weightIsRequired }]"
                >
                  Overall Payload Dimensions
                </h6>
                <v-layout wrap mt-2 class="payload-inputs">
                  <v-flex md3 pr-2>
                    <v-text-field
                      :label="`Length (${lwhUnit})`"
                      v-model.number="lengthInput"
                      :rules="[numberRule]"
                      type="number"
                      min="0"
                      flat
                      outline
                      :disabled="formDisabled"
                      class="v-solo-custom"
                      persistent-hint
                    >
                    </v-text-field>
                  </v-flex>
                  <v-flex md3 pr-2>
                    <v-text-field
                      :label="`Width (${lwhUnit})`"
                      v-model.number="widthInput"
                      :rules="[numberRule]"
                      type="number"
                      flat
                      min="0"
                      outline
                      class="v-solo-custom"
                      :disabled="formDisabled"
                      persistent-hint
                    >
                    </v-text-field>
                  </v-flex>
                  <v-flex md3 pr-2>
                    <v-text-field
                      :label="`Height (${lwhUnit})`"
                      v-model.number="heightInput"
                      :rules="[numberRule]"
                      type="number"
                      min="0"
                      flat
                      outline
                      :disabled="formDisabled"
                      class="v-solo-custom"
                      persistent-hint
                    >
                    </v-text-field>
                  </v-flex>
                  <v-flex md3>
                    <v-text-field
                      :label="`Weight (${weightUnit})`"
                      type="number"
                      v-model.number="weightInput"
                      flat
                      min="0"
                      @input="weightChanged"
                      :class="weightIsRequired ? 'form-field-required' : ''"
                      :rules="
                        weightIsRequired
                          ? [validationRules.required, numberRule]
                          : [numberRule]
                      "
                      :disabled="formDisabled"
                      class="v-solo-custom"
                      outline
                      persistent-hint
                    >
                    </v-text-field>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-layout>

          <ContentDialog
            :showDialog.sync="showNotesEdit"
            title="Add Note"
            width="40%"
            @cancel="showNotesEdit = false"
            :showActions="false"
          >
            <NotesEditor
              v-if="showNotesEdit"
              :communications="editedPudItem.notes"
              :isEdited="!formDisabled"
              :isAddingNote="showNotesEdit"
              @setIsAddingNote="showNotesEdit = $event"
              :type="3"
              :enableCommunicationTypeSelect="!sessionManager.isClientPortal()"
              :enableVisibilitySelect="!sessionManager.isClientPortal()"
              :isClientVisibilityOnly="sessionManager.isClientPortal()"
              :jobNoteLevel="JobNoteLevel.PUD_ITEM"
            >
            </NotesEditor>
          </ContentDialog>

          <ContentDialog
            :showDialog.sync="isEditingExistingNote"
            title="Edit Note"
            width="40%"
            @cancel="isEditingExistingNote = false"
            :showActions="true"
            @confirm="saveEditedNote"
          >
            <v-flex md12 v-if="isEditingExistingNote">
              <v-layout row wrap v-if="currentlyEditingNote.type !== undefined">
                <v-flex md12 px-3>
                  <v-layout py-1>
                    <v-flex>
                      <v-select
                        label="Visible To"
                        outline
                        class="v-solo-custom"
                        hide-details
                        :disabled="false"
                        multiple
                        :items="communicationVisibility"
                        item-text="longName"
                        item-value="id"
                        color="orange"
                        :rules="[validationRules.listRequired]"
                        v-model="currentlyEditingNote.visibleTo"
                      >
                      </v-select>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12 px-3>
                  <v-textarea
                    v-model="currentlyEditingNote.body"
                    placeholder="Note contents"
                    hide-details
                    color="orange"
                    class="v-solo-custom"
                    outline
                    :disabled="true"
                  ></v-textarea>
                </v-flex>
              </v-layout>
            </v-flex>
          </ContentDialog>
        </v-form>
      </v-flex>
    </v-layout>
    <v-divider></v-divider>
    <v-layout v-if="isDialogOpen" md12 class="btn-container">
      <v-btn
        class="action-btn cancel"
        solo
        outline
        justify-start
        color="error"
        @click="cancelChanges"
        >Cancel</v-btn
      >
      <v-spacer></v-spacer>
      <v-btn
        solo
        outline
        block
        :class="editedPudItem?.legTypeFlag"
        class="action-btn drop"
        :loading="isLoading"
        @click="savePudChangeLeg('D')"
        v-if="!isEditingExistingPud"
        :disabled="isEditingManifest || isPreloadDropoff"
      >
        Save & Add New Delivery
      </v-btn>
      <v-btn
        justify-end
        block
        class="action-btn confirm"
        :loading="isLoading"
        @click="isEditingManifest ? openDialogForSave() : savePudItem()"
      >
        {{
          isEditingExistingPud
            ? 'Update Leg'
            : 'Save This ' +
              (editedPudItem?.legTypeFlag === 'P' ? 'Pickup' : 'Delivery')
        }}
      </v-btn>
    </v-layout>
    <!-- Change Leg pickup/drop Confirmation Dialog -->
    <!-- only show if manifest items -->
    <v-dialog
      v-model="confirmationDialogActive"
      width="600px"
      class="pa-2"
      persistent
      no-click-animation
      :content-class="
        sessionManager.isClientPortal()
          ? 'v-dialog-custom client-portal'
          : 'v-dialog-custom'
      "
    >
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        Confirm Stop {{ dialogAction }}
      </v-layout>
      <v-layout row wrap md12 class="app-theme__center-content--body">
        <v-flex md12 class="dialog-content">
          <v-alert
            :type="dialogAction === 'delete' ? 'error' : 'warning'"
            value="true"
            icon="fas fa-exclamation-circle"
          >
            <!-- Change content based on action -->
            <span v-if="dialogAction === 'change'">
              Converting Stop to
              {{ props.pudItem?.legTypeFlag === 'P' ? 'DELIVERY' : 'PICKUP' }}
              will remove {{ editedPudItem?.manifest.length }} Manifest Item(s)
            </span>
            <span v-else-if="dialogAction === 'delete'">
              Are you sure you want to delete this
              {{ props.pudItem?.legTypeFlag === 'P' ? 'PICKUP' : 'DELIVERY' }} ?
            </span>
            <span v-else-if="dialogAction === 'save'">
              You are trying to save this stop with unsaved changes to manifest
              items, do you wish to continue?
            </span>
          </v-alert>
        </v-flex>

        <v-divider></v-divider>
        <v-flex md12 justify-evenly align-center class="dialog-btn-container">
          <v-btn
            justify-start
            outline
            small
            solo
            @click="confirmationDialogActive = false"
            class="v-btn-custom"
          >
            Go Back
          </v-btn>
          <v-btn
            justify-end
            solo
            small
            depressed
            color="primary"
            class="v-btn-custom"
            @click="confirmAction"
          >
            Confirm
          </v-btn>
        </v-flex>
      </v-layout>
    </v-dialog>
    <ContentDialog
      :showDialog.sync="selectCommonAddressDialog"
      title="Select Common Address from global common address search"
      width="60%"
      @cancel="selectCommonAddressDialog = false"
      :showActions="false"
    >
      <v-layout md12 class="body-scrollable--70 body-min-height--65">
        <v-data-table
          :headers="headers"
          :items="filteredGlobalCommonAddress"
          class="common-address-table"
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="tableProps">
            <tr
              :class="
                selectedGlobalAddressId === tableProps.item._id
                  ? 'selected'
                  : ''
              "
              @click="
                selectCommonGlobalAddress(
                  filteredGlobalCommonAddress,
                  tableProps.item._id,
                )
              "
            >
              <td>
                {{ tableProps.item.clientId }} -
                {{ clientNames[tableProps.item.clientId] }}
              </td>
              <td>
                {{
                  tableProps.item.addressNickname
                    ? tableProps.item.addressNickname.toUpperCase()
                    : ''
                }}
              </td>
              <td>
                {{ tableProps.item.address.formattedAddress }}
              </td>
              <v-icon
                v-if="selectedGlobalAddressId === tableProps.item._id"
                class="check-icon"
                >check_circle</v-icon
              >
            </tr>
          </template>
        </v-data-table>
      </v-layout>
      <v-divider></v-divider>
      <v-layout wrap mt-2 mb-2 pl-2 pr-2>
        <v-btn
          solo
          outline
          class="action-btn"
          color="error"
          @click="selectCommonAddressDialog = false"
          >Cancel</v-btn
        >
      </v-layout>
    </ContentDialog>
    <v-flex v-if="!!selectedAddress?.notes?.length">
      <JobBookingCommonAddressNotesAttachmentDialog
        :key="selectedAddress?.address.addressId"
        :commonAddress="selectedAddress ?? null"
        @addNotesFromSelection="addNotesFromDialog"
      ></JobBookingCommonAddressNotesAttachmentDialog>
    </v-flex>
  </JobContentDialog>
</template>

<script setup lang="ts">
export interface SavePudPayload {
  pudItem: PUDItem;
  plannedRoute?: ORSRoute;
  newLegTypeFlag?: 'P' | 'D';
}

export interface ButtonConfig {
  isVisible: boolean;
  isDisabled: boolean;
  tooltipText: string;
}
import JobBookingCommonAddressNotesAttachmentDialog from '@/components/booking/delivery_details/job_booking_commonAddress_notes_attachment_dialog.vue';
import JobManifestTable from '@/components/booking/delivery_details/job_manifest_table.vue';
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import NotesEditor from '@/components/common/notes_editor/notes_editor.vue';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import JobContentDialog from '@/components/common/ui-elements/job_content_dialog.vue';
import BookingReferences from '@/components/operations/BookJob/booking_references/index.vue';
import LegTimeDefinitionSelector from '@/components/operations/BookJob/leg_time_definition_selector.vue';
import PudRelatedContactSelect from '@/components/operations/BookJob/pud_related_contact_select/pud_related_contact_select.vue';
import { initialisePudItem } from '@/helpers/classInitialisers/InitialisePudItem';
import { updateClientNoteBeforeAddingToJob } from '@/helpers/CommunicationHelpers/ClientNoteHelpers';
import {
  returnEarliestBookingTime,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  formatDimensionToMetres,
  formatWeightToKilograms,
  parseDimensionFromMetres,
  parseWeightFromKilograms,
} from '@/helpers/DimensionsHelpers/DimensionsHelpers';
import { routingPossibleForCountryName } from '@/helpers/DistanceHelpers/DistanceHelpers';
import { getClientCommonAddressForSelectedAddress } from '@/helpers/JobBooking/JobBookingAddressHelpers';
import { addRelatedContactToLeg } from '@/helpers/JobBooking/JobBookingContactHelpers';
import {
  DEFAULT_PRELOAD_STOP_LOAD_TIME,
  referenceListContainsPreloadReference,
  returnPreloadReference,
} from '@/helpers/JobBooking/JobBookingPreloadHelpers';
import { copyLocationPropertiesToPudItem } from '@/helpers/JobBooking/JobBookingPudHelpers';
import { routeIsValidForJobLength } from '@/helpers/JobBooking/JobBookingRouteHelpers';
import { applyNicknameAddressValuesToPudItem } from '@/helpers/JobBooking/JobCommonAddressHelpers';
import { findAndRemovePudReferencesFromList } from '@/helpers/JobBooking/JobReferenceHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import type { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import type ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import AddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import Communication from '@/interface-models/Generic/Communication/Communication';
import {
  AddToJobType,
  ClientInstructions,
} from '@/interface-models/Generic/Communication/CommunicationTypes/ClientInstructions';
import { communicationVisibility } from '@/interface-models/Generic/Communication/CommunicationVisibility';
import { JobNoteLevel } from '@/interface-models/Generic/Communication/JobNoteLevel';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import ORSRoute from '@/interface-models/Generic/Route/ORSRoute';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { JobRecurrenceType } from '@/interface-models/Jobs/JobRecurrenceType';
import { Manifest } from '@/interface-models/Jobs/Manifest';
import { PUDItem } from '@/interface-models/Jobs/PUD/PUDItem';
import {
  ErrorMessage,
  errorMessageTypes,
} from '@/interface-models/Jobs/PUD/PudValidationError';
import type UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { sessionManager } from '@/store/session/SessionState';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  reactive,
  ref,
  toRef,
  watch,
} from 'vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'deletePudAtIndex', payload: number): void;
  (event: 'savePudItem', payload: SavePudPayload): void;
}>();

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    recurrenceType: JobRecurrenceType;
    pudItem: PUDItem | null;
    clientDetails: ClientDetails;
    isDialogOpen: boolean;
    formDisabled: boolean;
    unassignedPudListForClient?: UnassignedPudItem[] | null;
    jobDate: number;
  }>(),
  {
    pudItem: null,
    isDialogOpen: false,
    unassignedPudListForClient: null,
    jobDate: 0,
  },
);

const clientDetailsStore = useClientDetailsStore();
const fleetAssetStore = useFleetAssetStore();
const pudSiteContactRef: Ref<any> = ref(null);
const confirmationDialogActive: Ref<boolean> = ref(false);
const awaitingClientNames: Ref<boolean> = ref(false);
const form: Ref<any> = ref(null);
const jobDate: Ref<number> = ref(props.jobDate);
const editedPudItem: Ref<PUDItem | null> = ref(null);
const pudReferenceRequirementsMet: Ref<boolean> = ref(false);
const showNotesEdit: Ref<boolean> = ref(false);
const errorMessages: Ref<ErrorMessage[]> = ref([]);
const isLoading: Ref<boolean> = ref(false);
const dialogAction = ref<'change' | 'delete' | 'save'>('change');
const isEditingManifest: Ref<boolean> = ref(false);
const selectCommonAddressDialog: Ref<boolean> = ref(false);
const globalCommonAddressListByName: Ref<ClientCommonAddress[] | null> =
  ref(null);
const globalCommonAddressListById: Ref<ClientCommonAddress[] | null> =
  ref(null);
const isNickNameSearch: Ref<boolean> = ref(false);
const selectedGlobalAddressId: Ref<string> = ref('');
const searchInput = ref<string>('');

const isEditingExistingNote: Ref<boolean> = ref(false);
const currentlyEditingNote: Ref<Communication> = ref(new Communication());

// store client name with client ids
const clientNames = reactive<Record<string, string>>({});

// manifest table unit selections
const lwhUnit = toRef(fleetAssetStore, 'lwhUnit');
const weightUnit = toRef(fleetAssetStore, 'weightUnit');

// to switch address inputs back to address search
const switchAddressToSearch = ref(false);

// inputs for overall payload dimensions
const lengthInput: Ref<number | null> = ref(null);
const widthInput: Ref<number | null> = ref(null);
const heightInput: Ref<number | null> = ref(null);
const weightInput: Ref<number | null> = ref(null);

const selectedAddress: Ref<ClientCommonAddress | undefined> = ref(undefined);

const numberRule = (val: any) => {
  // Allow empty values (null, undefined, empty string)
  if (val === null || val === '') {
    return true;
  }
  // Ensure the value is a valid number (not a string with letters or spaces)
  if (isNaN(val) || (typeof val === 'string' && val.trim() === '')) {
    return 'Please enter a valid number';
  }
  // Ensure the number is positive or zero
  return Number(val) < 0 ? 'Please enter a positive number' : true;
};

// Expose editedPudItem so we can push changes to it when a websocket update is received
defineExpose({
  editedPudItem,
});

const headers = [
  { text: 'Client', value: 'client' },
  { text: 'Site Name', value: 'name' },
  { text: 'Address', value: 'address' },
];

// filters common address list to match input for combobox
const filteredClientCommonAddressList: ComputedRef<ClientCommonAddress[]> =
  computed<ClientCommonAddress[]>(() => {
    const filteredList = clientCommonAddressList.value.filter(
      (item) =>
        item.address.formattedAddress
          .toLowerCase()
          .includes(searchInput.value ? searchInput.value.toLowerCase() : '') ||
        item.addressNickname
          .toLowerCase()
          .includes(searchInput.value ? searchInput.value.toLowerCase() : ''),
    );

    // Sort the filtered list alphabetically by addressNickname
    filteredList.sort((a, b) =>
      a.addressNickname.localeCompare(b.addressNickname),
    );

    return filteredList;
  });

// filters global common address list for select common address table dialog
const filteredGlobalCommonAddress: ComputedRef<ClientCommonAddress[]> =
  computed(() => {
    const sourceList = isNickNameSearch.value
      ? globalCommonAddressListById.value
      : globalCommonAddressListByName.value;

    if (!sourceList || !sourceList.length) {
      return [];
    }

    // Create a Set of client common address IDs for quick lookup
    const clientAddressIds = new Set(
      clientCommonAddressList.value.map((item) => item._id),
    );

    const uniqueAddresses = new Map();
    return sourceList.filter((item) => {
      if (!uniqueAddresses.has(item._id) && !clientAddressIds.has(item._id)) {
        uniqueAddresses.set(item._id, true);
        return true;
      }
      return false;
    });
  });

// func to select common address from global common address dialog
function selectCommonGlobalAddress(
  globalCommonAddressListByName: ClientCommonAddress[],
  id: string,
) {
  // Find the address from globalCommonAddressListByName by id
  selectedAddress.value = globalCommonAddressListByName.find(
    (item) => item._id === id,
  );
  selectedGlobalAddressId.value = id;

  // If an address is found, assign it to editedPudItem.address
  if (selectedAddress.value && editedPudItem.value) {
    editedPudItem.value.address = deepCopy(selectedAddress.value.address);
    editedPudItem.value.customerDeliveryName =
      selectedAddress.value.addressNickname;
    searchInput.value = selectedAddress.value.addressNickname;
    applyNicknameAddressValuesToPudItem(
      editedPudItem.value,
      selectedAddress.value,
    );
    switchAddressToSearch.value = true;
    selectCommonAddressDialog.value = false;

    // set selected common address notes to prompt
    if (selectedAddress.value.notes && selectedAddress.value.notes.length > 0) {
      selectedAddress.value.notes.forEach((note) => {
        if (
          note.type &&
          (note.type.communicationDetails as ClientInstructions)
            .addToJobType !== AddToJobType.NEVER
        ) {
          (note.type.communicationDetails as ClientInstructions).addToJobType =
            AddToJobType.PROMPT;
        }
      });
    }
  }
  return;
}

// Handle common address selection from the dropdown
function handleCommonAddressSelection(
  selectedCommonAddress: ClientCommonAddress,
) {
  if (
    selectedCommonAddress &&
    searchInput.value !== editedPudItem.value?.customerDeliveryName
  ) {
    // Find the full address object using the selected ID
    selectedAddress.value = clientCommonAddressList.value.find(
      (item) => item._id === selectedCommonAddress._id,
    );
    if (selectedAddress.value && editedPudItem.value) {
      // Update editedPudItem.address with the selected address
      editedPudItem.value.address = deepCopy(selectedAddress.value.address);
      editedPudItem.value.customerDeliveryName =
        selectedAddress.value.addressNickname;
      searchInput.value = selectedAddress.value.addressNickname;
      globalCommonAddressListByName.value = [];
      applyNicknameAddressValuesToPudItem(
        editedPudItem.value,
        selectedAddress.value,
      );
      switchAddressToSearch.value = true;

      // Update site contact if available
      if (selectedAddress.value.defaultContact?.contactId) {
        pudSiteContactRef.value?.commonAddressSelected(
          selectedAddress.value.defaultContact.contactId,
        );
      }
    }
  }
}

// Handle site/business name blur event (search global common address from nickname)
async function handleSiteNameBlur() {
  const enteredText = editedPudItem.value?.customerDeliveryName;
  switchAddressToSearch.value = false;

  if (enteredText && enteredText?.length > 3) {
    // Check if the input text matches any item in the list
    const exists = clientCommonAddressList.value.some(
      (item) =>
        item.addressNickname.trim().toLowerCase() ===
        enteredText.trim().toLowerCase(),
    );

    if (!exists && !sessionManager.isClientPortal()) {
      try {
        // Send API request with the input text
        const result =
          await clientDetailsStore.requestClientCommonAddressByNickname(
            enteredText,
          );

        globalCommonAddressListByName.value = Array.isArray(result)
          ? result
          : [];

        selectedGlobalAddressId.value = '';
        fetchClientNames();
      } catch (error) {
        globalCommonAddressListByName.value = [];
      }
    }
  }
}

// Handle select address blur event (search global common address from address id)
async function handleAddressSelected(id: string) {
  // Find the matching address in clientCommonAddressList
  const existingAddress = clientCommonAddressList.value.find(
    (item) => item.address.addressId === id,
  );
  if (!existingAddress && !sessionManager.isClientPortal()) {
    try {
      // Fetch address data from API since it wasn't found in clientCommonAddressList
      const result =
        await clientDetailsStore.requestClientCommonAddressByAddressId(id);

      globalCommonAddressListById.value = Array.isArray(result) ? result : [];
      selectedGlobalAddressId.value = '';
      fetchClientNames();
    } catch (error) {
      globalCommonAddressListById.value = [];
    }
  } else {
    if (editedPudItem.value && existingAddress) {
      editedPudItem.value.customerDeliveryName =
        existingAddress.addressNickname;
      searchInput.value = existingAddress.addressNickname;
      applyNicknameAddressValuesToPudItem(editedPudItem.value, existingAddress);
      // Set selectedAddress for common address notes
      selectedAddress.value = existingAddress;
    }
    // Set the site contact in the pud_related_contact component via ref.
    if (existingAddress?.defaultContact?.contactId) {
      pudSiteContactRef.value.commonAddressSelected(
        existingAddress.defaultContact.contactId,
      );
    }
  }
}

// Methods to open the dialog with the respective action
function openDialogForChange() {
  dialogAction.value = 'change';
  confirmationDialogActive.value = true;
}
function openDialogForDelete() {
  dialogAction.value = 'delete';
  confirmationDialogActive.value = true;
}
function openDialogForSave() {
  dialogAction.value = 'save';
  confirmationDialogActive.value = true;
}
// Confirm action based on dialogAction
function confirmAction() {
  if (dialogAction.value === 'change') {
    changeLegType();
  } else if (dialogAction.value === 'delete') {
    deletePudItem();
  } else if (dialogAction.value === 'save') {
    savePudItem();
  }
  confirmationDialogActive.value = false;
}

/**
 * Handles emit from JobManifestTable component, to add or update a manifest item
 * @param manifest - The manifest item to add or update
 */
function addOrUpdateManifestItem(manifest: Manifest) {
  if (!editedPudItem.value || !manifest.id) {
    return;
  }
  const index = editedPudItem.value.manifest.findIndex(
    (item) => item.id === manifest.id,
  );
  if (index === -1) {
    editedPudItem.value.manifest.push(manifest);
  } else {
    editedPudItem.value.manifest.splice(index, 1, manifest);
  }
}

/**
 * Handles emit from JobManifestTable component, to delete a manifest item
 * @param id - The ID of the manifest item to delete
 */
function deleteManifestItem(id: string) {
  if (!editedPudItem.value) {
    return;
  }
  const index = editedPudItem.value.manifest.findIndex(
    (item) => item.id === id,
  );
  if (index !== -1) {
    editedPudItem.value.manifest.splice(index, 1);
  }
}

const rateTypeId: ComputedRef<number> = computed(() => {
  return props.jobDetails.serviceTypeObject.rateTypeId;
});

/**
 * Returns a boolean indicating whether the last leg of the job is a preload.
 * Used to disable the add pickup/drop buttons.
 */
const isPreloadDropoff: ComputedRef<boolean> = computed(() => {
  return (
    editedPudItem.value?.legTypeFlag === 'D' &&
    referenceListContainsPreloadReference(editedPudItem.value.dropoffReference)
  );
});

/**
 * Returns a ButtonConfig object which is used in the template to determine the
 * visibility and disabled state of the delete stop button, as well as whether
 * or not we show a tooltip on hover.
 */
const deleteStopButtonConfig: ComputedRef<ButtonConfig> = computed(() => {
  // If we're not editing an existing pudItem, don't show the delete button
  if (!isEditingExistingPud.value) {
    return {
      isVisible: false,
      isDisabled: false,
      tooltipText: '',
    };
  }
  // If the pudItem is in-progress or completed, don't allow the user to delete it
  if (props.pudItem?.status !== null) {
    return {
      isVisible: true,
      isDisabled: true,
      tooltipText: 'This stop is in-progress or completed.',
    };
  }
  // If this is the first pudItem in the list, and there is more than 1 pudItem
  // in the list, don't allow the first one to be deleted
  if (indexOfEditedPud.value === 0 && props.jobDetails.pudItems.length > 1) {
    return {
      isVisible: true,
      isDisabled: true,
      tooltipText: 'The first pickup of a job cannot be deleted.',
    };
  }
  return {
    isVisible: true,
    isDisabled: false,
    tooltipText: '',
  };
});

/**
 * Returns a ButtonConfig object which is used in the template to determine the
 * visibility and disabled state of the convert stop button (convert to pickup,
 * convert to dropoff), as well as whether or not we show a tooltip on hover.
 */
const convertStopButtonConfig: ComputedRef<ButtonConfig> = computed(() => {
  if (props.jobDetails.pudItems.length === 0 || isPreloadDropoff.value) {
    return { isVisible: false, isDisabled: false, tooltipText: '' };
  }
  // The first pud should always be a pickup. If we're editing the first pud,
  // don't allow the user to convert it to a delivery
  if (isEditingExistingPud.value && indexOfEditedPud.value === 0) {
    return {
      isVisible: true,
      isDisabled: true,
      tooltipText: 'The first stop of a job must be a pickup.',
    };
  }
  if (
    props.jobDetails.rateTypeId === JobRateType.ZONE_TO_ZONE &&
    editedPudItem.value?.legTypeFlag === 'D' &&
    props.jobDetails.pudItems.filter((p) => p.legTypeFlag === 'P').length >= 1
  ) {
    return {
      isVisible: true,
      isDisabled: true,
      tooltipText: 'Only one pickup is allowed for a zone-to-zone job.',
    };
  }
  return { isVisible: true, isDisabled: false, tooltipText: '' };
});

const formDisabled: ComputedRef<boolean> = computed(() => {
  return props.formDisabled || isPreloadDropoff.value;
});

// computed property to track switchAddressToSearch(prop in addressAu) to switch back to address search when common address is selected
const switchAddressToSearchEnabled: ComputedRef<boolean> = computed(() => {
  return switchAddressToSearch.value;
});

/**
 * Returns true if pudId is set, indicating that this is an existing PUD item
 */
const isEditingExistingPud: ComputedRef<boolean> = computed(() => {
  if (editedPudItem.value?.pudId) {
    return true;
  } else {
    // Check if the current `uniqueId` exists in the `pudItems` array in `props.jobDetails`
    return props.jobDetails.pudItems.some(
      (pudItem) => pudItem.uniqueId === editedPudItem.value?.uniqueId,
    );
  }
});

/**
 * Returns the index of the edited PUD item in the jobDetails.pudItems array
 */
const indexOfEditedPud: ComputedRef<number> = computed(() => {
  if (!editedPudItem.value) {
    return -1;
  }
  return props.jobDetails.pudItems.findIndex((pudItem) => {
    if (editedPudItem.value?.pudId) {
      // Use pudId if the PUD item is already saved
      return pudItem.pudId === editedPudItem.value.pudId;
    } else {
      // Else use the frontend-only field uniqueId
      return pudItem.uniqueId === editedPudItem.value?.uniqueId;
    }
  });
});

/**
 * Returns true if the weight information is required for the current PUD item,
 * based on the client requirements and the position of the PUD item in the job.
 */
const weightIsRequired = computed(() => {
  const firstPickupIndex = props.jobDetails.pudItems.findIndex(
    (pud: PUDItem) => pud.legTypeFlag === 'P',
  );
  const isFirstPickup: boolean = indexOfEditedPud.value === firstPickupIndex;
  return (
    editedPudItem.value?.weightIsRequired(
      props.clientDetails.weightRequirement,
      isFirstPickup,
    ) || false
  );
});

/**
 * If job is unit rate with weight as the unit type we update the weight
 * property on the pud item to be equal to the unit amount to be picked up.
 */
function weightChanged(value: number) {
  if (
    rateTypeId.value !== 5 ||
    !editedPudItem.value ||
    editedPudItem.value.rateDetails.unitTypeId !== 2
  ) {
    return;
  }
  if (editedPudItem.value.legTypeFlag === 'P') {
    editedPudItem.value.rateDetails.unitPickUps = value;
  } else {
    editedPudItem.value.rateDetails.unitDropOffs = value;
  }
}

// Checks if manifest items has value when converting pickup/drop
const checkManifestItems: ComputedRef<boolean> = computed(() => {
  if (editedPudItem.value && editedPudItem.value.manifest.length > 0) {
    return true;
  } else {
    return false;
  }
});

/**
 * Gets and sets the isDialogOpen prop passed from parent
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * Returns a list of common addresses for the client, returned from the store.
 * If the list of common addresses is empty or the client ID does not match the
 * client ID of the client details passed as props, an empty array is returned.
 */
const clientCommonAddressList: ComputedRef<ClientCommonAddress[]> = computed(
  () => {
    const commonAddresses = clientDetailsStore.clientCommonAddresses;
    if (
      !props.clientDetails ||
      !commonAddresses?.length ||
      commonAddresses[0].clientId !== props.clientDetails.clientId
    ) {
      return [];
    }
    return commonAddresses;
  },
);

/**
 * For the provided address, returns the ClientCommonAddress object related to
 * that location (if one exists)
 */
const selectedClientCommonAddress: ComputedRef<
  ClientCommonAddress | undefined
> = computed(() => {
  if (!editedPudItem.value) {
    return;
  }
  return getClientCommonAddressForSelectedAddress(
    editedPudItem.value.address,
    clientCommonAddressList.value,
  );
});

/**
 * Returns true if the client is a cash sale client (clientId of 'CS')
 */
const isCashSaleClient: ComputedRef<boolean> = computed(() => {
  return props.clientDetails.clientId === 'CS';
});

/**
 * When the visibility of the dialog changes, sets the editedPudItem to a clone
 * of the pudItem prop for use in the form
 */
watch(dialogController, (newValue) => {
  if (newValue && props.pudItem) {
    editedPudItem.value = initialisePudItem(props.pudItem);
    // set payload dimensions for inputs
    setOverallPayloadDimensionInputs(props.pudItem);
    // Set default dispatch address if we're adding the first pickup
    if (
      !isEditingExistingPud.value &&
      props.jobDetails.pudItems.length === 0 &&
      editedPudItem.value.legTypeFlag === 'P'
    ) {
      setPudAddressToDefaultDispatch(editedPudItem.value);
    }
  } else {
    editedPudItem.value = null;
  }
});

// React to unit changes and update displayed values
watch(lwhUnit, (newUnit, oldUnit) => {
  if (editedPudItem.value) {
    lengthInput.value = formatDimensionToMetres(lengthInput.value, oldUnit);
    widthInput.value = formatDimensionToMetres(widthInput.value, oldUnit);
    heightInput.value = formatDimensionToMetres(heightInput.value, oldUnit);

    lengthInput.value = parseDimensionFromMetres(lengthInput.value, newUnit);
    widthInput.value = parseDimensionFromMetres(widthInput.value, newUnit);
    heightInput.value = parseDimensionFromMetres(heightInput.value, newUnit);
  }
});

watch(weightUnit, (newUnit, oldUnit) => {
  if (editedPudItem.value) {
    weightInput.value = formatWeightToKilograms(weightInput.value, oldUnit);
    weightInput.value = parseWeightFromKilograms(weightInput.value, newUnit);
  }
});

// function to fetch and set client names for common address table form clientDetails using clientID
function fetchClientNames() {
  if (!filteredGlobalCommonAddress.value?.length) {
    return;
  }
  awaitingClientNames.value = true;

  filteredGlobalCommonAddress.value.forEach((address) => {
    const clientId = address.clientId;

    // Find client name from clientSummaryList instead of making a request
    const cachedClient = clientDetailsStore.clientSummaryList.find(
      (client) => client.clientId === clientId,
    );

    if (cachedClient) {
      clientNames[clientId] = cachedClient.clientName;
    }
  });

  awaitingClientNames.value = false;
}

/**
 * Called when opening the dialog to add new pud item. Checks if there are any
 * client common addresses where defaultDispatchAddress is true. If so, sets the
 * pud's address to that address.
 * if Common Address has notes, open JobBookingCommonAddressNotesAttachmentDialog to add notes
 */
function setPudAddressToDefaultDispatch(pudItem: PUDItem) {
  const defaultAddress = clientCommonAddressList.value.find(
    (address) => address.defaultDispatchAddress,
  );
  if (defaultAddress) {
    pudItem.address = deepCopy(defaultAddress.address);
    applyNicknameAddressValuesToPudItem(pudItem, defaultAddress);
    // sets selected address to open add notes from common address dialog after delay
    setTimeout(() => {
      selectedAddress.value = defaultAddress;
    }, 500);
  }
}

/**
 * Called when opening the dialog to add overall payload dimensions for a PUD item.
 * Parses and sets the length, width, height, and weight input values based on the selected units.
 *
 * @param {PUDItem} pudItem - The PUD item whose dimensions and weight are being set.
 */
function setOverallPayloadDimensionInputs(pudItem: PUDItem) {
  if (pudItem.dimensions) {
    lengthInput.value = parseDimensionFromMetres(
      pudItem.dimensions.length,
      lwhUnit.value,
    );
    widthInput.value = parseDimensionFromMetres(
      pudItem.dimensions.width,
      lwhUnit.value,
    );
    heightInput.value = parseDimensionFromMetres(
      pudItem.dimensions.height,
      lwhUnit.value,
    );
    weightInput.value = parseWeightFromKilograms(
      pudItem.weight,
      weightUnit.value,
    );
  }
}

/**
 * Updates the dimensions (length, width, height) and weight of the edited PUD item.
 * - If the input value is `0`, it sets the corresponding dimension or weight to an empty string (`''`).
 * - Otherwise, it formats the value using `formatDimension` or `formatWeight` functions.
 */
function updatePudItemDimensions(pudItem: PUDItem) {
  if (lengthInput.value !== null && lengthInput.value !== undefined) {
    const formattedLength = formatDimensionToMetres(
      lengthInput.value,
      lwhUnit.value,
    );
    pudItem.dimensions.length =
      formattedLength !== null ? formattedLength.toString() : '';
  }
  // Set width only if widthInput.value has a value
  if (widthInput.value !== null && widthInput.value !== undefined) {
    const formattedWidth = formatDimensionToMetres(
      widthInput.value,
      lwhUnit.value,
    );
    pudItem.dimensions.width =
      formattedWidth !== null ? formattedWidth.toString() : '';
  }

  // Set height only if heightInput.value has a value
  if (heightInput.value !== null && heightInput.value !== undefined) {
    const formattedHeight = formatDimensionToMetres(
      heightInput.value,
      lwhUnit.value,
    );
    pudItem.dimensions.height =
      formattedHeight !== null ? formattedHeight.toString() : '';
  }

  pudItem.weight = formatWeightToKilograms(weightInput.value, weightUnit.value);
}

/**
 * Capture emitted index from AddressSearchAU component Pass this into
 * applyNicknameAddressValuesToPudItem method to apply relevant values to job
 */
function nicknameAddressSelected(index: number) {
  if (!props.clientDetails || index === -1 || !editedPudItem.value) {
    return;
  }
  const commonAddress: ClientCommonAddress | undefined =
    clientDetailsStore.clientCommonAddresses[index];
  if (!commonAddress) {
    return;
  }
  selectedAddress.value = commonAddress;
  // Apply properties to job
  applyNicknameAddressValuesToPudItem(editedPudItem.value, commonAddress);

  // Set the site contact in the pud_related_contact component via ref.
  if (commonAddress.defaultContact?.contactId) {
    pudSiteContactRef.value.commonAddressSelected(
      commonAddress.defaultContact.contactId,
    );
  }
}

/**
 * Copies the address of the first pudItem in the job to the editedPudItem.
 * Captures emit from AddressSearchAU component
 */
function addReturnToFirstPud() {
  const copyFrom = props.jobDetails.pudItems[0];
  copyLocationPropertiesToPudItem(editedPudItem.value!, copyFrom);
  if (editedPudItem.value && copyFrom) {
    editedPudItem.value.customerDeliveryName = copyFrom.customerDeliveryName;
    searchInput.value = copyFrom.customerDeliveryName;
  }

  // get notes from address
  const existingAddress = clientCommonAddressList.value.find(
    (item) => item.address.addressId === editedPudItem.value?.address.addressId,
  );
  selectedAddress.value = existingAddress;
}

/**
 * Copies the default DispatchAddress to pudItem in the job to the editedPudItem.
 * Captures emit from AddressSearchAU component
 */
function addReturnToDefaultDispatchLocation() {
  const defaultAddress = clientCommonAddressList.value.find(
    (address) => address.defaultDispatchAddress,
  );
  if (editedPudItem.value && defaultAddress) {
    editedPudItem.value.address = defaultAddress.address;
    editedPudItem.value.customerDeliveryName = defaultAddress.addressNickname;
    searchInput.value = defaultAddress.addressNickname;
    editedPudItem.value.address = deepCopy(defaultAddress.address);
    applyNicknameAddressValuesToPudItem(editedPudItem.value, defaultAddress);
    // Set the site contact in the pud_related_contact component via ref.
    if (defaultAddress.defaultContact?.contactId) {
      pudSiteContactRef.value.commonAddressSelected(
        defaultAddress.defaultContact.contactId,
      );
    }
    selectedAddress.value = defaultAddress;
  }
}

/**
 * Copies the address of the first pudItem in the job to the editedPudItem.
 * Captures emit from AddressSearchAU component
 */
function addPreloadLeg() {
  const copyFrom = props.jobDetails.pudItems[0];
  copyLocationPropertiesToPudItem(editedPudItem.value!, copyFrom);

  if (editedPudItem.value && copyFrom) {
    editedPudItem.value.customerDeliveryName = copyFrom.customerDeliveryName;
    searchInput.value = copyFrom.customerDeliveryName;
  }

  editedPudItem.value!.dropoffReference = [returnPreloadReference()];
  editedPudItem.value!.loadTime = DEFAULT_PRELOAD_STOP_LOAD_TIME;
}

/**
 * Validates the current form data and adds error messages to the errorMessages array if validation fails.
 * @returns {boolean} True if validation passes, false otherwise.
 */
function validationHandler(): boolean {
  if (!editedPudItem.value) {
    return false;
  }
  errorMessages.value = [];
  if (!form.value.validate() && sessionManager.isClientPortal()) {
    const firstPudErrorIndex = errorMessageTypes.findIndex(
      (x: ErrorMessage) => x.type === 'firstPudPayload',
    );

    if (firstPudErrorIndex !== -1) {
      errorMessages.value.push(errorMessageTypes[firstPudErrorIndex]);
    }
  }

  // if (!sessionManager.isClientPortal()) {
  if (!editedPudItem.value.addressIsValid) {
    const gpsErrorTypeIndex = errorMessageTypes.findIndex(
      (x: ErrorMessage) => x.type === 'gpsError',
    );

    if (gpsErrorTypeIndex !== -1) {
      errorMessages.value.push(errorMessageTypes[gpsErrorTypeIndex]);
    }
  } else {
    const gpsErrorIndex = errorMessages.value.findIndex(
      (x: ErrorMessage) => x.type === 'gpsError',
    );
    if (gpsErrorIndex !== -1) {
      errorMessages.value.splice(gpsErrorIndex, 1);
    }
  }
  // Validation of within Australia
  if (
    !!editedPudItem.value.address.country &&
    !routingPossibleForCountryName(editedPudItem.value.address.country)
  ) {
    const outsideCountryErrorIndex = errorMessageTypes.findIndex(
      (x: ErrorMessage) => x.type === 'outsideOfCountry',
    );
    if (outsideCountryErrorIndex !== -1) {
      errorMessages.value.push(errorMessageTypes[outsideCountryErrorIndex]);
    }
  } else {
    const outsideCountryErrorIndex = errorMessages.value.findIndex(
      (x: ErrorMessage) => x.type === 'outsideOfCountry',
    );
    if (outsideCountryErrorIndex !== -1) {
      errorMessages.value.splice(outsideCountryErrorIndex, 1);
    }
  }
  // }
  // Epoch time is valid if we're:
  // - In the client portal
  // - The epoch time is not 0 and is greater than or equal to the earliest
  //   booking time (at the moment this value is: 3 months ago)
  const epochTimeIsValid: boolean =
    sessionManager.isClientPortal() ||
    (editedPudItem.value.epochTime !== 0 &&
      editedPudItem.value.epochTime >= returnEarliestBookingTime());

  if (!epochTimeIsValid) {
    const invalidDateTypeIndex = errorMessageTypes.findIndex(
      (x: ErrorMessage) => x.type === 'invalidDate',
    );
    if (invalidDateTypeIndex !== -1) {
      errorMessages.value.push(errorMessageTypes[invalidDateTypeIndex]);
    }
  }
  if (!form.value.validate() || errorMessages.value.length > 0) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return false;
  } else {
    return true;
  }
}

/**
 * Deletes the current PUD item and emits an event to signal deletion.
 */
function deletePudItem(): void {
  emit('deletePudAtIndex', indexOfEditedPud.value);
}

// reopen add stop dialog after saving stop
async function savePudChangeLeg(legType: 'P' | 'D') {
  await savePudItem(legType); // Wait for savePudItem to finish
}

/**
 * Saves the current editedPudItem object. Sets various properties include
 * pickupTime, pickupDate and createdByDriver. Emits the save payload to the
 * parent, containing the pudItem, index and the validated plannedRoute
 */
async function savePudItem(newLegType?: 'P' | 'D'): Promise<void> {
  // Validate for common errors
  if (
    !editedPudItem.value ||
    !validationHandler() ||
    (!props.clientDetails && !isCashSaleClient.value)
  ) {
    return;
  }
  isLoading.value = true;
  // Remove any empty references from lists1
  findAndRemovePudReferencesFromList(
    editedPudItem.value.legTypeFlag === 'P'
      ? editedPudItem.value.pickupReference
      : editedPudItem.value.dropoffReference,
    '',
  );
  editedPudItem.value.pickupTime = returnFormattedTime(
    editedPudItem.value.epochTime,
  );
  const epochValue = editedPudItem.value.epochTime;
  editedPudItem.value.pickupTime = returnFormattedTime(epochValue);
  editedPudItem.value.pickupDate = returnStartOfDayFromEpoch(epochValue);
  editedPudItem.value.createdByDriver = false;
  // Apply standby rate to pud item if client requires it. Comes from known
  // nicknamed addresses
  if (props.clientDetails && !isCashSaleClient.value) {
    const nicknameAddress = clientDetailsStore.clientCommonAddresses.find(
      (address: ClientCommonAddress) =>
        address.address.geoLocation[0] ===
          editedPudItem.value!.address.geoLocation[0] &&
        address.address.geoLocation[1] ===
          editedPudItem.value!.address.geoLocation[1],
    );
    editedPudItem.value.isStandbyRate =
      nicknameAddress && nicknameAddress.isStandbyRate
        ? nicknameAddress.isStandbyRate
        : false;
  }

  // set dimensions and weight from overall payload dimension and convert before saving
  // cm to m & t/g to kg
  updatePudItemDimensions(editedPudItem.value);
  const payload: SavePudPayload = {
    pudItem: editedPudItem.value,
    newLegTypeFlag: newLegType,
  };
  let shouldRequestRoute = false;

  // Construct the updated pud list, with the edited pud item included
  const updatedPudList = returnUpdatedPudList(
    props.jobDetails.pudItems,
    editedPudItem.value,
  );
  const allAddressesValid = updatedPudList.every((pud) => pud.addressIsValid);

  // If there are any pudItems in the list with invalid addresses, show a notification
  if (!allAddressesValid) {
    shouldRequestRoute = false;
  } else if (
    // If after adding the current pud item, there would be 2 more or puds, then
    // we need to request the route.
    isEditingExistingPud.value &&
    props.jobDetails.pudItems.length >= 2
  ) {
    shouldRequestRoute = true;
  } else if (
    !isEditingExistingPud.value &&
    props.jobDetails.pudItems.length >= 1
  ) {
    shouldRequestRoute = true;
  }

  // Try to generate the route. If the result is null, it means the new route
  // contains one or more points that our routing service could not reach.
  if (shouldRequestRoute) {
    const route = await retrieveAndValidateRoute(updatedPudList);
    if (!route) {
      return;
    }
    // If successful then add the route to the payload
    payload.plannedRoute = route;
  }
  isLoading.value = false;
  // Emit the object and close the dialog
  emit('savePudItem', payload);
  dialogController.value = false;
}

function returnUpdatedPudList(
  currentPudItems: PUDItem[],
  updatedPudItem: PUDItem,
) {
  const pudList = [...props.jobDetails.pudItems];
  // Replace the edited pud item with the updated one, or add it to the list
  if (indexOfEditedPud.value !== -1) {
    pudList[indexOfEditedPud.value] = updatedPudItem;
  } else {
    pudList.push(updatedPudItem);
  }
  return pudList;
}

/**
 * Retrieves the planned route for the job, including the edited pud item.
 * Validates that the response has a number of segments equal to the number of
 * pud items minus 1, which means each pud item is reachable from the previous
 * one.
 * @param {PUDItem} pudItem The pud item to add to the job details
 * @returns {ORSRoute | null} The planned route, or null if the route is invalid
 */
async function retrieveAndValidateRoute(
  pudItems: PUDItem[],
): Promise<ORSRoute | null> {
  try {
    const tempJobDetails = new JobDetails();
    tempJobDetails.pudItems = [...pudItems];

    const route: ORSRoute | null = await tempJobDetails.getPlannedRoute();

    // Length of segments should be equal to the number of pud items minus 1
    if (!routeIsValidForJobLength(tempJobDetails.pudItems.length, route)) {
      throw new Error(
        '1 or more points are not reachable. Please check the route.',
      );
    }
    return route;
  } catch (e) {
    let errorMessage = GENERIC_ERROR_MESSAGE;
    if (e instanceof Error) {
      errorMessage = e.message;
    }
    showAppNotification(errorMessage);
    return null;
  }
}

/**
 * Shows a notification with the provided text and type
 * @param {string} text The text to display in the notification
 * @param {HealthLevel} type The type of notification to display
 */
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: 'Job Booking - Add/Edit Leg',
  });
}

/**
 * Opens confirmationDialog if pickup/drop
 * has manifest items
 */
function handleLegChange() {
  if (!isEditingExistingPud.value) {
    changeLegType();
  } else {
    if (checkManifestItems.value) {
      openDialogForChange();
    } else {
      changeLegType();
    }
  }
}

/**
 * Updates the leg type of the currently edited PUD item and performs related
 * actions based on the leg type.
 */
function changeLegType() {
  if (!props.pudItem) {
    return;
  }
  if (props.pudItem.legTypeFlag === 'P') {
    props.pudItem.legTypeFlag = 'D';
    if (editedPudItem.value) {
      editedPudItem.value.manifest = [];
      editedPudItem.value.legTypeFlag = 'D';
    }
  } else {
    props.pudItem.legTypeFlag = 'P';
    if (editedPudItem.value) {
      editedPudItem.value.legTypeFlag = 'P';
      editedPudItem.value.manifest = [];
    }
  }
}
// close dialog and do not save
function cancelChanges() {
  dialogController.value = false;
}
// update pud time
const updateCurrentPudItemPudTime = (newValue: string) => {
  if (editedPudItem.value) {
    editedPudItem.value.pickupTime = newValue;
  }
};
//  set pud Time
function setSelectedJobDate(epoch: number | null): void {
  if (editedPudItem.value) {
    editedPudItem.value.pickupDate = epoch;
  }
}

// Delete the note from PudDetails
function handleRemoveNote(index: number) {
  if (editedPudItem.value) {
    editedPudItem.value.notes.splice(index, 1);
  }
}

/**
 * Clears stop notes and the selected address when the business/site name combobox is cleared.
 * @param {string | null} value - The new value of the combobox. If `null`, the address and notes are cleared.
 */
function onClearSiteUpdate(value: string | undefined) {
  if (value === undefined) {
    selectedAddress.value = undefined;
    if (editedPudItem.value) {
      editedPudItem.value.address = new AddressAU();

      // Remove all notes where type.id === 2
      editedPudItem.value.notes = editedPudItem.value.notes.filter(
        (editedNote) => editedNote.type?.id !== 2,
      );
    }
  }
}

/**
 * Handles emit from JobBookingClientNotesDialog component. Payload contains a
 * list of notes to add to the job.
 */
function addNotesFromDialog(notes: Communication[]) {
  if (editedPudItem.value) {
    notes = notes.map((n) => {
      return Object.assign(new Communication(), n);
    });
    const existingNoteIds = new Set(
      editedPudItem.value.notes.map((note) => note.id),
    );

    // // Filter out duplicates and prepare the notes to be added
    const notesToAdd = notes
      .filter((note) => !existingNoteIds.has(note.id)) // Check for duplicates
      .map((note) => {
        return updateClientNoteBeforeAddingToJob(
          note,
          sessionManager.getUserName(),
        );
      });
    editedPudItem.value.notes = [...editedPudItem.value.notes, ...notesToAdd];
  }
}

// Handler for the emitted event from the child
const handleEditedManifestUpdate = (newManifest: Manifest | null) => {
  // Set the boolean based on whether the newManifest is null or not
  isEditingManifest.value = newManifest !== null;
};

// handle edit note event
function handleEditNote(note: Communication) {
  currentlyEditingNote.value = note;
  isEditingExistingNote.value = true;
}

// function to save edited Note Locally
function saveEditedNote() {
  if (currentlyEditingNote.value) {
    // Find the note in editedPudItem.notes using the id
    const noteToEdit = editedPudItem.value?.notes.find(
      (note) => note.id === currentlyEditingNote.value.id,
    );
    if (noteToEdit) {
      noteToEdit.visibleTo = currentlyEditingNote.value.visibleTo;
      isEditingExistingNote.value = false;
    } else {
      console.error('Note to edit not found');
      isEditingExistingNote.value = false;
    }
  }
}

// pass to jobAllNotes panel to hide selected pud notes
const selectedPudId = computed(() => {
  const uniqueId = editedPudItem.value?.uniqueId || null;
  return uniqueId;
});
</script>
<style scoped lang="scss">
.title-container {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: stretch;
  padding: 2px 18px;
  border-bottom: 1px solid $translucent-bg;
  .form-title-txt {
    font-family: $font-sans;
    color: var(--text-color);
    font-size: $font-size-22;
    font-weight: 600;
    margin-right: 28px;

    .new-pud-type {
      display: inline-block;
      background-color: red;
      width: 15px;
      height: 15px;
      border-radius: 100px;
      padding: 0px;
      margin-left: 6px;
      &.P {
        background-color: $pickup;
        border-left: 2px solid $pickup-highlight;
        box-shadow: 0px 1px 2px 1px $pickup !important;
      }
      &.D {
        background-color: $drop;
        border-left: 2px solid $drop-highlight;
        box-shadow: 0px 1px 2px 1px $drop !important;
      }
    }
  }
  .driver-chat-btn {
    border: 2px solid orange;
    color: var(--text-color);
    margin-right: 8px;
    padding-right: 16px;
  }
  justify-content: space-between;
  .v-btn {
    font-size: $font-size-12;
    max-width: 100%;
    &.P {
      color: $drop-highlight;
      background: rgba(249, 0, 112, 0.1) !important;
      border: 1px solid $drop-highlight;
      &.disabled {
        border: none;
      }
    }
    &.D {
      color: $pickup;
      background: rgba(85, 191, 89, 0.1) !important;
      border: 1px solid $pickup;
      &.disabled {
        border: none;
      }
    }
    border-radius: $border-radius-lg;
  }
  .delete-pud-btn {
    align-items: center;
    .v-icon {
      margin-left: 6px;
    }
    color: white;
    border: 1px solid red;
    &.disabled {
      border: none;
    }
  }
}

.time-container {
  margin-top: 26px;
}

.payload-header {
  padding-top: 4px;
  .payload-inputs {
    padding-bottom: 4px;
    margin-top: 4px;
  }
}

.manifest-table {
  margin-top: 10px;
}
.header-container {
  padding: 2px;
}
.qty-txt {
  padding: 12px;
  font-size: $font-size-16;
  color: $bg-light !important;
  font-weight: 600;
}
.btn-container {
  display: flex;
  align-content: center;
  margin: 22px;
  height: 24px;
  .action-btn {
    max-width: 300px;
    border-radius: $border-radius-btn;
    transition: box-shadow 0.3s;
    height: 42px;
    margin-right: 18px;
    &.confirm {
      max-width: 400px;
      background-color: $info !important;
      color: white;
      &:hover {
        box-shadow: var(--box-shadow);
      }
    }
    &.drop {
      margin-right: 42px;
      color: var(--light-text-color);
      &:hover {
        background: $drop-color !important;
        border-color: $drop-highlight !important;
        box-shadow: var(--box-shadow);
        color: white;
      }
    }
    // &.pickup {
    //   color: var(--light-text-color);
    //   &:hover {
    //     background: $pickup-color !important;
    //     border-color: $pickup !important;
    //     box-shadow: var(--box-shadow);
    //     color: white;
    //   }
    // }
    &.cancel {
      min-width: 200px;
    }
  }
}

.stop-icon {
  box-shadow: $box-shadow-dark !important;
  margin-left: 6px;
  margin-bottom: 3px;
}

.notes-btn {
  width: 100%;
  border-radius: 14px;
  color: var(--warning) !important;
  padding: 0px;
  margin: 0px;
  height: 34px;
  background-color: transparent !important;
  border: 1.5px solid var(--warning);
  &:disabled {
    background: none !important;
    border: none !important;
  }
}
.notes-container {
  height: 302px;
  max-height: 100%;
  padding-right: 4px;
  overflow-x: hidden;
  margin-top: 4px;
}

.vertical-divider {
  margin-right: 14px;
  margin-left: 14px;
}

.dialog-content {
  padding: 26px;
}

.dialog-btn-container {
  display: flex;
  justify-content: space-between;
  align-content: center;
  padding: 8px;
  border-top: 1px solid $translucent;
  .v-btn {
    min-width: 260px;
    min-height: 34px;
  }
}

.check-icon {
  padding-top: 10px;
  color: $accent !important;
}

.global-common-address-search-txt {
  color: var(--accent);
  padding: 2px 8px;
  margin-left: 8px;
  cursor: pointer;
  .clickable-text {
    margin-left: 8px;
    font-weight: 800;
    border-bottom: 2px solid $accent;
  }
}
</style>
