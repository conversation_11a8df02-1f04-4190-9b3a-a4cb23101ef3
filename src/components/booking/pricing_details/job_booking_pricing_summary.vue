<template>
  <v-layout pt-2 mb-2>
    <v-flex md12 :px-3="!readOnly">
      <table class="pricing-summary-table">
        <tbody>
          <tr
            v-for="item in tableData"
            :key="item.id"
            :class="{
              'reduce-padding':
                !readOnly &&
                item.allowIncrement &&
                item.additionalChargeId &&
                item.additionalChargeId !== tollAdminAndHandlingId,
            }"
          >
            <td>{{ item.service }}</td>
            <td>
              <span>{{ item.zone }}</span>
              <span
                v-if="item.zoneRequired"
                class="error-chip accent-text--card error-type"
              >
                Required
              </span>
            </td>
            <td>{{ item.rate }}</td>
            <td>
              <span
                class="quantity-container"
                v-if="
                  !readOnly &&
                  item.allowIncrement &&
                  item.additionalChargeId &&
                  item.additionalChargeId !== tollAdminAndHandlingId
                "
              >
                <v-flex md12>
                  <v-layout justify-start align-center>
                    <v-text-field
                      class="v-solo-custom shrink-input-field"
                      type="number"
                      solo
                      flat
                      height="40px"
                      color="light-blue"
                      label="Quantity"
                      @focus="$event.target.select()"
                      v-model.number="item.quantityValue"
                      prefix="Qty"
                      max="1000"
                      hide-details
                      :rules="[
                        validationRules.required,
                        validationRules.number,
                        validationRules.nonNegative,
                      ]"
                      validate-on-blur
                      @change="
                        updateAdditionalChargeItem({
                          type: AdditionalChargeUpdateType.SET_QUANTITY,
                          id: item.additionalChargeId,
                          quantity: $event,
                        })
                      "
                      @wheel.prevent
                      @keydown.up.prevent="
                        updateAdditionalChargeItem({
                          type: AdditionalChargeUpdateType.INCREMENT,
                          id: item.additionalChargeId,
                        })
                      "
                      @keydown.down.prevent="
                        updateAdditionalChargeItem({
                          type: AdditionalChargeUpdateType.DECREMENT,
                          id: item.additionalChargeId,
                        })
                      "
                    ></v-text-field>
                  </v-layout>
                </v-flex>
              </span>
              <span v-else>
                {{ item.quantity }}
              </span>
            </td>
            <td>{{ item.calculation }}</td>
            <td>
              {{ item.amount }}

              <span class="pl-2" v-if="item.tooltipInfo">
                <InformationTooltip
                  :right="true"
                  :tooltipType="HealthLevel.INFO"
                  ><span slot="content">{{ item.tooltipInfo }}</span>
                </InformationTooltip>
              </span>
              <div
                class="tooltip-container"
                v-if="
                  !readOnly &&
                  item.additionalChargeId &&
                  item.additionalChargeId !== tollAdminAndHandlingId
                "
              >
                <v-tooltip right>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      tabindex="-1"
                      flat
                      v-on="on"
                      icon
                      class="ma-0"
                      @click="
                        updateAdditionalChargeItem({
                          type: AdditionalChargeUpdateType.REMOVE,
                          id: item.additionalChargeId,
                        })
                      "
                    >
                      <v-icon size="17" color="grey lighten-1"
                        >fas fa-minus-circle</v-icon
                      >
                    </v-btn>
                  </template>
                  Remove this charge
                </v-tooltip>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <span class="info-txt" v-if="showInfoText">
        Pricing is subject to change based on
        {{ useCompanyDetailsStore().divisionDetails?.divisionShortName }}
        terms and conditions.
        <p v-if="accounting.clientRates?.[0]?.rate?.rateTypeId === 1">
          Pricing estimate for time rate jobs might include additional times
          outside of the 'First Stop' / 'Final Stop'.
        </p>
      </span>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
interface TableData {
  id: string;
  service: string;
  zone: string;
  zoneRequired?: boolean;
  rate: string;
  quantity: string;
  quantityValue: number;
  additionalChargeId?: string;
  allowIncrement?: boolean;
  calculation: string;
  amount: string;
  tooltipInfo?: string;
}

import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  DisplayCurrencyValue,
  addPercentageTo,
  getPercentageOf,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import {
  computeTollChargeItemSubtotal,
  reorderAdditionalChargeItems,
  validateAdditionalChargeUpdate,
} from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import {
  getAdjustedDistanceRateValues,
  returnRangeRateSummary,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import {
  isDistanceRateTypeObject,
  isZoneToZoneRateTypeObject,
} from '@/helpers/RateHelpers/RateTableItemHelpers';
import {
  timeRateDescription,
  timeRateMinCharge,
} from '@/helpers/RateHelpers/TimeRateHelpers';
import { demurrageChargeIncrement } from '@/helpers/RateHelpers/ZoneRateHelpers';
import { returnZoneToZoneName } from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { returnServiceTypeLongNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { AdditionalChargeApplicationType } from '@/interface-models/AdditionalCharges/AdditionalChargeApplicationType';
import type { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeRateBasis } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import {
  AdditionalChargeUpdateType,
  UpdateAdditionalChargeItem,
} from '@/interface-models/AdditionalCharges/AdditionalChargeUpdateOperation';
import type { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { DistanceRateData } from '@/interface-models/Jobs/FinishedJobDetails/DistanceRate/DistanceRateData';
import { DistanceRateRangeSubtotal } from '@/interface-models/Jobs/FinishedJobDetails/DistanceRate/DistanceRateRangeSubtotal';
import { UnitRateData } from '@/interface-models/Jobs/FinishedJobDetails/UnitRateData';
import ZoneRateData from '@/interface-models/Jobs/FinishedJobDetails/zoneRateData';
import { ZoneToZoneRateData } from '@/interface-models/Jobs/FinishedJobDetails/ZoneToZoneRate/ZoneToZoneRateData';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { demurrageAppliesLabel } from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { DistanceRateType } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { MinChargeBasis } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/MinChargeBasis';
import { TimeRateType } from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import { UnitRate } from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import { ZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { ZoneToZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { v4 as uuidv4 } from 'uuid';
import { ComputedRef, Ref, computed, toRef } from 'vue';

const props = withDefaults(
  defineProps<{
    serviceTypeId: number;
    pudItems: PUDItem[];
    accounting: JobAccountingDetails;
    readOnly?: boolean;
    showInfoText?: boolean;
  }>(),
  {
    readOnly: true,
    showInfoText: true,
  },
);

const emit = defineEmits<{
  (event: 'updateAdditionalCharge', payload: UpdateAdditionalChargeItem): void;
}>();

const jobAccountingDetails: Ref<JobAccountingDetails> = toRef(
  props,
  'accounting',
);

/**
 * Returns the first row of the table, containing 1 or more rows describing
 * current selected rate.
 */
function returnClientRateTableRow(
  accounting: JobAccountingDetails,
): TableData[] {
  let rows: TableData[] = [];

  try {
    if (!accounting.clientRates || accounting.clientRates.length === 0) {
      return [
        {
          id: uuidv4(),
          service: 'Select pricing information',
          zone: '',
          rate: '',
          quantity: '',
          quantityValue: 0,
          calculation: '',
          amount: 'TBD',
        },
      ];
    }
    const rateTableItem = accounting.clientRates[0].rate;
    const rateTypeId: number = rateTableItem.rateTypeId;
    const serviceCode = returnServiceTypeLongNameFromId(props.serviceTypeId);

    const clientVariancePct =
      accounting.clientServiceRateVariations?.clientAdjustmentPercentage;

    switch (rateTypeId) {
      case JobRateType.TIME: // TIME
        rows = returnTableRowsFromTimeRateData(
          props.pudItems,
          accounting,
          serviceCode,
        );
        break;
      case JobRateType.ZONE: // ZONE
        const zoneRateData = accounting.finishedJobData
          .clientRateData as ZoneRateData[];
        rows = returnTableRowsFromZoneRateData(
          rateTableItem,
          props.pudItems,
          zoneRateData,
          serviceCode,
        );
        break;
      case JobRateType.DISTANCE: // DISTANCE
        const distanceRateData = accounting.finishedJobData
          .clientRateData as DistanceRateData;
        rows = returnTableRowsFromDistanceRateData(
          rateTableItem,
          distanceRateData,
          serviceCode,
          clientVariancePct,
        );
        break;
      case JobRateType.UNIT: // UNIT
        const unitRateData = accounting.finishedJobData
          .clientRateData as UnitRateData;
        rows = returnTableRowsFromUnitRateData(
          rateTableItem,
          props.pudItems,
          unitRateData,
          serviceCode,
        );
        break;
      case JobRateType.TRIP: // TRIP/Quoted
        rows.push({
          id: uuidv4(),
          service: 'Quoted Rate',
          zone: '',
          rate: '',
          quantity: '1',
          quantityValue: 1,
          calculation: rateTableItem.fuelSurcharge
            ? 'Fuel inclusive'
            : 'Fuel exclusive',
          amount: `$${accounting.totals.subtotals.freightCharges.client}`,
        });
        break;
      case JobRateType.ZONE_TO_ZONE: // ZONE TO ZONE
        const zoneToZoneRateData = accounting.finishedJobData
          .clientRateData as ZoneToZoneRateData;
        rows = returnTableRowsFromZoneToZoneRateData(
          rateTableItem,
          zoneToZoneRateData,
          serviceCode,
          clientVariancePct,
        );
        break;
    }
  } catch (error) {
    // console.error('Error in returnClientRateTableRow:', error);
    rows = [
      {
        id: uuidv4(),
        service: 'Error',
        zone: '',
        rate: '',
        quantity: '',
        quantityValue: 0,
        calculation: '',
        amount: error instanceof Error ? error.message : 'Unknown error',
      },
    ];
  }

  return rows;
}

/**
 * Returns the rows for the table based on the time rate data. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param pudItems - The PUD items from the job
 * @param accounting - The job accounting info
 * @param serviceCode - The service type id
 */
function returnTableRowsFromTimeRateData(
  pudItems: PUDItem[],
  accounting: JobAccountingDetails,
  serviceCode: string,
): TableData[] {
  const clientRate = accounting.clientRates[0];
  const timeRateType = clientRate.rate.rateTypeObject as TimeRateType;
  const estimatedDuration = returnCorrectDuration(
    accounting.finishedJobData?.clientDurations?.expectedBilledDuration ?? 0,
  );
  const readableBilledDuration =
    accounting.finishedJobData?.clientDurations?.readableBilledDuration;
  // Get the percentage discount/surcharge applied to this rate and service
  const variancePercentage =
    accounting.clientServiceRateVariations?.clientAdjustmentPercentage ?? 0;

  const rateDescription = timeRateDescription({
    rate: timeRateType.rate,
    multiplier: timeRateType.rateMultiplier,
    variancePct: variancePercentage,
  });

  const standbyDuration =
    accounting.finishedJobData?.clientDurations?.readableStandbyDuration;
  const standbyDescription = timeRateDescription({
    rate: timeRateType.standbyRate,
    multiplier: timeRateType.standbyMultiplier,
    variancePct: variancePercentage,
  });
  const rows: TableData[] = [];
  rows.push({
    id: uuidv4(),
    service: serviceCode,
    zone: '',
    rate: rateDescription,
    quantity: estimatedDuration
      ? `${estimatedDuration} estimate - (Min. ${timeRateMinCharge(
          timeRateType,
        )})`
      : 'N/A',
    quantityValue: 1,
    calculation: `${rateDescription} x ${readableBilledDuration}`,
    amount: `$${accounting.totals.subtotals.freightCharges.client}`,
  });
  if (accounting.totals?.subtotals?.standbyChargeTotals?.client) {
    const standbyStops = pudItems
      .map((pudItem, idx) => {
        return pudItem.isStandbyRate ? `Stop #${idx + 1}` : '';
      })
      .filter((stop) => stop !== '')
      .join(', ');
    rows.push({
      id: uuidv4(),
      service: 'Standby Charge',
      zone: standbyStops,
      rate: standbyDescription,
      quantity: standbyDuration
        ? `${standbyDuration} standby time (ESTIMATE)`
        : 'N/A',
      quantityValue: 1,
      calculation: `${standbyDescription} x ${standbyDuration}`,
      amount: `$${accounting.totals.subtotals.standbyChargeTotals.client}`,
    });
  }
  return rows;
}

/**
 * Returns the rows for the table for a DISTANCE rate type job. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param rateTableItem - The applicable rate table item
 * @param pudItems - The PUD items from the job
 * @param distanceRateData - The distance rate data from job finished data
 * @param serviceCode - The jobs service code
 */
function returnTableRowsFromDistanceRateData(
  rateTableItem: RateTableItems,
  distanceRateData: DistanceRateData,
  serviceCode: string,
  variancePct: number | null | undefined,
): TableData[] {
  const rows: TableData[] = [];

  if (
    !isDistanceRateTypeObject(
      rateTableItem.rateTypeId,
      rateTableItem.rateTypeObject,
    )
  ) {
    return [];
  }

  const distanceRateType: DistanceRateType = rateTableItem.rateTypeObject;

  if (distanceRateData.rangeSubtotals.length === 1) {
    // If there's only one element, we can assume it's the total charge and use
    // properties on the distanceRateData object. We also don't need to add an
    // additional first row or additional totals row.
    const subtotal = distanceRateData.rangeSubtotals[0];
    const foundRange = distanceRateType.rates.find(
      (rate) => rate.id === subtotal.rangeRateId,
    );
    if (foundRange) {
      let rate = '';
      let quantity = '';

      // Get the rate we will apply per km, applying the variance percentage if
      // provided
      const { rateToApply, minCharge, baseFreightCharge } =
        getAdjustedDistanceRateValues(
          foundRange,
          distanceRateType,
          variancePct,
        );

      if (distanceRateType.minChargeBasis === MinChargeBasis.AMOUNT) {
        rate = `$${rateToApply}/km (Min. $${minCharge})`;
        quantity = `${distanceRateData.editedTravelDistance} km`;
      } else {
        rate = `$${rateToApply}/km`;
        quantity = `${distanceRateData.editedTravelDistance}km (Min. ${minCharge}km)`;
      }
      // Add first summary row
      rows.push({
        id: uuidv4(),
        service: serviceCode,
        zone: '',
        rate: '',
        quantity: quantity,
        quantityValue: 1,
        calculation: `Pre: ${distanceRateData.preJobTravel.distanceInKm}km, Post: ${distanceRateData.postJobTravel.distanceInKm}km`,
        amount: `Base Rate: $${DisplayCurrencyValue(baseFreightCharge)}`,
        tooltipInfo: '',
      });

      rows.push({
        id: uuidv4(),
        service: '',
        zone: 'All',
        rate: rate,
        quantity: `${distanceRateData.calculatedTotalDistance}km`,
        quantityValue: 1,
        calculation: `$${rateToApply}/km x ${distanceRateData.calculatedTotalDistance}km`,
        amount: `$${DisplayCurrencyValue(distanceRateData.chargeExclGst)}`,
        tooltipInfo: '',
      });
    }
  } else {
    let rate = '';
    let quantity = '';

    // Get the rate we will apply per km, applying the variance percentage if
    // provided
    const { minCharge, baseFreightCharge } = getAdjustedDistanceRateValues(
      { rate: 0 },
      distanceRateType,
      variancePct,
    );

    if (distanceRateType.minChargeBasis === MinChargeBasis.AMOUNT) {
      rate = `Various (Min. $${minCharge})`;
      quantity = `${distanceRateData.editedTravelDistance} km`;
    } else {
      rate = `Various`;
      quantity = `${distanceRateData.editedTravelDistance}km (Min. ${minCharge}km)`;
    }

    // Add first summary row
    rows.push({
      id: uuidv4(),
      service: serviceCode,
      zone: '',
      rate: rate,
      quantity: quantity,
      quantityValue: 1,
      calculation: `Pre: ${distanceRateData.preJobTravel.distanceInKm}km, Post: ${distanceRateData.postJobTravel.distanceInKm}km`,
      amount: `Base Rate: $${DisplayCurrencyValue(baseFreightCharge)}`,
      tooltipInfo: '',
    });

    // Iterate over rangeSubtotals and add a row each
    distanceRateData.rangeSubtotals.forEach(
      (subtotal: DistanceRateRangeSubtotal) => {
        const foundRangeIndex = distanceRateType.rates.findIndex(
          (rate) => rate.id === subtotal.rangeRateId,
        );
        if (foundRangeIndex === -1) {
          return;
        }

        const foundRange = distanceRateType.rates[foundRangeIndex];

        // Get the rate we will apply per km, applying the variance percentage if
        // provided
        const rateToApply = addPercentageTo(foundRange.rate, variancePct ?? 0);

        rows.push({
          id: uuidv4(),
          service: '',
          zone: `${returnRangeRateSummary(foundRange, foundRangeIndex)}`,
          rate: `$${rateToApply}/km`,
          quantity: `${subtotal.chargeableDistance}km`,
          quantityValue: 1,
          calculation: `$${rateToApply}/km x ${subtotal.chargeableDistance}km`,
          amount: `$${DisplayCurrencyValue(subtotal.chargeExclGst)}`,
          tooltipInfo: '',
        });
      },
    );

    // Add totals row
    rows.push({
      id: uuidv4(),
      service: '',
      zone: '',
      rate: '',
      quantity: '',
      quantityValue: 1,
      calculation: 'FREIGHT TOTAL',
      amount: `$${DisplayCurrencyValue(distanceRateData.chargeExclGst)}`,
      tooltipInfo: '',
    });
  }

  return rows;
}
/**
 * Returns the rows for the table for a ZONE rate type job. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param rateTableItem - The applicable rate table item
 * @param pudItems - The PUD items from the job
 * @param zoneRateData - The zone rate data from job finished data
 * @param serviceCode - The jobs service code
 */
function returnTableRowsFromZoneRateData(
  rateTableItem: RateTableItems,
  pudItems: PUDItem[],
  zoneRateData: ZoneRateData[],
  serviceCode: string,
): TableData[] {
  const rows: TableData[] = pudItems.map((pudItem, index) => {
    const associatedZone = zoneRateData.find((zoneRate) =>
      pudItem.pudId
        ? zoneRate.pudId === pudItem.pudId
        : zoneRate.pudId === index.toString(),
    );
    if (!associatedZone) {
      return {
        id: uuidv4(),
        service: serviceCode,
        zone: `Stop #${index + 1}: `,
        zoneRequired: true,
        rate: '-',
        quantity: '-',
        quantityValue: 0,
        calculation: '-',
        amount: 'N/A',
      };
    }
    const zoneName = associatedZone.rate;
    const zoneRate = `$${DisplayCurrencyValue(
      associatedZone.freightChargesTotalExclGst,
    )}`;

    // Check if we need to display a tooltip with demurrage information
    let tooltipInfo = '';
    const foundZoneRateType = (
      rateTableItem.rateTypeObject as ZoneRateType[]
    ).find((rate) => rate.zone === associatedZone.zoneId);
    if (foundZoneRateType?.demurrage?.rate) {
      const appliesAfter = returnCorrectDuration(
        foundZoneRateType.demurrage.graceTimeInMilliseconds,
      );
      const demurrageRate = `$${DisplayCurrencyValue(
        foundZoneRateType.demurrage.rate,
      )}`;
      const demurrageIncrement = demurrageChargeIncrement(
        foundZoneRateType.demurrage,
      );
      tooltipInfo = `Demurrage applies after ${appliesAfter} (${demurrageRate} per ${demurrageIncrement})`;
    }

    return {
      id: uuidv4(),
      service: index === 0 ? serviceCode : '',
      zone: `Zone: ${zoneName}`,
      rate: zoneRate,
      quantity: `Stop #${index + 1}`,
      quantityValue: 1,
      calculation: '',
      amount: `$${DisplayCurrencyValue(
        associatedZone.freightChargesTotalExclGst,
      )}`,
      tooltipInfo: tooltipInfo,
    };
  });

  return rows;
}

/**
 * Returns the rows for the table for a unit rate job. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param pudItems - The PUD items from the job
 * @param accounting - The job accounting info
 * @param unitRateData - The unit rate data from job finished data
 * @param serviceCode - The jobs service code
 */
function returnTableRowsFromUnitRateData(
  rateTableItem: RateTableItems,
  pudItems: PUDItem[],
  unitRateData: UnitRateData,
  serviceCode: string,
): TableData[] {
  return pudItems.map((pudItem, index) => {
    const stopNumber = `Stop #${index + 1}`;
    const associatedUnitData = unitRateData.zonedUnitRateData.find(
      (unitRate) =>
        pudItem.pudId
          ? unitRate.pudId === pudItem.pudId
          : unitRate.pudId === index.toString(),
    );

    if (!associatedUnitData) {
      return {
        id: uuidv4(),
        service: 'N/A',
        zone: `Stop #${index + 1}: `,
        zoneRequired: true,
        rate: '-',
        quantity: '-',
        quantityValue: 0,
        calculation: '-',
        amount: 'N/A',
      };
    }

    const unitCount =
      pudItem.legTypeFlag === 'P'
        ? pudItem.rateDetails.unitPickUps || 0
        : pudItem.rateDetails.unitDropOffs || 0;

    // Format load and dangerous goods charges if they exist
    const loadCharges = associatedUnitData.loadChargesTotalExclGst
      ? `$${associatedUnitData.loadChargesTotalExclGst}`
      : '';
    const dgCharges = associatedUnitData.dangerousGoodsChargesTotalExclGst
      ? `$${associatedUnitData.dangerousGoodsChargesTotalExclGst}`
      : '';

    // Construct the zone description, including load charges and dangerous goods charges if applicable
    let zoneDescription = `${stopNumber}: ${associatedUnitData.unitTypeName}`;
    if (loadCharges || dgCharges) {
      const flagfallStr = loadCharges ? `Flagfall ${loadCharges}` : '';
      const dgStr = dgCharges ? `Dangerous Goods ${dgCharges}` : '';
      zoneDescription += ` (${[flagfallStr, dgStr]
        .filter(Boolean)
        .join(', ')})`;
    }

    const perUnitRate = `$${associatedUnitData.appliedRangeRate}`;
    const rateString = `${perUnitRate}/unit`;
    const zoneTotal = `$${DisplayCurrencyValue(
      associatedUnitData.zoneChargeExclGst,
    )}`;

    // Construct the calculation string
    const calculation = `${perUnitRate} x ${unitCount}`;
    const calcParts: string[] = [];
    if (loadCharges) {
      calcParts.push(`${loadCharges} (FF)`);
    }
    if (dgCharges) {
      calcParts.push(`${dgCharges} (DG)`);
    }
    if (loadCharges || dgCharges) {
      calcParts.push(`( ${calculation} )`);
    } else {
      calcParts.push(calculation);
    }
    const combinedCalculationStr = calcParts.join(' + ');

    let tooltipInfo = '';
    const foundUnitRate = (rateTableItem.rateTypeObject as UnitRate[]).find(
      (rate) => rate.zoneId === associatedUnitData.zoneId,
    );
    if (foundUnitRate?.demurrage?.rate) {
      const appliesAfter = returnCorrectDuration(
        foundUnitRate.demurrage.graceTimeInMilliseconds,
      );
      const demurrageRate = `$${DisplayCurrencyValue(
        foundUnitRate.demurrage.rate,
      )}`;
      const demurrageIncrement = demurrageChargeIncrement(
        foundUnitRate.demurrage,
      );
      tooltipInfo = `Demurrage applies after ${appliesAfter} (${demurrageRate} per ${demurrageIncrement})`;
    }

    return {
      id: uuidv4(),
      service: index === 0 ? serviceCode : '',
      zone: zoneDescription,
      rate: rateString,
      quantity: `x ${unitCount}`,
      quantityValue: unitCount,
      calculation: combinedCalculationStr,
      amount: zoneTotal,
      tooltipInfo,
    };
  });
}
/**
 * Returns the rows for the table for a ZONE TO ZONE rate type job. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param rateTableItem - The applicable rate table item
 * @param pudItems - The PUD items from the job
 * @param zoneToZoneRateData - The distance rate data from job finished data
 * @param serviceCode - The jobs service code
 */
function returnTableRowsFromZoneToZoneRateData(
  rateTableItem: RateTableItems,
  zoneToZoneRateData: ZoneToZoneRateData,
  serviceCode: string,
  variancePct: number | null | undefined,
): TableData[] {
  const rows: TableData[] = [];

  if (
    !isZoneToZoneRateTypeObject(
      rateTableItem.rateTypeId,
      rateTableItem.rateTypeObject,
    )
  ) {
    return [];
  }

  const zoneToZoneRateType: ZoneToZoneRateType[] = rateTableItem.rateTypeObject;

  zoneToZoneRateType.forEach((rateType, index) => {
    // Calculate the zone rate if any rate variation applies
    const rateWithVariation =
      rateType.rate + getPercentageOf(rateType.rate, variancePct ?? 0);

    rows.push({
      id: uuidv4(),
      service: index === 0 ? serviceCode : '',
      zone: returnZoneToZoneName(rateType),
      rate: `$${DisplayCurrencyValue(rateWithVariation)}`,
      quantity: demurrageAppliesLabel(rateType.demurrage.appliedDemurrageId),
      quantityValue: 1,
      calculation: `$${DisplayCurrencyValue(rateWithVariation)}`,
      amount: `$${DisplayCurrencyValue(rateWithVariation)}`,
      tooltipInfo: '',
    });
  });

  // Add totals row
  rows.push({
    id: uuidv4(),
    service: '',
    zone: '',
    rate: '',
    quantity: '',
    quantityValue: 1,
    calculation: 'FREIGHT TOTAL',
    amount: `$${zoneToZoneRateData.chargeExclGst}`,
    tooltipInfo: '',
  });

  return rows;
}

/**
 * Returns the rows for the table for any applicable outside metro charges. If
 * outside metro charges were applicable, returns a list containing one element
 * (the outside metro row). If not, returns an empty list.
 * @param accounting - The job accounting info, containing additional charge
 * info
 */
function returnOutsideMetroChargeRows(
  accounting: JobAccountingDetails,
): TableData[] {
  try {
    if (
      !accounting.clientRates[0].outsideMetroRate ||
      !accounting.totals.subtotals.outsideMetroChargeTotals.client
    ) {
      return [];
    }
    const rate = accounting.clientRates[0].outsideMetroRate;
    const suburbCount = props.pudItems.filter(
      (pudItem) => pudItem.isOutsideMetro,
    ).length;
    return [
      {
        id: uuidv4(),
        service: 'Outside Metro Charge',
        zone: '',
        rate: `$${rate}%`,
        quantity: `${suburbCount} suburb(s)`,
        quantityValue: 1,
        calculation: `$${DisplayCurrencyValue(
          accounting.totals.subtotals.freightChargeTotals.client -
            accounting.totals.subtotals.outsideMetroChargeTotals.client,
        )} x ${rate}%`,
        amount: `$${accounting.totals.subtotals.outsideMetroChargeTotals.client}`,
      },
    ];
  } catch (e) {
    return [
      {
        id: uuidv4(),
        service: 'Error',
        zone: 'Outside Metro',
        rate: '',
        quantity: '',
        quantityValue: 0,
        calculation: '',
        amount: e instanceof Error ? e.message : 'Unknown error',
      },
    ];
  }
}

/**
 * Returns the rows for the table for any applicable additional charge items.
 * Returns a list of TableData objects, each of which will form a row in the
 * table.
 * @param accounting - The job accounting info, containing additional charge
 * info
 */
function returnAdditionalChargeRows(
  accounting: JobAccountingDetails,
  types: AdditionalChargeApplicationType[],
): TableData[] {
  try {
    const tollChargeTypeId = useRootStore().tollChargeTypeId;

    const applicableChargeItems =
      accounting.additionalCharges.chargeList.filter((charge) => {
        return (
          types.includes(charge.client.appliesTo) &&
          !!charge.client.charge &&
          charge.client.charge > 0 &&
          charge.quantity > 0
        );
      });
    const sortedApplicableChargeItems = reorderAdditionalChargeItems(
      applicableChargeItems,
      tollChargeTypeId ?? '',
      tollAdminAndHandlingId.value ?? '',
    );

    const rows: TableData[] = sortedApplicableChargeItems.map((charge) => {
      const foundChargeSubtotal =
        accounting.totals.subtotals.additionalChargeItems
          .flatMap((subtotal) => subtotal.items ?? [])
          .find((subtotal) => subtotal.chargeRef === charge._id);

      // Only show increment buttons for FIXED charge types
      const allowIncrement =
        charge.client.chargeBasis === AdditionalChargeRateBasis.FIXED;
      let chargeValue =
        charge.client.chargeBasis === AdditionalChargeRateBasis.FIXED
          ? `$${DisplayCurrencyValue(charge.client.charge)}`
          : `${DisplayCurrencyValue(charge.client.charge)}%`;

      let calculation = `${chargeValue} x ${charge.quantity}`;

      // If the additional charge item is a toll admin and handling charge, we
      // should display the percentage value from the charge in the store in
      // the rate columns
      if (charge._id === tollAdminAndHandlingId.value) {
        const tollAndHandlingItem =
          useRootStore().additionalChargeItemList.find(
            (x: AdditionalChargeItem) =>
              x._id === useRootStore().tollAdminAndHandlingId,
          );

        // Using the tollAdminAndHandlingId and tollChargeTypeId, we can
        // calculate the values that went into the calculations for toll admin
        // and handling
        if (tollAndHandlingItem?._id && tollChargeTypeId) {
          chargeValue = `${DisplayCurrencyValue(
            tollAndHandlingItem.client.charge,
          )}%`;
          const tollChargeSubtotal = computeTollChargeItemSubtotal(
            applicableChargeItems,
            tollChargeTypeId,
            tollAndHandlingItem?._id,
          );
          calculation = `${chargeValue} x $${DisplayCurrencyValue(
            tollChargeSubtotal,
          )}`;
        }
      }

      return {
        id: uuidv4(),
        service: charge.longName,
        zone: '',
        rate: chargeValue,
        quantity: `x ${charge.quantity}`,
        quantityValue: charge.quantity,
        additionalChargeId: charge._id,
        allowIncrement: allowIncrement,
        calculation: calculation,
        amount: `$${DisplayCurrencyValue(
          foundChargeSubtotal?.total.client ?? 0,
        )}`,
      };
    });

    return rows;
  } catch (e) {
    return [
      {
        id: uuidv4(),
        service: 'Error',
        zone: '',
        rate: '',
        quantity: e instanceof Error ? e.message : 'Unknown error',
        quantityValue: 0,
        calculation: '',
        amount: '',
      },
    ];
  }
}

const tollAdminAndHandlingId: ComputedRef<string | undefined> = computed(() => {
  return useRootStore().tollAdminAndHandlingId;
});

/**
 * Emits an event to update an additional charge item. The event is picked up by
 * the parent component to update the additional charge item.
 */
function updateAdditionalChargeItem(update: UpdateAdditionalChargeItem) {
  const isValid = validateAdditionalChargeUpdate(update);
  if (!isValid) {
    return;
  }
  emit('updateAdditionalCharge', update);
}

/**
 * Returns the rows for the table for any applicable fuel charge. Returns TableData object, each of which will form a row in the table.
 * @param accounting - The job accounting info, containing fuel levy info
 */
function returnFuelRow(accounting: JobAccountingDetails): TableData {
  try {
    const isFuelApplied = accounting.clientRates[0].rate.isClientFuelApplied;
    const fuelRate = isFuelApplied
      ? accounting.additionalCharges.clientFuelSurcharge
          ?.appliedFuelSurchargeRate ?? 0
      : 0;
    return {
      id: uuidv4(),
      service: 'Fuel',
      zone: '',
      rate: `${fuelRate}%`,
      quantity: 'Fuel Surcharge',
      quantityValue: 1,
      calculation: isFuelApplied ? `${fuelRate}%` : 'N/A',
      amount: `$${accounting.totals.subtotals.fuelSurcharges.client}`,
    };
  } catch (e) {
    return {
      id: uuidv4(),
      service: 'Error',
      zone: '',
      rate: '',
      quantity: '',
      quantityValue: 0,
      calculation: '',
      amount: e instanceof Error ? e.message : 'Unknown error',
    };
  }
}

/**
 * Returns the data for the table, including all rows and totals. Combines all
 * the different row types into a single array, as well as adding rows for the
 * totals at the end.
 */
const tableData: ComputedRef<TableData[]> = computed(() => {
  const clientRateRows = returnClientRateTableRow(jobAccountingDetails.value);
  return [
    ...clientRateRows,
    ...returnAdditionalChargeRows(jobAccountingDetails.value, [
      AdditionalChargeApplicationType.FREIGHT,
    ]),
    ...returnOutsideMetroChargeRows(jobAccountingDetails.value),
    returnFuelRow(jobAccountingDetails.value),
    ...returnAdditionalChargeRows(jobAccountingDetails.value, [
      AdditionalChargeApplicationType.NON_FREIGHT,
      AdditionalChargeApplicationType.CUSTOM,
    ]),
    {
      id: uuidv4(),
      service: 'Total Ex GST',
      zone: '',
      rate: '',
      quantity: '',
      quantityValue: 1,
      calculation: 'Includes fuel',
      amount: `$${jobAccountingDetails.value.totals.subtotals.lessGst.client}`,
    },
    {
      id: uuidv4(),
      service: 'Total Inc GST',
      zone: '',
      rate: '',
      quantity: '',
      quantityValue: 1,
      calculation: 'ESTIMATED',
      amount: `$${jobAccountingDetails.value.totals.finalTotal.client}`,
    },
  ].filter((item) => isAuthorisedAdmin.value || item.service !== 'Error');
});

const isAuthorisedAdmin: ComputedRef<boolean> = computed(() => {
  return hasAdminRole();
});
</script>
<style scoped lang="scss">
.pricing-summary-table {
  width: 100%;
  border-collapse: collapse;
  font-family: $font-sans;
  color: var(--text-color);
  background-color: var(--background-color-300);

  tbody {
    tr {
      position: relative;

      &.reduce-padding {
        td {
          padding: 1px 8px;

          .shrink-input-field {
            width: 100px;
            min-width: 0;
            max-width: 120px;
          }
        }
      }

      &:last-child {
        td {
          color: var(--primary);
        }
      }

      &:hover {
        .tooltip-container {
          opacity: 1;
        }
      }
      td {
        padding: 10px 8px;
        border-bottom: 2px solid var(--background-color-500);
        font-size: 16px;
        color: var(--text-color);
        vertical-align: middle;
        font-weight: 500;
        letter-spacing: 0.5px;

        &:first-child {
          letter-spacing: 0px;
          font-weight: 700;
          color: var(--bg-light);
        }

        .error-chip {
          background-color: #ff4d4d; /* Red background for error chips */
          color: var(--text-color); /* White text */
          padding: 2px 6px;
          border-radius: 4px;
          text-transform: uppercase;
          font-size: $font-size-12;
          font-weight: 600;
        }

        .quantity-container {
          display: flex;
          justify-content: start;
          align-items: center;
          gap: 4px;
          .quantity-text {
            padding-right: 5px;
            line-height: 19px;
          }
          .quantity-amount {
            padding-right: 5px;
          }

          .quantity-up,
          .quantity-down {
            cursor: pointer;
            color: var(--light-text-color); /* Light grey for icons */
            transition: color 0.2s;
            position: relative;
            // top: -2px;
            &:hover {
              color: #2196f3;
              transition: 0.1s;
            }
          }
        }
      }

      .tooltip-container {
        opacity: 0;
        position: absolute;
        right: 0px;
        top: 50%;
        transform: translateY(-50%) translateX(90%);
      }

      .pl-2 {
        padding-left: 8px;
      }
    }
  }

  td {
    &:last-child {
      letter-spacing: 1px;
      font-weight: 700;
      text-align: right;
      font-size: 17px;
    }
  }
}

.info-txt {
  margin-top: 18px;
  display: block;
  color: var(--light-text-color);
  text-align: center;
}

// Responsive Styles
@media (max-width: 1500px) {
  .pricing-summary-table {
    tbody {
      tr {
        td {
          font-size: $font-size-12;
          .error-chip {
            font-size: $font-size-10;
          }
          .quantity-container {
            .quantity-amount {
              font-size: $font-size-12;
            }
          }
        }
      }
    }
    td {
      &:last-child {
        font-size: $font-size-12;
      }
    }
  }
}

@media (max-width: 1024px) {
  .pricing-summary-table {
    tbody {
      tr {
        td {
          font-size: $font-size-10;
          .error-chip {
            font-size: 8px;
          }
          .quantity-container {
            .quantity-amount {
              font-size: $font-size-10;
            }
          }
        }
      }
    }
    td {
      &:last-child {
        font-size: $font-size-10;
      }
    }
  }
}
</style>
