<template>
  <v-select
    v-if="inputType === 'SELECT'"
    prefix="Fuel: "
    v-model="inputModel"
    :items="selectItems"
    item-text="longName"
    item-value="bracketId"
    :disabled="isFormDisabled"
    class="v-solo-custom fuel-surcharge-select"
    solo
    flat
    label="Select Fuel Surcharge"
    :hide-details="!showLongName"
    :hint="currentSelectedItem?.hintText"
    persistent-hint
  >
    <template v-slot:selection="data">
      <span class="selection-truncate">
        {{ data.item.shortName }}
      </span>
    </template>
    <template v-slot:item="data">
      {{ data.item.longName }}
    </template>
    <template v-slot:append-outer>
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-btn flat v-on="on" icon @click="inputModel = ''" class="ma-0">
            <v-icon color="grey lighten-1">close</v-icon>
          </v-btn>
        </template>
        Restore to Default
      </v-tooltip>
    </template>
  </v-select>
  <span v-else>
    <v-tooltip left>
      <template v-slot:activator="{ on }">
        <v-icon
          v-on="on"
          @click="isDialogOpen = true"
          class="edit-break-icon"
          icon
          small
          text
        >
          {{ !isFormDisabled ? `fal fa-edit` : `fal fa-search` }}
        </v-icon>
      </template>
      Edit Fuel Surcharge
    </v-tooltip>
    <ContentDialog
      :showDialog.sync="isDialogOpen"
      title="Fuel Surcharge Selection"
      width="60%"
      contentPadding="pa-0"
      @cancel="isDialogOpen = false"
      @confirm="confirmSelectedRow"
      :showActions="true"
      :isConfirmUnsaved="false"
      :isDisabled="!_selectedTableRowId"
      :isLoading="false"
      confirmBtnText="Confirm"
    >
      <v-layout>
        <v-flex md12 class="body-scrollable--75 pa-3">
          <v-layout align-center class="banner-custom">
            <v-layout column>
              <h3>Edit Applied Fuel Surcharge</h3>
              <h4>Select a fuel surcharge from the table below.</h4>
            </v-layout>
            <span class="tags-chip mr-1" v-if="jobRangeData.planned">
              Estimated: {{ DisplayCurrencyValue(jobRangeData.planned) }}km
            </span>
            <span class="tags-chip" v-if="jobRangeData.suburbCentres">
              Suburb Centres:
              {{ DisplayCurrencyValue(jobRangeData.suburbCentres) }}km
            </span>
            <span class="tags-chip mr-1" v-if="jobRangeData.gpsEstimate">
              GPS: {{ DisplayCurrencyValue(jobRangeData.gpsEstimate) }}km
            </span>
          </v-layout>
          <v-divider class="my-2"></v-divider>
          <table class="simple-data-table">
            <thead>
              <tr>
                <th>Fuel Name</th>
                <th>Charge Scheme</th>
                <th>From</th>
                <th>To</th>
                <th>Brackets</th>
                <th>Rate</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in selectItems"
                :key="item.bracketId"
                :class="{
                  'row-selectable': !isFormDisabled,
                  'row-selected': _selectedTableRowId === item.bracketId,
                }"
                @click="_selectedTableRowId = item.bracketId"
              >
                <td>{{ item.tableName }}</td>
                <td>{{ item.chargeScheme }}</td>
                <td>
                  {{ item.validFrom }}
                </td>
                <td>
                  {{ item.validTo }}
                </td>
                <td>
                  {{ item.bracketDescription }}
                </td>
                <td>
                  {{ item.rate }}
                </td>
              </tr>
            </tbody>
          </table>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </span>
</template>

<script setup lang="ts">
interface SelectItem {
  fuelId: string;
  bracketId: string;
  shortName: string;
  longName: string;

  // Table properties
  tableName: string;
  chargeScheme: string;
  hintText: string;
  bracketDescription: string;
  rate: string;
  validFrom: string;
  validTo: string;
}
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  describeFuelSurchargeBracket,
  validateFuelSurcharge,
} from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import { FuelLevyChargeBasis } from '@/interface-models/ServiceRates/FuelSurcharge/FuelLevyChargeBasis';
import { FuelSurchargeRate } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import {
  FuelSurchargeType,
  returnReadableFuelSurchargeType,
} from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeType';
import { RangedFlexRate } from '@/interface-models/ServiceRates/FuelSurcharge/RangedFlexRate';
import {
  computed,
  ComputedRef,
  onBeforeMount,
  Ref,
  ref,
  WritableComputedRef,
} from 'vue';
import { returnReadableRangeDeterminant } from '@/interface-models/ServiceRates/FuelSurcharge/RangeDeterminant';
import { JobRangeDeterminantValues } from '@/interface-models/ServiceRates/FuelSurcharge/JobRangeDeterminantValues';
import { ClientFuelSurchargeRate } from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import { FleetAssetFuelSurchargeRate } from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';

const props = defineProps<{
  inputType: 'DIALOG' | 'SELECT';
  selectedFuelSurchargeId: string;
  selectedBracketId: string;
  items: FuelSurchargeRate[];
  isFormDisabled: boolean;
  jobRangeData: JobRangeDeterminantValues;
}>();

const emit = defineEmits<{
  (event: 'updateSelection', payload: string): void;
}>();

const _selectedBracketId: Ref<string> = ref('');
const selectItems: Ref<SelectItem[]> = ref([]);

const isDialogOpen: Ref<boolean> = ref(false);
const _selectedTableRowId: Ref<string | null> = ref(null);

const inputModel: WritableComputedRef<string> = computed({
  get(): string {
    return _selectedBracketId.value;
  },
  set(value: string): void {
    if (value === _selectedBracketId.value) {
      return; // No change, do nothing
    }
    console.log(
      `Selected bracket changed from ${_selectedBracketId.value} to ${value}`,
    );
    // Emit the input event to update the parent component
    emit('updateSelection', value);
    _selectedBracketId.value = value;
  },
});

/**
 * Returns the currently selected item based on the inputModel value. Used in
 * the template to display a summary of the selected value as hintText
 */
const currentSelectedItem: ComputedRef<SelectItem | undefined> = computed(
  () => {
    return selectItems.value.find(
      (item) => item.bracketId === inputModel.value,
    );
  },
);

/**
 * Used on the click event of the confirm button. Sets the inputModel to the selected row id
 * and closes the dialog.
 */
function confirmSelectedRow(): void {
  if (!_selectedTableRowId.value) {
    return;
  }
  inputModel.value = _selectedTableRowId.value;
  isDialogOpen.value = false;
}

const showLongName: ComputedRef<boolean> = computed(() => {
  return props.selectedBracketId !== '';
});

/**
 * Transforms an array of FuelSurchargeRate items into a flat array of select options.
 * Each option represents a fuel surcharge bracket with a descriptive label.
 *
 * @param fuelSurchargeList - Array of FuelSurchargeRate objects to process.
 * @returns Array of objects containing id, bracketId, and longName for select dropdown.
 */
function returnSelectItems(
  fuelSurchargeList: FuelSurchargeRate[],
): SelectItem[] {
  return fuelSurchargeList
    .filter((fuel) => validateFuelSurcharge(fuel) === true)
    .flatMap((fuel) => {
      // Ensure rateBrackets is an array
      if (!Array.isArray(fuel.rateBrackets)) {
        logConsoleError(
          `Fuel surcharge item ${fuel.name} has invalid rateBrackets`,
          fuel.rateBrackets,
        );
        return [];
      }
      // Format validity dates
      const validFromReadable = fuel.validFromDate
        ? returnFormattedDate(fuel.validFromDate)
        : '';
      const validToReadable = fuel.validToDate
        ? returnFormattedDate(fuel.validToDate)
        : '';
      const dateRangeReadable = [validFromReadable, validToReadable]
        .filter(Boolean)
        .join(' - ');
      const chargeScheme =
        fuel.fuelSurchargeApplicationType === FuelSurchargeType.CONSTANT
          ? returnReadableFuelSurchargeType(fuel.fuelSurchargeApplicationType)
          : `${returnReadableFuelSurchargeType(
              fuel.fuelSurchargeApplicationType,
            )} - ${returnReadableRangeDeterminant(fuel.rangeDeterminant!)}`;

      return fuel.rateBrackets.map((bracket: RangedFlexRate, index: number) => {
        // Describe determinant and bracket
        const bracketDescription = fuel.rangeDeterminant
          ? describeFuelSurchargeBracket(bracket, fuel.rangeDeterminant)
          : 'N/A';

        // Format rate string
        const ratePrefix =
          bracket.chargeType === FuelLevyChargeBasis.FIXED_CHARGE ? '$' : '';
        const rateSuffix =
          bracket.chargeType === FuelLevyChargeBasis.PERCENTAGE ? '%' : '';
        const rate = `${ratePrefix}${DisplayCurrencyValue(
          bracket.rate,
        )}${rateSuffix}`;

        const hintText =
          fuel.fuelSurchargeApplicationType === FuelSurchargeType.CONSTANT
            ? `Locked to selection: ${rate}`
            : `Locked to selection: ${rate} (bracket ${bracketDescription})`;

        const rateDescription =
          fuel.fuelSurchargeApplicationType === FuelSurchargeType.CONSTANT
            ? rate
            : `${bracketDescription} @ ${rate}`;

        let isDivisionRate = false;
        if (fuel instanceof ClientFuelSurchargeRate) {
          isDivisionRate = fuel.isDivisionRate;
        } else if (fuel instanceof FleetAssetFuelSurchargeRate) {
          isDivisionRate = fuel.isDivisionRate;
        }

        const fuelName = isDivisionRate
          ? `Default Rate: ${fuel.name}`
          : fuel.name;
        // Build select item
        return {
          fuelId: fuel.id!,
          bracketId: bracket.bracketId,
          shortName: `${fuelName} - ${fuel.rateDescription}`,
          longName: `${fuelName} | ${rateDescription} | ${dateRangeReadable}`,
          // Table properties
          tableName: index === 0 ? fuel.name : '',
          chargeScheme: index === 0 ? chargeScheme : '',
          hintText: hintText,
          rate: rate,
          validFrom: index === 0 ? validFromReadable : '',
          validTo: index === 0 ? validToReadable : '',
          bracketDescription: bracketDescription,
        };
      });
    });
}

onBeforeMount(() => {
  selectItems.value = returnSelectItems(props.items);
  _selectedBracketId.value = props.selectedBracketId;

  // If the selectedBracketId is not set (ie not set in the parent), it means
  // the user has not manually changed the selection yet. In this case we set
  // the local value to be the first item in the selectItems list that matches
  // the selectedFuelSurchargeId.
  if (!_selectedBracketId.value && !!props.selectedFuelSurchargeId) {
    const foundItem = selectItems.value.find(
      (item) => item.fuelId === props.selectedFuelSurchargeId,
    );
    if (foundItem) {
      _selectedBracketId.value = foundItem.bracketId;
    }
  }
});
</script>
<style scoped lang="scss">
.edit-break-icon {
  margin-left: 14px;
  border-radius: 100px;
  background-color: var(--background-color-100);
  color: $warning !important;
  transition: all 0.3s;

  &:hover {
    scale: 1.4;
  }
}
.fuel-surcharge-select {
  max-width: 500px;
}
.selection-truncate {
  max-width: 400px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
