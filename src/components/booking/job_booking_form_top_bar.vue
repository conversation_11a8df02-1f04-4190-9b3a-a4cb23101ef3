<template>
  <div
    :class="
      sessionManager.getPortalType() === Portal.CLIENT
        ? 'top-bar client-portal'
        : 'top-bar'
    "
  >
    <v-layout wrap mt-2 mb-2 pl-3 pr-3 align-center style="width: 100%">
      <v-flex xs2 md2>
        <v-btn
          outline
          class="v-btn-cancel"
          color="error"
          @click="emit('cancel')"
        >
          Cancel
        </v-btn>
      </v-flex>

      <v-flex xs6 md6 class="top-bar-title-container">
        <span class="top-bar-title-txt">
          {{ isNewJob ? '' : 'Job' }}
          <span
            class="big"
            :class="
              recurrenceType === JobRecurrenceType.PERMANENT ? 'permanent' : ''
            "
          >
            {{
              jobDetails.jobId
                ? `# ${jobDetails.displayId}`
                : recurrenceType === JobRecurrenceType.PERMANENT
                  ? 'New Permanent Job'
                  : 'New Job'
            }}
          </span>
          <span class="client">
            {{ jobDetails.client.id }} -
            {{ jobDetails.client.clientName }}
          </span>
        </span>
      </v-flex>

      <v-flex xs4 md4 class="d-flex justify-end align-center">
        <v-btn
          v-if="
            recurrenceType === JobRecurrenceType.ADHOC &&
            !isBookingFromQuote &&
            !jobDetails.jobId
          "
          depressed
          :loading="awaitingSaveResponse"
          class="v-btn-quote"
          :disabled="
            !isSaveButtonEnabled || jobDetails.additionalJobData?.appliedQuoteId
          "
          @click="emit('saveQuote')"
        >
          Save Quote
        </v-btn>
        <div class="v-btn-save">
          <ConfirmationDialog
            :buttonText="
              isNewJob
                ? recurrenceType === JobRecurrenceType.PERMANENT
                  ? 'Book Permanent Job'
                  : 'Book Job'
                : 'Save Job'
            "
            :message="jobReviewConfirmationMessage"
            :isBlockButton="true"
            :isDepressedButton="true"
            title="Confirm Job"
            @confirm="emit('confirmJobSave', $event)"
            :buttonDisabled="!isSaveButtonEnabled"
            :isOutlineButton="false"
            :isClientPortal="sessionManager.isClientPortal()"
            confirmationButtonText="Save Job"
            :isCheckbox="true"
            :isLoading="awaitingSaveResponse"
            :checkboxLabel="'Approve job'"
            :dialogIsActive="jobDetails.requiresApproval"
          />
        </div>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import type { ClientDetails } from '@/interface-models/Client/ClientDetails/ClientDetails';
import { Portal } from '@/interface-models/Generic/Portal';
import type JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobRecurrenceType } from '@/interface-models/Jobs/JobRecurrenceType';
import { JobSourceType } from '@/interface-models/Jobs/JobSourceType';
import { sessionManager } from '@/store/session/SessionState';
import { ComputedRef, computed, defineEmits, defineProps } from 'vue';

// Props from the parent component
const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    selectedClientDetails: ClientDetails;
    awaitingSaveResponse: boolean;
    recurrenceType: JobRecurrenceType;
  }>(),
  {
    awaitingSaveResponse: false,
    recurrenceType: JobRecurrenceType.ADHOC,
  },
);

// Emits events back to the parent
const emit = defineEmits<{
  (event: 'cancel'): void;
  (event: 'saveQuote'): void;
  (event: 'confirmJobSave', payload: boolean): void;
}>();

/**
 * Determines if the save button should be enabled. The save button should be
 * enabled if the job has at least one PUD item, a service type, a rate type,
 * client rates, and a client selected.
 */
const isSaveButtonEnabled: ComputedRef<boolean> = computed(() => {
  return (
    props.jobDetails.pudItems.length > 0 &&
    !!props.jobDetails.serviceTypeId &&
    !!props.jobDetails.rateTypeId &&
    !!props.jobDetails.accounting?.clientRates?.length &&
    (!!props.selectedClientDetails || props.jobDetails.isCashSale)
  );
});

/**
 * Returns a message to display when a job approval is required. Passed into the
 * top bar component and displayed in the confirmation dialog.
 */
const jobReviewConfirmationMessage: ComputedRef<string> = computed(() => {
  // If the job requires review we will present a confirmation dialog to the user before saving the job. This method returns the message that will be displayed.
  const bookedByClient: boolean = props.jobDetails.statusList.includes(57);
  const isCompletedActionRequired: boolean =
    props.jobDetails.statusList.includes(45);
  if (!bookedByClient && !isCompletedActionRequired) {
    return '';
  }
  let message: string = '';
  if (bookedByClient) {
    if (props.jobDetails.jobSourceType === JobSourceType.CLIENT) {
      message +=
        'This job was booked in by ' +
        props.jobDetails.eventList[0].editedBy +
        ' via the client portal. ';
    } else if (
      props.jobDetails.jobSourceType === JobSourceType.IMPORT ||
      props.jobDetails.jobSourceType === JobSourceType.API
    ) {
      message += 'This job was booked in via electronic data interchange. ';
    }
  }
  message +=
    'Please confirm that you have checked the job and are ready for it to be approved.';
  return message;
});

/**
 * If appliedQuoteId in the job is defined, it means we are booking from a
 * quote. Used in template to disable certain actions.
 */
const isBookingFromQuote: ComputedRef<boolean> = computed(() => {
  return !!props.jobDetails.additionalJobData?.appliedQuoteId;
});

const isNewJob: ComputedRef<boolean> = computed(() => {
  return !props.jobDetails.jobId;
});
</script>

<style scoped lang="scss">
.top-bar {
  font-family: $sub-font-family;
  &.client-portal {
    position: fixed;
    top: 88px !important;
    left: 45px !important;
    left: 0px !important;
    width: 98%;
  }
  position: fixed;
  align-items: center;
  justify-items: center;
  top: 40px;
  left: 45px;
  right: 0;
  width: 70%;
  z-index: 100;
  border-radius: 0 40px 40px 0;
  background: var(--background-color-200);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--background-color-500);
  overflow: hidden;
  .top-bar-title-container {
    display: flex;
    align-content: center;
    justify-content: space-evenly;
    .top-bar-title-txt {
      color: var(--light-text-color);
      font-size: $font-size-18;
      font-weight: 700;
      .big {
        background-color: var(--primary-dark);
        padding: 3px 6px;
        margin-right: 6px;
        border-radius: 6px;
        color: white;

        &.permanent {
          background-color: var(--accent) !important;
          color: $app-dark-primary-200;
        }
      }
      .client {
        color: var(--text-color);
        margin-left: 18px;
        margin-right: 18px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .v-btn {
    font-weight: 700;
    border-radius: $border-radius-base;
  }

  .v-btn-cancel {
    float: left;
  }

  .v-btn-quote {
    background-color: var(--primary) !important;
    height: 38px;
    margin-right: 12px;
    color: white !important;
  }

  .v-flex {
    display: flex;
    align-items: center;
  }
}

.v-btn-save {
  justify-content: center !important;
  align-content: stretch !important;
  margin-bottom: 9px;
  max-height: 38px;
  margin-left: 4px;
  margin-right: 14px;
}

// Responsive Styles
@media (max-width: 1500px) {
  .top-bar-title-txt {
    font-size: $font-size-15 !important;
  }
}

@media (max-width: 1200px) {
  .top-bar-title-txt {
    font-size: $font-size-12 !important;
  }
  .v-btn {
    font-size: small !important;
  }
}
</style>
