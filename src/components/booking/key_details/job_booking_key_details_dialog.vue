<template>
  <v-flex>
    <v-progress-linear
      v-if="awaitingClientData"
      height="2"
      rounded
      color="orange"
      indeterminate
      ma-4
    ></v-progress-linear>
    <v-layout>
      <v-flex md12 class="form-container">
        <v-form ref="form">
          <v-layout wrap justify-space-between>
            <v-flex class="section" align-center justify-start>
              <v-layout wrap>
                <!-- ONLY SHOW CLIENT INPUTS IF NO CLIENT ID PASSED, MEANING WE'RE ON
                CLIENT PORTAL -->
                <h6>Client Details</h6>
                <v-flex md12 v-if="!sessionManager.isClientPortal()">
                  <v-layout>
                    <v-flex md12 mb-2>
                      <v-autocomplete
                        id="client-job-select"
                        outline
                        v-model="clientShort.id"
                        :items="clientSelectList"
                        item-value="clientId"
                        item-text="clientSearchCriteria"
                        label="Client Select"
                        @change="trySetClientDetails"
                        :rules="[validate.required]"
                        class="v-solo-custom form-field-required"
                        browser-autocomplete="off"
                        auto-select-first
                        :disabled="
                          pudItemCount > 0 || awaitingClientData || !canEditForm
                        "
                        :allow-overflow="false"
                        hide-selected
                        color="orange"
                        :search-input.sync="search"
                        :filter="customFilter"
                        cache-items
                        hide-details
                        @update:search-input="trimInput"
                        :append-outer-icon="clientShort.id ? 'close' : ''"
                        @click:append-outer="clearClient"
                      >
                        <template v-slot:selection="data">
                          <span>{{ data.item.clientDisplayName.trim() }}</span>
                          <span
                            v-if="
                              data.item.statusList.includes(13) ||
                              data.item.creditStatus === 2 ||
                              data.item.statusList.includes(47)
                            "
                            class="px-2"
                            >|</span
                          >
                          <span
                            :style="{
                              color: data.item.statusList.includes(13)
                                ? '#ff5252'
                                : data.item.statusList.includes(47) ||
                                    data.item.creditStatus === 2
                                  ? '#fb8c00'
                                  : '#FFFFFF',
                            }"
                            v-if="
                              data.item.statusList.includes(13) ||
                              data.item.creditStatus === 2 ||
                              data.item.statusList.includes(47)
                            "
                            >{{
                              getClientStatus(
                                data.item.statusList,
                                data.item.creditStatus,
                              )
                            }}
                            ></span
                          >
                        </template>
                        <template v-slot:item="data">
                          <span>{{ data.item.clientDisplayName.trim() }} </span>
                          <span
                            v-if="
                              data.item.statusList.includes(13) ||
                              data.item.creditStatus === 2 ||
                              data.item.statusList.includes(47)
                            "
                            class="px-2"
                            >|</span
                          >
                          <span
                            :style="{
                              color: data.item.statusList.includes(13)
                                ? '#ff5252'
                                : data.item.statusList.includes(47) ||
                                    data.item.creditStatus === 2
                                  ? '#fb8c00'
                                  : '#FFFFFF',
                            }"
                            v-if="
                              data.item.statusList.includes(13) ||
                              data.item.creditStatus === 2 ||
                              data.item.statusList.includes(47)
                            "
                            >{{
                              getClientStatus(
                                data.item.statusList,
                                data.item.creditStatus,
                              )
                            }}
                            ></span
                          >
                        </template>
                      </v-autocomplete>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12 v-else>
                  <v-layout>
                    <v-flex md12 mb-2 class="client-name">{{
                      props.clientDetails?.clientName
                    }}</v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12 v-if="isCashSale" class="input-container" mb-2>
                  <CashSalesDetails
                    ref="cashSalesDetailsRef"
                    :clientDetails="cashSaleClientDetails"
                    :validate="validate"
                    :proofOfDelivery="proofOfDelivery"
                    :cashSaleClientExpansionPanelOpen="
                      cashSaleClientExpansionPanelOpen
                    "
                    @setCashSaleExpansionPanel="
                      cashSaleClientExpansionPanelOpen = $event
                    "
                    :cashSalesDialog="false"
                  />
                </v-flex>
                <v-layout row wrap class="mb-4">
                  <v-flex xs6 d-flex v-if="!sessionManager.isClientPortal()">
                    <RecentBookedJobsDialog
                      ref="recentJobDialogRef"
                      v-if="isNewJob"
                      :key="clientShort.id"
                      :clientDetails="selectedClientDetails"
                      @applyJobId="bookFromRecentJob"
                      @applyNewJobDate="setSelectedJobDate"
                      @applyFirstPudArrival="setFirstPudArrival"
                      :disabled="
                        !clientShort.id ||
                        awaitingClientData ||
                        appliedQuoteId !== null
                      "
                    />
                  </v-flex>

                  <v-flex xs6 d-flex>
                    <JobBookingQuoteSelect
                      v-if="isNewJob"
                      :key="clientShort.id"
                      :clientKey="clientShort.id ?? ''"
                      :appliedQuoteId="appliedQuoteId ?? ''"
                      :quoteDetailsList="clientBookingData?.quoteDetails ?? []"
                      @applyQuoteId="applyQuoteId"
                      :disabled="
                        !selectedClientDetails ||
                        !clientBookingData?.quoteDetails?.length ||
                        appliedJobId !== null
                      "
                    />
                  </v-flex>
                  <v-flex
                    v-if="selectedClientDetails"
                    md12
                    class="selected-job-txt-container"
                  >
                    <span v-if="appliedQuoteId || appliedJobId" class="active">
                      {{
                        appliedQuoteId
                          ? `Quote Selected:  ${quoteIdPrefix}${appliedQuoteDetails?.quoteId}`
                          : 'Recent Job Selected: ' + appliedJobId
                      }}

                      <v-tooltip bottom>
                        <template #activator="{ on, attrs }">
                          <v-btn
                            v-bind="attrs"
                            v-on="on"
                            class="cancel-selected-job-btn"
                            icon
                            small
                            @click="clearSelectedJob"
                          >
                            <v-icon size="16">close</v-icon>
                          </v-btn>
                        </template>
                        <span>Clear Selected Job</span>
                      </v-tooltip>
                    </span>

                    <span v-else class="disabled"
                      >No RecentJob/Quote Selected</span
                    >
                  </v-flex>
                </v-layout>
                <v-flex
                  md12
                  v-if="!isCashSale && selectedClientDetails !== null"
                  class="input-container"
                  mb-2
                >
                  <ClientPersonDispatcherBookingSelect
                    :key="
                      selectedClientDetails
                        ? selectedClientDetails.clientId
                        : ''
                    "
                    ref="clientDispatcherSelect"
                    v-model="clientDispatcher"
                    :job_id="jobDetails._id"
                    :client_id="selectedClientDetails._id"
                    :clientId="selectedClientDetails.clientId"
                    :clientName="selectedClientDetails.displayName"
                    :defaultDispatcherId="
                      selectedClientDetails.defaultDispatcherId
                    "
                    :clientPersonIds="
                      selectedClientDetails.clientPersonDispatchers
                    "
                    :required="true"
                    :disabled="
                      awaitingClientData ||
                      !canEditForm ||
                      !selectedClientDetails
                    "
                  />
                </v-flex>
                <v-flex md12>
                  <h6 class="mt-3">Job Requirements</h6>
                  <v-layout justify-start align-start mb-1>
                    <DatePickerBasic
                      @setEpoch="setSelectedJobDate"
                      :hideIcon="false"
                      labelName="Select Job Booking Date"
                      :epochTime="selectedJobDate"
                      hint="Select Job Booking Date"
                      class="v-solo-custom"
                      :solo-input="true"
                      :hideDetails="true"
                      persistent-hint
                      :formDisabled="
                        awaitingClientData ||
                        !canEditForm ||
                        !selectedClientDetails
                      "
                    >
                    </DatePickerBasic>
                    <span
                      v-if="selectedClientDetails && !awaitingClientData"
                      class="date-warning"
                      :class="bookingDateDetails.class"
                    >
                      <v-icon size="22">{{ bookingDateDetails.icon }}</v-icon>
                      {{ bookingDateDetails.description }}
                    </span>
                  </v-layout>
                </v-flex>
                <v-flex md12 mt-1>
                  <v-select
                    v-model="additionalEquipmentList"
                    :items="equipmentTypes"
                    item-text="longName"
                    item-value="id"
                    label="Required Equipment"
                    multiple
                    small-chips
                    outline
                    solo
                    flat
                    class="v-solo-custom"
                    color="Orange"
                    :hide-details="true"
                    :disabled="
                      awaitingClientData ||
                      !canEditForm ||
                      !selectedClientDetails
                    "
                  >
                  </v-select>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-divider class="ma-3" inset vertical></v-divider>
            <v-flex class="section" align-center justify-end>
              <v-flex>
                <h6>Job References ({{ jobReferences.length }})</h6>
              </v-flex>
              <v-flex class="reference-section" md12 mt-1>
                <BookingReferences
                  :requirementsMet.sync="jobReferenceRequirementsMet"
                  :jobReferences="jobReferences"
                  :clientReferences="
                    selectedClientDetails
                      ? selectedClientDetails.references.mainJobScreen
                      : []
                  "
                  :readOnlyView="awaitingClientData || !selectedClientDetails"
                />
                <!-- :readOnlyView="
                    awaitingClientData ||
                    (jobDetails.pudItems.length >- 0 &&
                      !jobDetails.statusList.includes(57)) ||
                    jobDetails.fleetAssetId !== '' ||
                    jobDetails.driverId !== ''
                  " -->
              </v-flex>
            </v-flex>
          </v-layout>
        </v-form>
      </v-flex>
    </v-layout>
    <v-divider v-if="isDialogOpen"></v-divider>
    <v-layout v-if="canEditForm" wrap mt-2 mb-2 pl-2 pr-2>
      <template v-if="isNewJob">
        <v-btn flat solo class="action-btn" @click="clearClient">Clear</v-btn>
        <v-spacer></v-spacer>
        <v-btn
          solo
          :disabled="
            awaitingClientData || isBookingFromQuote || isBookingFromRecentJobs
          "
          color="blue"
          big
          class="action-btn mt-2"
          @click="applyChanges(JobRecurrenceType.PERMANENT)"
          v-if="!sessionManager.isClientPortal()"
          >Add Permanent Job Details</v-btn
        >
        <v-btn
          solo
          :disabled="awaitingClientData"
          color="green"
          class="action-btn add mt-2"
          @click="applyChanges(JobRecurrenceType.ADHOC)"
          >Add Job Delivery Information</v-btn
        >
      </template>
      <template v-else>
        <v-btn
          v-if="isDialogOpen"
          outline
          solo
          big
          color="error"
          class="action-btn cancel"
          @click="cancelChanges"
          >Cancel</v-btn
        >
        <v-spacer></v-spacer>
        <v-btn
          v-if="isDialogOpen"
          solo
          :disabled="awaitingClientData"
          color="blue"
          class="action-btn"
          @click="applyChanges()"
          >Apply Changes</v-btn
        >
      </template>
    </v-layout>
    <JobBookingClientNotesDialog
      v-if="selectedClientDetails && !jobDetails.jobId"
      :key="selectedClientDetails.clientId"
      :clientDetails="selectedClientDetails"
      @addNotesFromSelection="addNotesFromDialog"
    ></JobBookingClientNotesDialog>

    <ConfirmationDialog
      v-if="confirmDialogIsOpen"
      :requiresButtonToOpen="false"
      title="Unsaved changes"
      buttonText="Confirm"
      message="You have unsaved changes. Are you sure you want to discard them?"
      @confirm="confirmClearDialog"
      confirmationButtonText="Confirm"
      @closeDialog="cancelClearDialog"
      :dialogIsActive="true"
    />
  </v-flex>
</template>

<script setup lang="ts">
export interface JobKeyDetailsUpdate {
  clientDetails: ClientDetails;
  clientShort: ClientShort;
  clientDispatcher: ClientPerson;
  proofOfDelivery: ProofOfDelivery;
  additionalEquipmentList: number[];
  jobReferences: JobReferenceDetails[];
  jobNotes: Communication[];
  bookingData: ClientJobBookingData;
  jobDate: number;
  recurrenceType: JobRecurrenceType | null;
  cashSaleClientDetails: CashSaleClientDetails | null;
  quoteDetails?: QuoteDetails | null;
  jobDetails?: JobDetails | null;
  firstPudArrival: string;
}

import JobBookingClientNotesDialog from '@/components/booking/key_details/job_booking_client_notes_dialog.vue';
import JobBookingQuoteSelect from '@/components/booking/quote/job_booking_quote_select.vue';
import RecentBookedJobsDialog from '@/components/booking/recentJobs/recent_booked_jobs_dialog.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import BookingReferences from '@/components/operations/BookJob/booking_references/index.vue';
import CashSalesDetails from '@/components/operations/BookJob/cash_sales_details/index.vue';
import ClientPersonDispatcherBookingSelect from '@/components/operations/BookJob/dispatcher_booking_select/dispatcher_booking_select.vue';
import { initialiseClientDetails } from '@/helpers/classInitialisers/InitialiseClientDetails';
import {
  getClientNotesForJob,
  updateClientNoteBeforeAddingToJob,
} from '@/helpers/CommunicationHelpers/ClientNoteHelpers';
import {
  getUserLocale,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { quoteIdPrefix } from '@/helpers/JobBooking/JobBookingQuoteHelpers.ts';
import { addClientReferencesToJobReferences } from '@/helpers/JobBooking/JobReferenceHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ClientJobBookingData } from '@/interface-models/Booking/ClientJobBookingData';
import { ClientDetails } from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientPerson from '@/interface-models/Client/ClientDetails/ClientPerson/ClientPerson';
import { ClientSearchSummary } from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import ClientShort from '@/interface-models/Client/ClientDetails/ClientShort';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { equipmentTypes } from '@/interface-models/Generic/EquipmentTypes/EquipmentTypes';
import ProofOfDelivery from '@/interface-models/Generic/ProofOfDelivery/ProofOfDelivery';
import { AdditionalJobData } from '@/interface-models/Jobs/AdditionalJobData/AdditionalJobData';
import { CashSaleClientDetails } from '@/interface-models/Jobs/CashSalesDetails/CashSalesDetails';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobRecurrenceType } from '@/interface-models/Jobs/JobRecurrenceType';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import { QuoteDetails } from '@/interface-models/Jobs/Quote/QuoteDetails';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  nextTick,
  onMounted,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'applyChanges', payload: JobKeyDetailsUpdate): void;
  (event: 'clientDetailsSet', payload: ClientDetails | null): void;
}>();

// Expose getAndSetClientDetails, so that we can call it from the JobBooking
// component when booking a job with unassigned pud config
defineExpose({ getAndSetClientDetails, applyPropertiesFromQuoteSearch });

const props = withDefaults(
  defineProps<{
    clientId?: string | null;
    clientDetails?: ClientDetails | null;
    jobDetails: JobDetails;
    isDialogOpen: boolean;
    isNewJob?: boolean;
    jobDate?: number | null;
    // bookingData will be supplied when we're editing an existing job, in which case the request
    bookingData?: ClientJobBookingData | null;
    canEditForm: boolean;
  }>(),
  {
    clientId: null,
    clientDetails: null,
    isNewJob: false,
    bookingData: null,
    isDialogOpen: false,
    jobDate: null,
    canEditForm: false,
  },
);

const validate = validationRules;
const recentJobDialogRef = ref<HTMLElement | any>();

const clientDispatcherSelect: Ref<any> = ref(null);

const clientDetailsStore = useClientDetailsStore();
const clientPortalStore = useClientPortalStore();
const userManagementStore = useUserManagementStore();

const form: Ref<any> = ref(null);
const cashSalesDetailsRef: Ref<any> = ref(null);

const awaitingClientData: Ref<boolean> = ref(false);
const selectedClientDetails: Ref<ClientDetails | null> = ref(null);
const clientShort: Ref<ClientShort> = ref(new ClientShort());
const proofOfDelivery: Ref<ProofOfDelivery> = ref(new ProofOfDelivery());
const cashSaleClientDetails: Ref<CashSaleClientDetails> = ref(
  new CashSaleClientDetails(),
);
const clientDispatcher: Ref<ClientPerson> = ref(new ClientPerson());
const additionalEquipmentList: Ref<number[]> = ref([]);
const jobReferences: Ref<JobReferenceDetails[]> = ref([]);
const jobNotes: Ref<Communication[]> = ref([]);

const clientBookingData: Ref<ClientJobBookingData | null> = ref(null);

const selectedJobDate: Ref<number> = ref(moment().valueOf());
// The first pudItem's epochTime formatted as HHmm
const firstPudArrival = ref('');

const jobReferenceRequirementsMet: Ref<boolean> = ref(false);
const search: Ref<string> = ref('');

const cashSaleClientExpansionPanelOpen: Ref<boolean[]> = ref([true]);

const appliedQuoteId: Ref<string | null> = ref(null);
const appliedJobId: Ref<number | null> = ref(null);
const appliedQuoteDetails: Ref<QuoteDetails | null> = ref(null);

const appliedRecentJobDetails: Ref<JobDetails | null> = ref(null);

const cashSalesDialog: Ref<boolean> = ref(false);

const confirmDialogIsOpen: Ref<boolean> = ref(false);
const pendingAction: Ref<'reset' | 'setClient' | null> = ref(null);
const newClientId: Ref<string> = ref('');

const bookingDateDetails = computed(() => {
  return returnBookingDateDetails(selectedJobDate.value);
});

/**
 * Returns the number of PUD items in the job details.
 */
const pudItemCount: ComputedRef<number> = computed(() => {
  return props.jobDetails.pudItems.length;
});

// Method to trim the input value
const trimInput = (query: string) => {
  search.value = query.trim(); // Trim leading/trailing spaces
};

// Custom filter to ignore leading/trailing spaces
const customFilter = (
  item: ClientSearchSummary,
  queryText: string,
  itemText: string,
): boolean => {
  // Trim the query text and item text for comparison
  const text = itemText.toLowerCase().trim();
  const query = queryText.toLowerCase().trim();

  // Check if the item includes the trimmed query
  return text.includes(query);
};

// function to return booking date in natural language for date-selected
function returnBookingDateDetails(selectedJobDate: number): {
  description: string;
  class: string;
  icon: string;
} {
  const tz = getUserLocale();
  const now = moment().tz(tz).startOf('day'); // Start of today
  const bookingDate = moment(selectedJobDate).tz(tz).startOf('day'); // Start of selected date

  // Check if the booking date is today
  if (bookingDate.isSame(now, 'day')) {
    return {
      description: 'Today',
      class: 'today',
      icon: 'today', // Icon for today
    };
  }
  // Check if the booking date is in the past
  else if (bookingDate.isBefore(now, 'day')) {
    return {
      description: 'Backdated',
      class: 'backdated',
      icon: 'history', // Icon for backdated
    };
  }

  // Future dates logic
  else {
    const daysDifference = bookingDate.diff(now, 'days');

    if (daysDifference === 1) {
      return {
        description: 'Tomorrow',
        class: 'upcoming',
        icon: 'event', // Icon for tomorrow
      };
    } else if (daysDifference < 7) {
      return {
        description: `In ${daysDifference} day${
          daysDifference !== 1 ? 's' : ''
        }`,
        class: 'upcoming',
        icon: 'schedule', // Icon for upcoming days
      };
    } else {
      const weeksDifference = Math.ceil(daysDifference / 7);
      return {
        description: `In ${weeksDifference} week${
          weeksDifference !== 1 ? 's' : ''
        }`,
        class: 'upcoming',
        icon: 'calendar_month', // Icon for upcoming weeks
      };
    }
  }
}

/**
 * Controls visibility of ContentDialog. Gets and sets prop isDialogOpen.
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * Returns the client list for the Client Select Autocomplete.
 */
const clientSelectList: ComputedRef<ClientSearchSummary[]> = computed(() => {
  return clientDetailsStore.clientListForBooking;
});

/**
 * Watches for changes in the dialogController value. If the dialog is opened,
 * set the component state. If the dialog is closed, reset the component state.
 */
watch(dialogController, (newValue) => {
  if (newValue) {
    setWorkingVariables();
  } else {
    resetWorkingVariables();
  }
});

/**
 * Copies properties from props to working variables when the dialog is opened.
 * These variables will be modelled to inputs in the template. We don't model
 * directly to the props to allow for cancelling changes.
 */
function setWorkingVariables() {
  // Set values from props to working variables
  if (props.clientDetails) {
    selectedClientDetails.value = initialiseClientDetails(props.clientDetails);
  }
  if (props.bookingData) {
    clientBookingData.value = props.bookingData;
  }

  // Set values from jobDetails to working variables
  clientShort.value = Object.assign(new ClientShort(), props.jobDetails.client);
  clientDispatcher.value = Object.assign(
    new ClientPerson(),
    props.jobDetails.clientDispatcher,
  );
  proofOfDelivery.value = Object.assign(
    new ProofOfDelivery(),
    props.jobDetails.proofOfDelivery,
  );
  additionalEquipmentList.value = [...props.jobDetails.additionalEquipments];
  jobReferences.value = [
    ...props.jobDetails.jobReference.map((reference) =>
      Object.assign(new JobReferenceDetails(), reference),
    ),
  ];
  jobNotes.value = deepCopy(props.jobDetails.notes);

  if (props.jobDetails.cashSaleClientDetails) {
    cashSaleClientDetails.value = Object.assign(
      new CashSaleClientDetails(),
      props.jobDetails.cashSaleClientDetails,
    );
  }

  // Use epochTime if first pudItem if it exists, else use jobDate, else use
  // current time
  selectedJobDate.value = props.jobDetails.pudItems[0]
    ? props.jobDetails.pudItems[0].epochTime
    : props.jobDate || moment().valueOf();
  firstPudArrival.value = props.jobDetails.pudItems[0]
    ? returnFormattedTime(props.jobDetails.pudItems[0].epochTime, 'HHmm')
    : '';

  form.value?.resetValidation();
}

/**
 * If appliedQuoteId is defined, it means we are booking from a quote. Used in
 * template to disable certain buttons.
 */
const isBookingFromQuote: ComputedRef<boolean> = computed(() => {
  return !!appliedQuoteId.value;
});

/**
 * If appliedJobId is defined, it means we are booking from a recent job. Used in
 * template to disable add permanent job button.
 */
const isBookingFromRecentJobs: ComputedRef<boolean> = computed(() => {
  return !!appliedJobId.value;
});

const isCashSale: ComputedRef<boolean> = computed(() => {
  return clientShort.value.id === 'CS';
});

/**
 * Resets working variables to their initial state when the dialog is closed.
 */
function resetWorkingVariables() {
  // only clear client if not client portal
  if (!sessionManager.isClientPortal()) {
    selectedClientDetails.value = null;
    // Emit event to notify parent component that client details have been cleared
    emit('clientDetailsSet', null);
    clientShort.value = new ClientShort();
    clientDispatcher.value = new ClientPerson();
    clientBookingData.value = null;
  }
  proofOfDelivery.value = new ProofOfDelivery();
  additionalEquipmentList.value = [];
  jobNotes.value = [];
  cashSaleClientDetails.value = new CashSaleClientDetails();
  selectedJobDate.value = moment().valueOf();
  firstPudArrival.value = '';
  jobReferences.value = [new JobReferenceDetails()];
  appliedQuoteId.value = null;
  appliedQuoteDetails.value = null;
  appliedJobId.value = null;
  appliedRecentJobDetails.value = null;
}

/**
 * Applies changes to the job details and emits the changes to the parent.
 */
function applyChanges(recurrenceType?: JobRecurrenceType) {
  if (selectedClientDetails.value?.clientId === 'CS') {
    const isCashSalesFormValid =
      cashSalesDetailsRef.value?.validateCashSalesForm?.();

    if (!isCashSalesFormValid) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }
  }

  if (!form.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }

  // Check if all required data is present
  if (!clientBookingData.value || !selectedClientDetails.value) {
    showNotification(
      'Something went wrong. Missing information required for booking.',
    );
    return;
  }
  jobReferences.value = jobReferences.value.filter(
    (reference) => reference.reference !== '',
  );
  if (jobReferences.value.length === 0) {
    jobReferences.value = [new JobReferenceDetails()];
  }

  const jobKeyDetailsUpdate: JobKeyDetailsUpdate = {
    clientDetails: selectedClientDetails.value,
    clientShort: clientShort.value,
    clientDispatcher: clientDispatcher.value,
    proofOfDelivery: proofOfDelivery.value,
    additionalEquipmentList: additionalEquipmentList.value,
    jobReferences: jobReferences.value,
    jobNotes: jobNotes.value,
    bookingData: clientBookingData.value,
    jobDate: selectedJobDate.value,
    recurrenceType: recurrenceType ?? null,
    cashSaleClientDetails: selectedClientDetails.value.isCashSale
      ? cashSaleClientDetails.value
      : null,
    quoteDetails: appliedQuoteDetails.value,
    jobDetails: appliedRecentJobDetails.value,
    firstPudArrival: firstPudArrival.value,
  };

  emit('applyChanges', jobKeyDetailsUpdate);
  dialogController.value = false;
}

function cancelChanges() {
  dialogController.value = false;
  emit('update:isDialogOpen', dialogController.value);
}

/**
 * Returns the list of ClientPersons from the store if the clientId matches
 * the clientDetails._id
 * @returns the list of ClientPersons
 */
const clientPersonsList = computed((): ClientPerson[] | null => {
  const clientContacts =
    userManagementStore.clientIdAndClientPersonWithAuthDetailsList;

  // Check if the client ID matches the clientDetails._id
  if (
    clientContacts?.client_id &&
    clientContacts.client_id === props.clientDetails?._id
  ) {
    // Map over the list of client persons and return their details
    return clientContacts.clientPersonWithAuthDetailsList.map(
      (x) => x.clientPerson,
    );
  }

  return null;
});

/**
 * Requests client details by client ID, awaits the response and handles it.
 * @param clientId clientId to request
 * @param unassignedPudIds provided when booking a job from unassigned puds from
 * the Point Manager, and when this method is being called from JobBooking
 * component
 */
async function getAndSetClientDetails(
  clientId: string,
  unassignedPudIds?: string[],
) {
  awaitingClientData.value = true;
  let clientDetails: ClientDetails | null = props.clientDetails;
  // If the client is a cash sale, set the client details to a new ClientDetails
  // object. If it's not a Cash Sale client, then request the client details associated with the id
  if (!sessionManager.isClientPortal()) {
    if (clientId === 'CS') {
      clientDetails = new ClientDetails();
      clientDetails.isCashSale = true;
      cashSalesDialog.value = true;
    } else {
      // Request ClientDetails and handle response
      clientDetails =
        await clientDetailsStore.requestClientDetailsByClientId(clientId);
    }
  }
  const result = await setClientDetails(clientDetails, unassignedPudIds);
  awaitingClientData.value = false;

  // Refresh the default dispatcher in the client dispatcher select component
  if (!sessionManager.isClientPortal()) {
    nextTick(() => {
      clientDispatcherSelect.value?.refreshDefaultDispatcher();
    });
    // FAILED - reset everything and show error message
    if (!result) {
      resetWorkingVariables();
    }
  } else {
    // set default dispatcher for client portal
    // Use current users referenceId from token to find the dispatcher in the list of Client Persons
    const currentUser = clientPersonsList.value?.find(
      (x) => x._id === sessionManager.getReferenceId(),
    );
    // If we find the current user in the list of ClientPersons, set the dispatcher details
    if (currentUser) {
      props.jobDetails.clientDispatcher.firstName = currentUser.firstName;
      props.jobDetails.clientDispatcher.lastName = currentUser.lastName;
      props.jobDetails.clientDispatcher.contactMobileNumber =
        currentUser.contactMobileNumber;
      props.jobDetails.clientDispatcher.contactLandlineNumber =
        currentUser.contactLandlineNumber;
      if (currentUser.emailAddress.length > 0 && currentUser.emailAddress[0]) {
        props.jobDetails.clientDispatcher.emailAddress[0] =
          currentUser.emailAddress[0];
      }
      clientDispatcher.value = props.jobDetails.clientDispatcher;
    }
  }
}

/**
 * Called from template (date picker and recent jobs dialog) to set the selectedJobDate to the incoming value
 * @param epoch - epoch time to set the selectedJobDate to
 */
function setSelectedJobDate(epoch: number): void {
  if (epoch !== null) {
    selectedJobDate.value = epoch;
  }
}

/**
 * Called from template (recent jobs dialog) to set the first pud time for the
 * job
 * @param time - time to set the firstPudArrival to, formatted as HHmm
 */
function setFirstPudArrival(time: string): void {
  firstPudArrival.value = time;
}

/**
 * Sets the client details and required booking data for the client. Fetches all
 * required information for the booking. Returns a boolean which describes
 * whether the booking should be allowed to proceed based on responses.
 * @param client - ClientDetails object to set
 * @param unassignedPudIds - Provided when booking a job from unassigned puds
 * from the Point Manager. Set to the requiredBookingData object so that we can access it in the form.
 */
async function setClientDetails(
  client: ClientDetails | null,
  unassignedPudIds?: string[],
): Promise<boolean> {
  if (!client?.clientId) {
    return false;
  }
  // Check if the client is valid for booking
  if (!isClientValidForBooking(client)) {
    return false;
  }
  let requiredBookingData: ClientJobBookingData | null = null;
  // Request common addresses, related contacts and client persons
  if (sessionManager.isClientPortal()) {
    requiredBookingData =
      await clientPortalStore.requestJobBookingDataForClient(client);
  } else {
    requiredBookingData =
      await clientDetailsStore.requestJobBookingDataForClient(client);
  }

  // Some required data was missing, or the request failed
  if (
    !requiredBookingData?.currentServiceRates ||
    !requiredBookingData?.currentFuelSurcharges
  ) {
    return false;
  }
  requiredBookingData.unassignedPudIds = unassignedPudIds;
  clientBookingData.value = requiredBookingData;
  selectedClientDetails.value = client;

  // Emit event to notify parent component that client details have been set
  emit('clientDetailsSet', client);

  // Set properties from ClientDetails object to working variables
  setJobDetailsFromClient(client);
  return true;
}

/**
 * Checks if the client is valid for booking. If the client has the flag of "SEE
 * ACCOUNTS" we should block the job booking and alert the user.
 * @param client - ClientDetails object to check
 */
function isClientValidForBooking(client: ClientDetails) {
  if (
    !client.isCashSale &&
    client.accountsReceivable.creditStatus === 2 &&
    !client.statusList.includes(3)
  ) {
    const dialogNotificationMessage =
      'Booking unavailable for ' +
      (client.tradingName ? client.tradingName : client.clientName) +
      '. Please see Accounts.';
    useRootStore().setDialogNotification([dialogNotificationMessage]);
    return false;
  }
  return true;
}

/**
 * Sets properties from ClientDetails object to working variables, for use in
 * the form. This is so we can apply and emit the changes, or cancel the
 * changes, without mutating the props directly.
 * @param client - ClientDetails object to set properties from
 */
function setJobDetailsFromClient(client: ClientDetails) {
  if (!client.isCashSale) {
    clientShort.value.id = client.clientId!;
    clientShort.value.clientName = client.displayName;
    proofOfDelivery.value = client.proofOfDelivery;

    // Add any client references to the working job references list
    addClientReferencesToJobReferences(
      jobReferences.value,
      client.references.mainJobScreen,
    );

    // Add any client notes to the working job notes list
    jobNotes.value = getClientNotesForJob(
      client.specialInstructions,
      jobNotes.value,
    ).map((note) =>
      // Update the note with the current user's name and timestamp
      updateClientNoteBeforeAddingToJob(note, sessionManager.getUserName()),
    );
  } else {
    clientShort.value.clientName = client.clientName;
    clientShort.value.id = client.clientId!;
    jobReferences.value = [new JobReferenceDetails()];
    clientDispatcher.value = new ClientPerson();
    cashSaleClientDetails.value = new CashSaleClientDetails();
    proofOfDelivery.value = new ProofOfDelivery();
    proofOfDelivery.value.pudPaperwork = false;
    proofOfDelivery.value.jobPaperwork = false;
  }
}

/**
 * Handles emit from JobBookingClientNotesDialog component. Payload contains a
 * list of notes to add to the job.
 */
function addNotesFromDialog(notes: Communication[]) {
  const notesToAdd = notes
    .map((note) =>
      // Update the note with the current user's name and timestamp
      updateClientNoteBeforeAddingToJob(note, sessionManager.getUserName()),
    )
    // Filter out any notes that are already in the job notes list
    .filter((note) => jobNotes.value.findIndex((x) => x.id === note.id) === -1);
  jobNotes.value = [...jobNotes.value, ...notesToAdd];
}

/**
 * Returns the status of the client appended to the end of the client's name in
 * the client select list
 * @param statusList - List of statuses
 * @param creditStatus - Credit status
 */
function getClientStatus(statusList: number[], creditStatus: number): string {
  return statusList.includes(13)
    ? 'RETIRED'
    : creditStatus === 2
      ? 'SEE ACCOUNTS'
      : statusList.includes(47)
        ? 'CLOSED'
        : '';
}

/**
 * Handles emit from JobBookingQuoteSelect component to apply the selected quote.
 * @param id - Quote mongo id to apply
 */
function applyQuoteId(id: string | null) {
  appliedQuoteId.value = id;
  applyPropertiesFromQuote(id);
}
/**
 * Applies properties from the selected quote to the current job. Finds the
 * quote referenced by the id and applies the job details to the current job.
 * @param id - Quote mongo id to apply
 */
function applyPropertiesFromQuote(id: string | null) {
  if (!id) {
    appliedQuoteDetails.value = null;
    return;
  }
  const foundQuote = clientBookingData.value?.quoteDetails?.find(
    (quote) => quote.id === id,
  );
  if (foundQuote?.jobDetails) {
    additionalEquipmentList.value =
      foundQuote.jobDetails.additionalEquipments ?? [];
    jobReferences.value = foundQuote.jobDetails.jobReference ?? [];
    jobNotes.value = foundQuote.jobDetails.notes ?? [];
    clientDispatcher.value =
      foundQuote.jobDetails.clientDispatcher ?? new ClientPerson();
    selectedJobDate.value =
      foundQuote.jobDetails.pudItems?.[0]?.epochTime ?? moment().valueOf();
    firstPudArrival.value = returnFormattedTime(
      foundQuote.jobDetails.pudItems?.[0]?.epochTime ?? moment().valueOf(),
      'HHmm',
    );

    // Set the quote to local property so we can emit it later
    appliedQuoteDetails.value = foundQuote;
  }

  if (clientBookingData.value?.clientId === 'CS') {
    if (foundQuote?.jobDetails.cashSaleClientDetails) {
      cashSaleClientDetails.value =
        foundQuote?.jobDetails.cashSaleClientDetails;
      proofOfDelivery.value = foundQuote?.jobDetails.proofOfDelivery;
      // appliedQuoteDetails.value = foundQuote;
    }
  }
}

/**
 * Applies properties from the selected quote to the current job. Finds the
 * quote referenced by the id and applies the job details to the current job.
 * @param id - Quote mongo id to apply
 */
function applyPropertiesFromQuoteSearch(quote) {
  const quoteDetails = quote.quoteDetails;
  if (!quoteDetails) {
    appliedQuoteDetails.value = null;
    return;
  }
  getAndSetClientDetails(quoteDetails.clientId);
  appliedQuoteId.value = quoteDetails.id ?? '';
  const foundQuote = quoteDetails;
  if (foundQuote?.jobDetails) {
    additionalEquipmentList.value =
      foundQuote.jobDetails.additionalEquipments ?? [];
    jobReferences.value = foundQuote.jobDetails.jobReference ?? [];
    jobNotes.value = foundQuote.jobDetails.notes ?? [];
    clientDispatcher.value =
      foundQuote.jobDetails.clientDispatcher ?? new ClientPerson();
    selectedJobDate.value =
      foundQuote.jobDetails.pudItems?.[0]?.epochTime ?? moment().valueOf();
    firstPudArrival.value = returnFormattedTime(
      foundQuote.jobDetails.pudItems?.[0]?.epochTime ?? moment().valueOf(),
      'HHmm',
    );

    // CHECK IF STATUS IS NOT VALID RESET ACCOUNTING DETAILS AND SERVICE DETAILS
    if (quote.status !== 'Valid') {
      foundQuote.jobDetails.serviceTypeId = 0;
      foundQuote.jobDetails.accounting = new JobAccountingDetails();
      foundQuote.jobDetails.rateTypeId = 0;
      foundQuote.jobDetails.additionalJobData = new AdditionalJobData();
    }
    // Set the quote to local property so we can emit it later
    appliedQuoteDetails.value = foundQuote;
  }

  if (clientBookingData.value?.clientId === 'CS') {
    if (foundQuote?.jobDetails.cashSaleClientDetails) {
      cashSaleClientDetails.value =
        foundQuote?.jobDetails.cashSaleClientDetails;
      proofOfDelivery.value = foundQuote?.jobDetails.proofOfDelivery;
      // appliedQuoteDetails.value = foundQuote;
    }
  }
}

/**
 * Handles emit applyJobId from RecentBookedJobsSelect component to apply the selected quote.
 * @param id - job id to apply
 */
function bookFromRecentJob(id: number | null) {
  appliedJobId.value = id;
  applyPropertiesFromJob(id);
}
/**
 * Applies properties from the selected job to the current job. Finds the
 * job referenced by the id and applies the job details to the current job.
 * @param id - job id to apply
 */
async function applyPropertiesFromJob(id: number | null) {
  try {
    if (!id) {
      appliedRecentJobDetails.value = null;
      return;
    }
    const foundJob = await useOperationsStore().requestJobDetailsByJobId(id);
    if (!foundJob) {
      throw new Error(
        'Unable to retrieve the job details for the selected job.',
      );
    }
    additionalEquipmentList.value = foundJob.additionalEquipments || [];
    jobNotes.value = foundJob.notes || [];
    clientDispatcher.value = foundJob.clientDispatcher || new ClientPerson();

    if (foundJob.pudItems.length > 0) {
      foundJob.pudItems[0].epochTime = selectedJobDate.value;
    }

    // Set the job to local property so we can emit it later
    appliedRecentJobDetails.value =
      recentJobDialogRef.value.createReBookJobDetails(foundJob);

    jobReferences.value = appliedRecentJobDetails.value?.jobReference || [];

    if (clientBookingData.value?.clientId === 'CS') {
      if (!foundJob?.cashSaleClientDetails) {
        throw new Error(
          'Unable to retrieve the cash sale details for the selected job.',
        );
      }
      cashSaleClientDetails.value = foundJob.cashSaleClientDetails;
      proofOfDelivery.value = foundJob.proofOfDelivery;
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'An unexpected error occurred.';
    showNotification(errorMessage);
  }
}

/**
 * Set the view type to NEW and resets the component state to a fresh JobDetails
 * object
 */
function clearClient() {
  tryResetWorkingVariables();
  form.value?.resetValidation();
}

/**
 * clear the selected job details from recent jobs or quote dialog
 * resets applied Id, and applied Details
 */
function clearSelectedJob() {
  appliedQuoteId.value = null;
  appliedJobId.value = null;
  appliedJobId.value = null;
  appliedRecentJobDetails.value = null;
  appliedQuoteDetails.value = null;

  selectedJobDate.value = moment().valueOf();

  if (cashSaleClientDetails.value) {
    cashSaleClientDetails.value = new CashSaleClientDetails();
  }
}

/**
 * Checks if any job reference has a non-empty string value.
 * Checks if dispatcher is selected and is not default dispatcher
 * Checks if required equipments has value
 * Used to determine if there are unsaved changes.
 */
function hasUnsavedJobReferences(): boolean {
  const dispatcherChanged =
    !!clientDispatcher.value._id &&
    selectedClientDetails.value?.defaultDispatcherId !==
      clientDispatcher.value._id;

  const hasEquipment = additionalEquipmentList.value.length > 0;

  const hasJobRefs = jobReferences.value.some(
    (ref) => typeof ref.reference === 'string' && ref.reference.trim() !== '',
  );

  return dispatcherChanged || hasEquipment || hasJobRefs;
}

/**
 * Attempts to reset working variables.
 * If there are unsaved changes, prompts user confirmation.
 * Otherwise, clears all data immediately.
 */
function tryResetWorkingVariables() {
  if (hasUnsavedJobReferences()) {
    confirmDialogIsOpen.value = true;
    pendingAction.value = 'reset';
  } else {
    resetWorkingVariables();
  }
}
/**
 * Attempts to change client details.
 * If there are unsaved changes, prompts confirmation.
 * Otherwise, fetches and sets new client details directly.
 */
function trySetClientDetails(id: string) {
  if (hasUnsavedJobReferences()) {
    confirmDialogIsOpen.value = true;
    pendingAction.value = 'setClient';
    newClientId.value = id;
  } else {
    resetWorkingVariables();
    getAndSetClientDetails(id);
  }
}

/**
 * Handles confirmation dialog confirm button.
 * Proceeds with the pending action if user confirms the discard.
 */
function confirmClearDialog() {
  confirmDialogIsOpen.value = false;

  if (pendingAction.value === 'reset') {
    resetWorkingVariables();
  } else if (pendingAction.value === 'setClient') {
    resetWorkingVariables();
    getAndSetClientDetails(newClientId.value);
  }
  pendingAction.value = null;
}

/**
 * Handles confirmation dialog cancel button.
 * cancel action and reset selected clientId.
 */
function cancelClearDialog() {
  confirmDialogIsOpen.value = false;
  pendingAction.value = null;

  if (clientBookingData.value) {
    // reset to old client if canceled
    clientShort.value.id = clientBookingData.value?.clientId;
  }
}

onMounted(() => {
  setWorkingVariables();
  if (props.clientId) {
    getAndSetClientDetails(props.clientId);
  }
});
</script>

<style scoped lang="scss">
.form-container {
  padding-left: 24px;
  padding-right: 24px;
  margin-top: 24px;
  margin-bottom: 44px;
  .section {
    height: 100%;
    width: 48%; // 2% for divider
  }
}
.date-picker-label {
  color: $bg-light;
  font-size: 10px;
}
.v-btn-confirm-custom {
  position: relative;
  z-index: 2;
  .v-btn {
    color: var(--text-color) !important;
    background: var(--primary-gradient) !important;
    background-color: var(--primary) !important;
    min-width: 240px !important;
    border-radius: $border-radius-Xlg;
    border: 2px solid var(--primary-dark);
    transition: all ease-in-out 0.3s;
    &:hover {
      transform: scale(1.1);
      box-shadow: var(--box-shadow);
    }
    &:disabled {
      background: none !important;
      border: none !important;
    }
  }
}

.action-btn {
  border-radius: $border-radius-btn;
  box-shadow: none !important;
  padding: 22px !important;
  margin-right: 14px;
  margin-left: 14px;
  &.cancel {
    color: $error !important;
  }
  &.add {
    color: white !important;
  }
  &:hover {
    box-shadow: $box-shadow !important;
  }
}

.notes-container {
  padding-top: 2px;
  margin-top: 2px;
  .v-btn {
    width: 97%;
    border-bottom: 1px dotted $warning;
    border-radius: 10px 10px 0px 0px;
    height: 48px;
    &:disabled {
      background: none !important;
      border: none !important;
    }
  }
}

.date-warning {
  font-weight: 500;
  margin: 10px;
  font-size: $font-size-18;

  .v-icon {
    margin-right: 6px;
  }

  &.backdated {
    color: var(--error);
    .v-icon {
      color: var(--error);
    }
  }

  &.today {
    color: var(--accent);
    .v-icon {
      color: var(--accent);
    }
  }

  &.upcoming {
    color: orange;
    .v-icon {
      color: orange;
    }
  }
}

.client-name {
  font-size: $font-size-18;
  color: var(--text-color);
  text-transform: uppercase;
  font-weight: 600;
  padding: 6px;
  margin-top: 2px;
  background-color: var(--background-color-300);
  border-radius: $border-radius-base;
}

.selected-job-txt-container {
  display: flex;
  align-content: right;
  align-items: right;
  justify-content: center;
  padding-top: 8px;
  .active {
    color: var(--primary-light) !important;
    font-weight: 500;
  }
  .disabled {
    color: var(--light-text-color);
  }

  .cancel-selected-job-btn {
    border-radius: 100px;
    border: 1.5px solid var(--light-text-color);
    height: 18px;
    width: 32px;

    &:hover {
      background-color: $error-type !important;
      border-color: $error;
      .v-icon {
        color: white;
      }
    }
    .v-icon {
      color: var(--light-text-color);
    }
  }
}
</style>
