<template>
  <v-layout wrap class="service-rate-selector__container">
    <v-flex md12>
      <v-layout
        justify-space-between
        align-center
        px-2
        pt-1
        style="position: relative"
      >
        <span class="subheader">Adjust Travel Distance</span>
      </v-layout>
    </v-flex>
    <v-flex md12>
      <v-divider></v-divider>
    </v-flex>
    <v-flex md12>
      <v-layout class="px-2 pt-4 pb-2">
        <v-flex md6 pr-1>
          <v-text-field
            type="number"
            persistent-hint
            :value="uneditedTravelDistance"
            color="orange"
            label="Travel Distance"
            suffix="KM"
            outline
            :hint="returnReadableChargeBasisDescription(chargeBasis)"
            disabled
            class="v-solo-custom"
          />
        </v-flex>
        <v-flex md6 pl-1>
          <v-text-field
            type="number"
            :rules="[validationRules.required, validationRules.nonNegative]"
            v-model.number="editedTravelDistance"
            color="orange"
            label="Adjusted Travel Distance"
            outline
            suffix="KM"
            class="v-solo-custom form-field-required"
            @focus="$event.target.select()"
          />
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import {
  ChargeBasis,
  returnReadableChargeBasisDescription,
} from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import { WritableComputedRef, computed, onUnmounted } from 'vue';
const emit = defineEmits<{
  (event: 'update:editedTravelDistance', payload: number): void;
  (event: 'refreshAccountingDetails'): void;
}>();

const props = defineProps<{
  chargeBasis: ChargeBasis;
  editedTravelDistance: number | undefined;
  uneditedTravelDistance: number;
}>();

let updateTimeout: ReturnType<typeof setTimeout> | null = null;

const editedTravelDistance: WritableComputedRef<number> = computed({
  get(): number {
    return props.editedTravelDistance ?? 0;
  },
  set(value: number): void {
    if (value) {
      emit('update:editedTravelDistance', value);

      // Emit to parent to re-calculate the rates
      if (updateTimeout) {
        clearTimeout(updateTimeout);
      }
      updateTimeout = setTimeout(() => {
        emit('refreshAccountingDetails');
      }, 800);
    }
  },
});

onUnmounted(() => {
  if (updateTimeout) {
    clearTimeout(updateTimeout);
  }
});
</script>
<style scoped lang="scss">
.service-rate-selector__container {
  background-color: var(--background-color-300);
  border: 0.5px solid $border-color;
  padding: 10px;
  border-radius: $border-radius-base;

  .subheader {
    color: var(--light-blue-bg);
    font-size: $font-size-20;
    font-weight: 500;
  }
}
.task-bar-checkbox {
  flex-shrink: 0;
  flex-grow: 0;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute;
  right: 0;
  top: 0;
  color: orange !important;
}
</style>
