<template>
  <section class="leg-time-definition-selector">
    <v-layout md12 mb-1 row wrap class="header-section">
      <h6>Booking Time</h6>
      <span class="extra-txt" v-if="!isTheFirstPud">( Estimate )</span>
      <v-switch
        v-if="isTheFirstPud && allowAsapTimeDefinition"
        label="READY NOW"
        hide-details
        v-model="isAsapTimeDefinition"
        :disabled="formDisabled"
        class="asap-switch"
        color="orange"
      ></v-switch>
    </v-layout>
    <v-layout md12 row wrap space-evenly mt-1>
      <v-flex
        md4
        pr-2
        v-if="!isAsapTimeDefinition && isTheFirstPud && !isRecurringJob"
      >
        <DatePickerBasic
          @setEpoch="setEpoch"
          :labelName="'Select a Date'"
          :epochTime="pudDate"
          :disabled="formDisabled"
          :formDisabled="isAsapTimeDefinition || formDisabled"
          flat
          color="orange"
          solo
          :soloInput="true"
        >
        </DatePickerBasic>
      </v-flex>

      <v-flex md4 pr-2>
        <v-text-field
          v-if="showClockSelect"
          v-model="pudTime"
          :rules="[validationRules.twentyFourHourTime]"
          hint="24-hour time"
          mask="##:##"
          label="Arrival Time"
          color="orange"
          :disabled="formDisabled"
          @focus="$event.target.select()"
          @input="updatePudTime"
          outline
          flat
          class="v-solo-custom"
        />
      </v-flex>

      <v-flex :class="isTheFirstPud && !isAsap ? 'md4' : 'md12'">
        <v-text-field
          v-model.number="loadDurationController"
          hint="Expected load duration (mins)"
          type="number"
          label="Load Duration"
          :solo="soloInput"
          color="orange"
          flat
          :disabled="formDisabled"
          @focus="$event.target.select()"
          outline
          class="v-solo-custom"
        />
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import { getPudLoadDuration } from '@/helpers/BookingHelpers/BookingHelpers';
import {
  isToday,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { PUDItem } from '@/interface-models/Jobs/PUD/PUDItem';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  onMounted,
  ref,
  watch,
} from 'vue';

const props = withDefaults(
  defineProps<{
    currentPudItem: PUDItem;
    editingExistingPUDItem?: boolean;
    clientDetails?: ClientDetails | null;
    pudItems: PUDItem[];
    editingPudIndex: number;
    isRecurringJob?: boolean;
    soloInput?: boolean;
    formDisabled?: boolean;
    allowAsapTimeDefinition?: boolean;
    initWithEpochTime?: number | null;
  }>(),
  {
    editingExistingPUDItem: false,
    clientDetails: null,
    soloInput: false,
    formDisabled: false,
    allowAsapTimeDefinition: true,
    initWithEpochTime: null,
  },
);

const pudDate: Ref<number | null> = ref(null);
const isTheFirstPud: Ref<boolean> = ref(false);
const isAsap: Ref<boolean> = ref(true);

const emit = defineEmits<{
  (event: 'update:pudTime', payload: string): void;
  (event: 'update:pudDate', payload: number | null): void;
}>();

const pudTime = ref(props.currentPudItem.pickupTime); // Initialize pudTime with the value from currentPudItem

const updatePudTime = (newValue: string) => {
  pudTime.value = newValue; // Update the local pudTime value
  emit('update:pudTime', newValue); // Emit the event to notify parent component
};

const selectedTimeDefinition: WritableComputedRef<number> = computed({
  get(): number {
    return props.currentPudItem.timeDefinition;
  },
  set(value: number): void {
    props.currentPudItem.timeDefinition = value;
  },
});

const isAsapTimeDefinition: WritableComputedRef<boolean> = computed({
  get(): boolean {
    if (!props.allowAsapTimeDefinition) {
      return false;
    }
    return isAsap.value;
  },
  set(value: boolean): void {
    isAsap.value = value;
    selectedTimeDefinition.value = value ? 9 : 0;
    if (value) {
      const now = moment().tz(useCompanyDetailsStore().userLocale).valueOf();
      props.currentPudItem.epochTime = now;
    }
  },
});

const showClockSelect = computed(() => {
  const ctd = props.currentPudItem.timeDefinition;
  if (!(ctd === 0 || ctd === 1 || ctd === 2 || ctd === 3)) {
    return false;
  }
  if (isTheFirstPud.value) {
    return true;
  }
  if (props.pudItems.length >= 1) {
    if (props.editingExistingPUDItem && isTheFirstPud.value) {
      return true;
    }
    if (
      props.pudItems[0].timeDefinition === 1 &&
      selectedTimeDefinition.value <= 3
    ) {
      return true;
    }
  }
  return false;
});

function setDateAndTimeStringValues() {
  // set job date
  pudDate.value = returnStartOfDayFromEpoch(props.currentPudItem.epochTime);
  if (!pudDate.value) {
    return;
  }
  // Find the difference between the start of the day and the hour of day the
  // job is booked for.
  const difference = props.currentPudItem.epochTime - pudDate.value;

  // Convert the difference into 24 hour clock time. eg "13:30"
  const hours = ('0' + moment.duration(difference).hours()).slice(-2);
  const minutes = ('0' + moment.duration(difference).minutes()).slice(-2);
  pudTime.value = hours + minutes;
}

function setDefaultPudTime() {
  // If first PUD epoch and date to current Time
  if (isTheFirstPud.value) {
    // If initWithEpochTime is passed as a prop, set epoch and date to that
    // time. Otherwise use the current time
    props.currentPudItem.epochTime =
      props.initWithEpochTime && !isToday(props.initWithEpochTime)
        ? props.initWithEpochTime
        : moment().tz(useCompanyDetailsStore().userLocale).valueOf();
    props.currentPudItem.pickupDate = returnStartOfDayFromEpoch(
      props.currentPudItem.epochTime,
    );
  } else {
    // If not first PUD, set epoch and date to previous PUD's time
    if (props.pudItems.length > 0) {
      props.currentPudItem.epochTime =
        props.pudItems[props.pudItems.length - 1].epochTime;
      props.currentPudItem.pickupDate = returnStartOfDayFromEpoch(
        props.currentPudItem.epochTime,
      );
    }
  }
}

function setDefaultTimeDefinition() {
  if (!props.editingExistingPUDItem) {
    // If creating a new PUD (not editing), then set the time definition to ASAP
    // if we're adding a new first pud
    if (isTheFirstPud.value) {
      // If initWithEpochTime is provided, we are in the first pud, and the
      // provided epoch time is NOT today, then don't set the default time
      // definition to ASAP
      if (props.initWithEpochTime && !isToday(props.initWithEpochTime)) {
        isAsap.value = false;
        return;
      }

      isAsapTimeDefinition.value = props.pudItems.length === 0;
    } else {
      isAsap.value = false;
    }
  }
}

function checkIsFirstPud() {
  if (props.pudItems.length < 1 || props.editingPudIndex === 0) {
    isTheFirstPud.value = true;
  } else {
    isTheFirstPud.value = false;
  }
}

const hour: ComputedRef<number> = computed(() => {
  const a = pudTime.value[0];
  const b = pudTime.value[1];
  const hourString = a + b;

  const hour = parseInt(hourString, 10);
  return hour;
});

const minutes = computed(() => {
  const c = pudTime.value[2];
  const d = pudTime.value[3];
  const hourString = c + d;
  const hour = parseInt(hourString, 10);
  return hour;
});

const correctEpochTime: ComputedRef<number> = computed(() => {
  const dateStr = moment(pudDate.value);
  if (pudDate.value !== null) {
    return dateStr
      .add(hour.value, 'hours')
      .add(minutes.value, 'minutes')
      .valueOf();
  }
  return 0;
});

watch(correctEpochTime, (newValue: number) => {
  if (correctEpochTime.value !== null) {
    props.currentPudItem.epochTime = newValue;
  }
});

const setEpoch = (epoch: number | null) => {
  pudDate.value = epoch;
  // Emit the updated date value to the parent component
  emit('update:pudDate', epoch);
};

// Modelled to the textfield input for minutes of loadTime. Return the value
// for currentPudItem.loadTime as minutes. When setting, convert the incoming
// value to milliseconds to save in currentPudItem.
const loadDurationController: WritableComputedRef<number> = computed({
  get(): number {
    if (!props.currentPudItem.loadTime) {
      return 0;
    }
    return moment
      .duration(props.currentPudItem.loadTime, 'milliseconds')
      .asMinutes();
  },
  set(valueInMinutes: number): void {
    if (!valueInMinutes) {
      props.currentPudItem.loadTime = 0;
      return;
    }
    props.currentPudItem.loadTime = moment
      .duration(valueInMinutes, 'minutes')
      .asMilliseconds();
  },
});

function prepareData() {
  checkIsFirstPud();
  if (!props.editingExistingPUDItem) {
    // If NOT editing existing pud item
    setDefaultTimeDefinition();
    setDefaultPudTime();
    setDateAndTimeStringValues();
    loadDurationController.value = getPudLoadDuration(
      props.currentPudItem.isPickup,
      props.clientDetails,
    );
    if (props.isRecurringJob || !props.allowAsapTimeDefinition) {
      isAsap.value = false;
      selectedTimeDefinition.value = 0;
    }
  } else {
    // If editing existing pud item
    if (isTheFirstPud.value) {
      isAsap.value = props.currentPudItem.timeDefinition === 9;
    } else {
      isAsap.value = false;
    }
  }
  setDateAndTimeStringValues();
}

onMounted(() => {
  prepareData();
});
</script>

<style scoped lang="scss">
.add-pud-item {
  position: relative;
  .expansion-panel-container {
    border: 1px solid $translucent;
    border-radius: $border-radius-sm;
    .expansion-item-container {
      border-radius: $border-radius-sm !important;
      background-color: var(--background-color-300) !important;

      .lineitem__label {
        font-size: $font-size-12;
        text-transform: uppercase;
        font-weight: 600;
        color: var(--light-text-color);
      }
      .lineitem__value {
        font-size: $font-size-14;
        font-weight: 400;
      }
    }
  }
}

.header-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 20px;
  // justify-content: space-evenly;
  align-items: flex-start;
  padding-bottom: 6px;

  .asap-switch {
    margin: 0;
    padding: 0;
    justify-content: end;
    font-weight: 800;
  }
}
.extra-txt {
  font-size: $font-size-14;
  color: var(--light-text-color);
  text-transform: uppercase;
  font-weight: 500;
}
</style>
