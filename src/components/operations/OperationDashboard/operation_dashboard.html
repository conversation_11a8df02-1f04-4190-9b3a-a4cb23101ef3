<div class="operation-dashboard">
  <v-layout fill-height>
    <AssetInformationDialog
      v-if="showAssetInformationDialog && selectedFleetAssetId"
      :fleetAssetId="selectedFleetAssetId"
      :showAssetInformationDialog="showAssetInformationDialog"
    ></AssetInformationDialog>
    <OperationDashboardFilters
      v-model="filterActive"
      :dialogActive="filterActive"
      :isMap="false"
    ></OperationDashboardFilters>
    <div>
      <v-tooltip bottom>
        <template #activator="{ on }">
          <v-btn v-on="on" @click="filterActive = true" class="filter-btn" icon>
            <v-icon size="22">filter_alt</v-icon>
          </v-btn>
        </template>
        <span>Dashboard Filters</span>
      </v-tooltip>

      <v-tooltip
        bottom
        v-if="filterStore.selectedServiceType.length > 0 || filterStore.selectedVehicleClass.length > 0 || filterStore.selectedClientIds.length > 0 "
      >
        <template #activator="{ on }">
          <v-btn
            v-if="filterStore.selectedServiceType.length > 0 || filterStore.selectedVehicleClass.length > 0 || filterStore.selectedClientIds.length > 0 "
            v-on="on"
            @click="clearFilters"
            class="filter-btn clear"
            fab
            outline
          >
            <v-icon size="18">clear</v-icon>
          </v-btn>
        </template>
        <span>Clear All Filters</span>
      </v-tooltip>
    </div>
    <v-flex md12>
      <!-- Spotlight Search Button -->
      <v-btn small outline class="search-btn" @click="openSpotlight"
        ><v-icon size="20" class="pr-2">search</v-icon>Quick Job Search</v-btn
      >
      <v-layout column fill-height class="column-container">
        <v-flex class="one-third-section">
          <JobList
            :class="{ 'jobs-list-container': !!filterStore.selectedServiceType.length }"
            @openJobList="openJobList"
            :windowMode="false"
            :selectedJobDetails="jobDetails"
            :currentIndex="currentIndex"
            ref="operationsDashboardJobList"
            @closeAllExpandedRows="closeAllExpandedRows"
          />
        </v-flex>
        <v-flex
          class="one-third-section"
          v-if="selectedFleetAssetId && selectedDriverId"
        >
          <v-layout fill-height>
            <AllocatedWork
              ref="operationsDashboardAllocatedWork"
              :selectedJobDetails="jobDetails"
              :selectedFleetAssetId="selectedFleetAssetId"
              :selectedDriverId="selectedDriverId"
              @closeAllExpandedRows="closeAllExpandedRows"
            />
          </v-layout>
        </v-flex>
        <v-flex class="one-third-section" v-if="selectedJobId !== -1">
          <v-layout fill-height>
            <JobDetailsInformation
              :key="selectedJobId"
              :jobDetails="jobDetails"
              :selectedJobId="selectedJobId"
              @closeAllExpandedRows="closeAllExpandedRows"
            />
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
    <JobDetailsDialog
      v-if="showJobDetailsDialog && selectedJobDetails"
      :jobDetails="selectedJobDetails"
      :showJobDetailsDialog="showJobDetailsDialog"
    ></JobDetailsDialog>
    <!-- QuickJobSearch Overlay -->
    <OperationDashboardQuickJobSearch
      :visible="spotlightVisible"
      :jobs="jobsForSpotlight"
      @close="closeSpotlight"
      @selectedJobId="handleSpotlightJobSelect"
    />
  </v-layout>
</div>
