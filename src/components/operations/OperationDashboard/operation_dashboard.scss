.operation-dashboard {
  height: calc(100vh - 60px);
  margin-right: 4px;
  display: flex;
  flex-direction: column;

  .filter-btn {
    position: fixed;
    z-index: 200;
    left: calc(100% - 680px);
    top: -4px;
    color: var(--text-color);
    height: 35px !important;
    width: 35px !important;
    background-color: $light-theme-text;
    border: 2px solid $border-color;
    border-radius: 44px;
    margin-right: 24px;
    &.clear {
      margin-left: -24px;
      border-radius: 20px;
      top: 2px;
      background: none;
      color: var(--light-text-color);
      height: 24px !important;
      width: 24px !important;
      border: 2px solid $light-text-color;
    }
  }

  .search-btn {
    position: fixed;
    border-radius: 14px;
    z-index: 200;
    right: 760px;
    top: 0px;
    padding: 12px 36px;
    color: rgb(185, 185, 201);
    border: 1.5px solid rgb(185, 185, 201);
    font-weight: 700;
    .v-icon {
      font-weight: 800;
    }
  }

  .column-container {
    .one-third-section {
      &:first-child {
        margin-top: 0px;
      }
      height: 33.33%;
      margin: 1px;
      background-color: var(--background-color-200);
      border-radius: $border-radius-sm;
      resize: vertical;
      overflow: auto; // To allow resizing
      min-height: 3.33%;
      z-index: 0;
    }
  }
  .jobs-list-container {
    border-top: 2px solid $warning !important;
  }
}
