import AllocatedWork from '@/components/operations/OperationDashboard/components/AllocatedWork/index.vue';
import AssetInformationDialog from '@/components/operations/OperationDashboard/components/AssetInformationDialog/index.vue';
import JobDetailsInformation from '@/components/operations/OperationDashboard/components/JobDetails/index.vue';
import JobDetailsDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/index.vue';
import JobList from '@/components/operations/OperationDashboard/components/JobList/index.vue';
import OperationDashboardQuickJobSearch from '@/components/operations/OperationDashboard/components/operation_dashboard_quick_job_search.vue';
import OperationDashboardFilters from '@/components/operations/OperationDashboard/operation_dashboard_filters.vue';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    JobList,
    JobDetailsInformation,
    AllocatedWork,
    AssetInformationDialog,
    OperationDashboardFilters,
    OperationDashboardQuickJobSearch,
    JobDetailsDialog,
  },
})
export default class OperationDashboard extends Vue {
  @Prop() public currentIndex: number;
  @Prop() public jobDetails: JobDetails;

  public filterStore = useFilterStore();
  public operationsStore = useOperationsStore();
  public filterActive = false;
  public spotlightVisible = false;

  public jobStore = useJobStore();

  get operationsOptions() {
    return this.operationsStore.operationOptions;
  }
  get selectedJobId() {
    return this.operationsStore.selectedJobId;
  }
  public openJobList() {
    this.operationsStore.createBroadcastChannel(this.$route.name);
    const routeUrl = this.$router.resolve({
      path: '/job-list',
    });
    window.open(routeUrl.href, 'Jobs List', 'menubar=no');
  }

  get showAssetInformationDialog() {
    return this.operationsStore.viewingAssetInformationDialog;
  }

  get selectedFleetAssetId(): string {
    return this.operationsStore.selectedFleetAssetId ?? '';
  }
  get selectedDriverId() {
    return this.operationsStore.selectedDriverId ?? '';
  }

  get jobsForSpotlight() {
    return this.jobStore.operationJobsList;
  }

  public openSpotlight() {
    this.spotlightVisible = true;
  }
  public closeSpotlight() {
    this.spotlightVisible = false;
  }

  public async handleSpotlightJobSelect(jobId: number) {
    if (sessionManager.isClientPortal()) {
      useClientPortalStore().setSelectedJobId(jobId);
    } else {
      this.operationsStore.setSelectedJobId(jobId);
      const result = await this.operationsStore.getFullJobDetails(jobId);
      if (result) {
        this.operationsStore.setViewingJobDetailsDialog(true);
      }
    }
    this.closeSpotlight();
  }

  // Select refs for jobList and allocatedWork tables.
  // Call respective closeExpandedRows function to minimize all where
  // the data-key is not equal to the supplied string
  public closeAllExpandedRows(selectedType: string) {
    // Find job_list component through ref
    const foundJobList = this.$refs.operationsDashboardJobList as any;
    if (!foundJobList) {
      return;
    }
    if (foundJobList.closeExpandedRows) {
      foundJobList.closeExpandedRows(selectedType);
    }
    // Find allocated_work component through ref
    const foundAllocatedWork = this.$refs
      .operationsDashboardAllocatedWork as any;
    if (!foundAllocatedWork) {
      return;
    }
    if (foundAllocatedWork.closeExpandedRows) {
      foundAllocatedWork.closeExpandedRows(selectedType);
    }
  }

  public clearFilters() {
    this.filterStore.setSelectedServiceType([]);
    this.filterStore.setSelectedVehicleClass([]);
    this.filterStore.setSelectedClientIds([]);
    return;
  }

  public mounted() {
    Mitt.on('closeDashboardJobRows', this.closeAllExpandedRows);
  }
  public beforeDestroy() {
    Mitt.off('closeDashboardJobRows', this.closeAllExpandedRows);
  }

  get showJobDetailsDialog() {
    return this.operationsStore.viewingJobDetailsDialog;
  }

  get selectedJobDetails(): JobDetails | null {
    if (
      !this.showJobDetailsDialog ||
      this.operationsStore.selectedJobId === -1
    ) {
      return null;
    }
    return this.operationsStore.selectedJobDetails;
  }
}
