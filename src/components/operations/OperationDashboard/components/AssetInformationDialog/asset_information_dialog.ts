import AssetInformation from '@/components/common/asset_information/index.vue';
import DriverAppNotificationHistory from '@/components/common/driver_app_notification_history.vue';
import DriverChatHistory from '@/components/common/driver_chat_history/driver_chat_history.vue';
import DriverConversation from '@/components/common/driver_conversation/index.vue';
import DriverDeviceSnapshotHistory from '@/components/common/driver_device_snapshot_history.vue';
import DriverWorkDiaryHistory from '@/components/common/driver_work_diary_history/index.vue';
import FormCard from '@/components/common/ui-elements/form_card.vue';
import GpsPositionHistorySearch from '@/components/operations/maps/job_map_route/gps_position_history_search/index.vue';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    FormCard,
    AssetInformation,
    GpsPositionHistorySearch,
    DriverAppNotificationHistory,
    DriverChatHistory,
    DriverWorkDiaryHistory,
    DriverDeviceSnapshotHistory,
    DriverConversation,
  },
})
export default class AssetInformationDialog extends Vue {
  @Prop() public showAssetInformationDialog: boolean;
  @Prop({ default: '' }) public fleetAssetId: string;

  public operationsStore = useOperationsStore();

  public isEquipmentHire: boolean = false;
  public isOutsideHire: boolean = false;

  public fleetAsset: FleetAssetSummary | null = null;
  public fleetAssetOwner: FleetAssetOwnerSummary | null = null;

  public fleetAssetDriverLabel: string = '';

  get selectedTabId(): string {
    return this.operationsStore.selectedFleetAssetDialogTab;
  }
  set selectedTabId(value: string) {
    this.operationsStore.setSelectedFleetAssetDialogTab(value);
  }

  // Return menu options, hiding options that aren't applicable for Outside and Equipment hire
  get menuOptions(): any[] {
    return [
      { id: 'ASSET_DETAILS', visible: true, title: 'Asset Details' },
      {
        id: 'ALLOCATED_WORK',
        visible: !this.isEquipmentHire && !this.isOutsideHire,
        title: 'Allocated Work',
      },
      {
        id: 'GPS_HISTORY',
        visible: !this.isEquipmentHire && !this.isOutsideHire,
        title: 'GPS History',
      },
      {
        id: 'DRIVER_CONVERSATION',
        visible: !this.isEquipmentHire && !this.isOutsideHire,
        title: 'Send Message to Driver',
      },
      {
        id: 'MESSAGE_HISTORY',
        visible: !this.isEquipmentHire && !this.isOutsideHire,
        title: 'Message History',
      },
      {
        id: 'WORK_DIARY',
        visible: !this.isEquipmentHire && !this.isOutsideHire,
        title: 'Work Diary',
      },
      {
        id: 'APP_STATUS_HISTORY',
        visible: !this.isEquipmentHire && !this.isOutsideHire,
        title: 'Mobile App Status History',
      },
      {
        id: 'SNAPSHOT_HISTORY',
        visible: !this.isEquipmentHire && !this.isOutsideHire,
        title: 'Device Snapshot History',
      },
    ];
  }

  get showDialog() {
    return this.showAssetInformationDialog;
  }
  set showDialog(value: boolean) {
    this.operationsStore.setViewingAssetInformationDialog(value);
  }

  get selectedDriverId(): string {
    const driverId = this.operationsStore.selectedDriverId;
    return driverId ? driverId : '';
  }

  public closeDialog() {
    this.showDialog = false;
  }
  // Find FleetAsset details for supplied ID and set local variables
  public setFleetAssetDetails(fleetAssetId: string) {
    const fleetAssetStore = useFleetAssetStore();
    const foundFleetAsset: FleetAssetSummary | undefined =
      fleetAssetStore.getFleetAssetFromFleetAssetId(fleetAssetId);
    if (!foundFleetAsset) {
      return;
    }
    this.fleetAsset = foundFleetAsset;
    this.isOutsideHire = foundFleetAsset.outsideHire
      ? foundFleetAsset.outsideHire
      : false;
    // Find selected driver (if any exists) to display in title
    const selectedDriverId = this.operationsStore.selectedDriverId
      ? this.operationsStore.selectedDriverId
      : '';
    const foundDriver =
      useDriverDetailsStore().getDriverFromDriverId(selectedDriverId);
    if (!foundDriver) {
      this.fleetAssetDriverLabel = foundFleetAsset.csrAssignedId;
      return;
    }
    this.fleetAssetDriverLabel =
      foundFleetAsset.csrAssignedId + ' - ' + foundDriver.displayName;
  }

  // Find owner of truck and set local variables
  public setFleetAssetOwnerDetails(fleetAssetId: string) {
    const foundOwner =
      useFleetAssetOwnerStore().getOwnerFromFleetAssetId(fleetAssetId);
    if (foundOwner) {
      this.fleetAssetOwner = foundOwner;
      this.isEquipmentHire = foundOwner.affiliation === '2';
    }
  }

  public mounted() {
    this.setFleetAssetDetails(this.fleetAssetId);
    this.setFleetAssetOwnerDetails(this.fleetAssetId);
  }

  public beforeDestroy() {
    this.selectedTabId = '';
  }
}
