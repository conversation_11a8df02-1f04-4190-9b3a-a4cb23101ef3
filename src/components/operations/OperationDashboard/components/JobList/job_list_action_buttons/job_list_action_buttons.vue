<template>
  <section
    class="job-list-action-buttons"
    @dragover.prevent
    @drop="onDrop($event)"
  >
    <v-layout
      class="expanded-content__container"
      align-start
      :column="isVertical ? true : false"
      :class="[isVertical ? 'vertical-list' : '']"
    >
      <span
        class="expanded-content__icon-container"
        @click="customerContacted = !customerContacted"
        v-if="isVertical"
      >
        <v-icon class="expanded-content__icon" color="green accent-3" size="14">
          {{ customerContacted ? 'fas fa-circle' : 'far fa-circle' }}
        </v-icon>
        <span class="expanded-content__icon-tooltip cusCon left-align">
          Customer Contacted
        </span>
      </span>
      <span
        class="expanded-content__icon-container"
        @click="driverContacted = !driverContacted"
        v-if="isVertical"
      >
        <v-icon class="expanded-content__icon" color="blue accent-3" size="14">
          {{ driverContacted ? 'fas fa-circle' : 'far fa-circle' }}
        </v-icon>
        <span class="expanded-content__icon-tooltip driverCon left-align">
          Driver Contacted
        </span> </span
      ><span
        class="expanded-content__icon-container"
        @click="jobActioned = !jobActioned"
        v-if="isVertical"
      >
        <v-icon
          class="expanded-content__icon"
          color="yellow accent-3"
          size="14"
        >
          {{ jobActioned ? 'fas fa-circle' : 'far fa-circle' }}
        </v-icon>
        <span class="expanded-content__icon-tooltip jobAct left-align">
          Job Actioned
        </span> </span
      ><span
        class="expanded-content__icon-container"
        @click="jobDelayed = !jobDelayed"
        v-if="isVertical"
      >
        <v-icon class="expanded-content__icon" color="red accent-3" size="14">
          {{ jobDelayed ? 'fas fa-circle' : 'far fa-circle' }}
        </v-icon>
        <span class="expanded-content__icon-tooltip jobDel left-align">
          Job Delayed
        </span>
      </span>

      <span
        class="expanded-content__icon-container"
        @click="editJobInBookingScreen(jobId)"
        v-if="workStatus === WorkStatus.DRIVER_COMPLETED"
      >
        <v-icon class="expanded-content__icon"> far fa-play-circle </v-icon>
        <span class="expanded-content__icon-tooltip left-align"> Add Leg </span>
      </span>
      <span
        class="expanded-content__icon-container"
        v-if="canActionServiceFailure && !jobHasActiveServiceFailure"
        @click="addNoteToJob(jobId, true, false)"
      >
        <v-icon class="expanded-content__icon">
          fad fa-exclamation-circle
        </v-icon>
        <span class="expanded-content__icon-tooltip left-align">
          Service Failure
        </span>
      </span>
      <span
        class="expanded-content__icon-container failure"
        v-if="jobHasActiveServiceFailure"
        @click="addNoteToJob(jobId, true, false)"
      >
        <v-icon class="expanded-content__icon" color="error">
          fad fa-exclamation-circle
        </v-icon>
        <span class="expanded-content__icon-tooltip left-align">
          Remove Service Failure
        </span>
      </span>
      <span
        class="expanded-content__icon-container delete"
        v-if="canActionServiceFailure"
        @click="addNoteToJob(jobId, false, true)"
      >
        <v-icon class="expanded-content__icon"> far fa-trash-alt </v-icon>
        <span class="expanded-content__icon-tooltip left-align">
          Cancel Job
        </span>
      </span>
      <span
        class="expanded-content__icon-container"
        v-if="
          workStatus >= WorkStatus.PREALLOCATED &&
          workStatus <= WorkStatus.IN_PROGRESS &&
          !isVertical
        "
        @click="deallocateJob(jobId)"
      >
        <v-icon class="expanded-content__icon"> far fa-step-backward </v-icon>
        <span class="expanded-content__icon-tooltip left-align">
          Deallocate
        </span>
      </span>
      <span
        class="expanded-content__icon-container"
        @click="finaliseAllocation(jobId)"
        v-if="workStatus === WorkStatus.PREALLOCATED && !isVertical"
      >
        <v-icon class="expanded-content__icon"> far fa-play-circle </v-icon>
        <span class="expanded-content__icon-tooltip left-align">
          Allocate Job
        </span>
      </span>
      <span
        class="expanded-content__icon-container edit"
        v-if="
          (workStatus >= WorkStatus.BOOKED &&
            workStatus < WorkStatus.DRIVER_COMPLETED) ||
          statusList.includes(45)
        "
        @click="editJobInBookingScreen(jobId)"
      >
        <v-icon class="expanded-content__icon"> far fa-edit </v-icon>
        <span class="expanded-content__icon-tooltip left-align">
          Edit Job
        </span>
      </span>

      <v-spacer v-if="!isVertical"></v-spacer>
      <div style="max-width: 55%">
        <AllocateDriver
          class="mr-1"
          :key="`${keyIncrementer}-${workStatus}-${selectedJobId}`"
          v-if="
            !isVertical &&
            (currentIndex === 0 || windowMode) &&
            (workStatus === WorkStatus.BOOKED ||
              workStatus === WorkStatus.PREALLOCATED) &&
            !showJobDetailsDialog
          "
          :isExternalWindow="windowMode"
          :fleetAssetId.sync="selectedPreAllocatedVehicle"
          :driverId.sync="selectedPreAllocatedDriver"
          :serviceTypeId="serviceTypeId"
          :rateTypeId="rateToApplyToDriver"
          :fleetAssetRates="fleetAssetRates"
          :jobId="jobId"
          :small="true"
          :clientId="clientId"
          :type="ObjectToAllocate.JOB_DETAILS"
          :onValidSelection="OnValidAllocationTarget.SEND"
          :isFormDisabled="
            workStatus !== WorkStatus.BOOKED ||
            selectedJobId !== jobId ||
            statusList?.includes(57)
          "
          :searchDate="jobDate"
          ref="allocateDriverComponent"
        />
      </div>
      <span
        class="expanded-content__icon-container accent-type"
        v-if="
          !isVertical &&
          currentJobListTab &&
          currentJobListTab === 'START_OF_DAY_CHECKLIST'
        "
        @click="addNoteToJob(jobId, false, false, false, true)"
      >
        <v-icon class="expanded-content__icon"> far fa-check-double </v-icon>
        <span class="expanded-content__icon-tooltip right-align">
          Mark as Checked
        </span>
      </span>
      <span
        class="expanded-content__icon-container"
        v-if="workStatus >= WorkStatus.DRIVER_COMPLETED"
        @click="viewSelectedJobInReview(jobId)"
      >
        <v-icon class="expanded-content__icon"> far fa-sack-dollar </v-icon>
        <span class="expanded-content__icon-tooltip right-align">
          Change Job Fees
        </span>
      </span>
      <span
        class="expanded-content__icon-container"
        v-if="!isVertical"
        @click="addNoteToJob(jobId)"
      >
        <v-icon class="expanded-content__icon"> far fa-sticky-note </v-icon>
        <span class="expanded-content__icon-tooltip right-align">
          Add Note
        </span>
      </span>
      <span
        class="expanded-content__icon-container"
        :class="workStatus === WorkStatus.BOOKED ? 'disabled' : ''"
        v-if="!isOutsideHire"
        @click="sendMessageToDriver(jobId)"
      >
        <v-icon class="expanded-content__icon"> far fa-sms </v-icon>
        <span class="expanded-content__icon-tooltip right-align">
          Message Driver
        </span>
      </span>
      <span
        class="expanded-content__icon-container"
        v-if="!isVertical"
        @click="addNoteToJob(jobId, false, false, true)"
      >
        <v-icon class="expanded-content__icon"> far fa-clipboard </v-icon>
        <span class="expanded-content__icon-tooltip right-align">
          Dispatch Note
        </span>
      </span>

      <span
        class="expanded-content__icon-container jobDetails"
        v-if="!isVertical"
        @click="viewJobDetailsDialog(jobId)"
      >
        <v-layout class="view-job-details-btn">
          <span
            class="expanded-content__text"
            v-if="!isVertical"
            style="margin-left: 4px"
            >View Job Details</span
          >
          <v-icon class="expanded-content__icon" style="padding-left: 16px">
            far fa-eye
          </v-icon>
        </v-layout>
      </span>
    </v-layout>
    <div
      class="expanded-content__overlay"
      :class="
        overlayActive && jobDetails === null
          ? 'overlay-active'
          : 'overlay-inactive'
      "
    >
      <v-layout
        justify-center
        pt-4
        v-if="overlayActive && jobDetails === null && !noJobDetailsFound"
      >
        <v-progress-circular
          indeterminate
          color="white"
          size="24"
        ></v-progress-circular>
      </v-layout>
      <v-layout justify-center pt-4 v-if="noJobDetailsFound">
        <v-icon color="red" size="18">fas fa-exclamation-triangle</v-icon>
        <span class="pl-2 white--text" style="font-size: 14px; font-weight: 500"
          >This job could not be loaded. Please try again, or contact GoDesta
          Support if problems persist.
        </span>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import AllocateDriver from '@/components/common/allocate_driver/allocate_driver.vue';
import {
  ObjectToAllocate,
  OnValidAllocationTarget,
} from '@/helpers/AllocationHelpers.ts/AllocationHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { JobAllocationOrder } from '@/interface-models/Company/DivisionCustomConfig/Operations/JobAllocationOrder';
import FleetAssetDriverListSummary from '@/interface-models/FleetAsset/FleetAssetDriverListSummary';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import AddNoteToJobRequest from '@/interface-models/Generic/Communication/AddNoteToJobRequest';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import { JobEventType } from '@/interface-models/Jobs/Event/JobEventType';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useAllocationStore } from '@/store/modules/AllocationStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useMittListener } from '@/utils/useMittListener';
import {
  computed,
  ComputedRef,
  onBeforeUnmount,
  onMounted,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

// ==================== Props ====================
const props = withDefaults(
  defineProps<{
    windowMode?: boolean;
    isVertical?: boolean;
    isOutsideHire?: boolean;
    jobId: number;
    driverId?: string;
    statusList: number[];
    currentIndex?: number;
    openWindowActionIncrementer?: number;
    currentJobListTab?: string;
    clientRateTypeId?: number;
    driverIsTripRate?: boolean;
    fleetAssetId?: string;
    serviceTypeId?: number;
    jobDate?: number;
    clientId?: string;
    jobDetails: JobDetails | null;
    selectedJobId?: number | null;
    workStatus?: WorkStatus;
    hideTextInputs?: boolean;
  }>(),
  {
    driverId: '',
    fleetAssetId: '',
    openWindowActionIncrementer: 0,
    currentJobListTab: '',
    clientRateTypeId: 0,
    driverIsTripRate: false,
    serviceTypeId: 1,
    jobDate: 0,
    clientId: '',
    workStatus: WorkStatus.BOOKED,
    selectedJobId: null,
    currentIndex: 0,
  },
);

const emit = defineEmits<{
  (e: 'editJobInBookingScreen', jobId: number): void;
  (e: 'allocatePreallocatedJob', jobIds: number[]): void;
  (e: 'deallocateJob', jobId: number): void;
  (
    e: 'addNoteToJob',
    payload: {
      jobId: number;
      serviceFailure: boolean;
      cancelJob: boolean;
      dispatchNote: boolean;
      startOfDayCheckNote: boolean;
    },
  ): void;
  (e: 'viewJobDetailsDialog', jobId: number): void;
  (e: 'sendMessageToDriver', jobId: number): void;
  (e: 'viewSelectedJobInReview', jobId: number): void;
}>();

// ==================== Stores ====================
const operationsStore = useOperationsStore();
const jobStore = useJobStore();
const allocationStore = useAllocationStore();

// ==================== State ====================
const pendingOutsideHireCommunication: Ref<Communication | null> = ref(null);
const selectedPreAllocatedVehicle: Ref<string> = ref('');
const selectedPreAllocatedDriver: Ref<string> = ref('');
const fleetAssetRates: Ref<JobPrimaryRate[]> = ref([]);
const overlayActive: Ref<boolean> = ref(false);
const noJobDetailsFound: Ref<boolean> = ref(false);
const keyIncrementer: Ref<number> = ref(1);
const overlayTimeout: Ref<ReturnType<typeof setTimeout> | undefined> = ref();
const noJobDetailsFoundTimeout: Ref<ReturnType<typeof setTimeout> | undefined> =
  ref();

const allocateDriverComponent = ref<InstanceType<typeof AllocateDriver> | null>(
  null,
);

// ==================== Computed ====================
/**
 * Returns the rate type to apply to the driver.
 */
const rateToApplyToDriver: ComputedRef<JobRateType> = computed(() => {
  if (props.driverIsTripRate) {
    return JobRateType.TRIP;
  } else if (props.clientRateTypeId === JobRateType.TRIP) {
    return JobRateType.TIME;
  } else {
    return props.clientRateTypeId;
  }
});

/**
 * Returns true if the job has an active service failure.
 */
const jobHasActiveServiceFailure: ComputedRef<boolean> = computed(() => {
  return (props.statusList ?? []).includes(54);
});

/**
 * Returns true if the customer has been contacted.
 */
const customerContacted: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return (props.statusList ?? []).includes(50);
  },
  set() {
    jobStore.updateJobStatus(props.jobId, JobEventType.CustomerContacted);
  },
});

/**
 * Returns true if the driver has been contacted.
 */
const driverContacted: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return (props.statusList ?? []).includes(51);
  },
  set() {
    jobStore.updateJobStatus(props.jobId, JobEventType.DriverContacted);
  },
});

/**
 * Returns true if the job has been actioned.
 */
const jobActioned: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return (props.statusList ?? []).includes(52);
  },
  set() {
    jobStore.updateJobStatus(props.jobId, JobEventType.JobActioned);
  },
});

/**
 * Returns true if the job has been delayed.
 */
const jobDelayed: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return (props.statusList ?? []).includes(53);
  },
  set() {
    jobStore.updateJobStatus(props.jobId, JobEventType.JobDelayed);
  },
});

/**
 * Returns true if the user can action a service failure.
 */
const canActionServiceFailure: ComputedRef<boolean> = computed(() => {
  return (
    props.workStatus >= WorkStatus.BOOKED &&
    props.workStatus <= WorkStatus.IN_PROGRESS
  );
});

/**
 * Returns true if the job details or job notes dialog is open.
 */
const showJobDetailsDialog: ComputedRef<boolean> = computed(() => {
  return (
    operationsStore.viewingJobDetailsDialog ||
    operationsStore.viewingJobNotesDialog
  );
});

// ==================== Methods ====================

/**
 * Edits the job in the booking screen.
 * @param jobId - The job ID.
 */
function editJobInBookingScreen(jobId: number): void {
  if (props.windowMode) {
    emit('editJobInBookingScreen', jobId);
  } else {
    operationsStore.setSelectedBookingScreenJobId(jobId);
  }
}

/**
 * Finalises the allocation for a job.
 * @param jobId - The job ID.
 */
function finaliseAllocation(jobId: number): void {
  if (props.windowMode) {
    emit('allocatePreallocatedJob', [jobId]);
  } else {
    allocationStore.allocatePreAllocatedJobIds([jobId]);
  }
}

/**
 * Called when the user clicks the deallocate button.
 * If the job is at PREALLOCATED status, the job will be deallocated without further steps.
 * If it has passed preallocated, a confirmation dialog will display to get the user to confirm before proceeding.
 * @param jobId - The job ID.
 */
function deallocateJob(jobId: number): void {
  if (!props.jobDetails) {
    return;
  }
  const workStatus = props.jobDetails.workStatus ?? WorkStatus.BOOKED;
  const displayId = props.jobDetails.recurringJobId
    ? props.jobDetails.recurringJobId
    : props.jobDetails.jobId;

  if (workStatus > WorkStatus.PREALLOCATED) {
    let message = '';
    switch (workStatus) {
      case WorkStatus.ALLOCATED:
      case WorkStatus.ACCEPTED:
        message = `This job (#${displayId}) has already been allocated and sent to the driver. Are you sure you want to deallocate this job?`;
        break;
      case WorkStatus.IN_PROGRESS:
        message = `This job (#${displayId}) is currently in progress. Are you sure you want to deallocate this job?`;
        break;
      default:
        message = `This job (#${displayId}) is currently complete. Are you sure you want to deallocate this job?`;
    }
    if (!window.confirm(message)) {
      return;
    }
  }
  if (props.windowMode) {
    emit('deallocateJob', jobId);
  } else {
    jobStore.updateJobStatus(jobId, JobEventType.DeallocateJob);
  }
}

/**
 * Adds a note to a job.
 */
function addNoteToJob(
  jobId: number,
  serviceFailure: boolean = false,
  cancelJob: boolean = false,
  dispatchNote: boolean = false,
  startOfDayCheckNote: boolean = false,
): void {
  if (props.windowMode) {
    emit('addNoteToJob', {
      jobId,
      serviceFailure,
      cancelJob,
      dispatchNote,
      startOfDayCheckNote,
    });
  } else {
    operationsStore.setSelectedJobId(jobId);
    operationsStore.setViewingJobNotesDialog(true);
    operationsStore.setJobServiceFailure(serviceFailure);
    operationsStore.setJobCancellation(cancelJob);
    operationsStore.setDispatchNote(dispatchNote);
    operationsStore.setStartOfDayCheckNote(startOfDayCheckNote);
  }
}

/**
 * Opens the job details dialog.
 * @param jobId - The job ID.
 */
function viewJobDetailsDialog(jobId: number): void {
  if (props.windowMode) {
    emit('viewJobDetailsDialog', jobId);
  } else {
    operationsStore.setSelectedJobId(jobId);
    operationsStore.setViewingJobDetailsDialog(true);
  }
}

/**
 * Sends a message to the driver for the job.
 * @param jobId - The job ID.
 */
function sendMessageToDriver(jobId: number): void {
  if (props.windowMode) {
    emit('sendMessageToDriver', jobId);
  } else {
    const messageContent: string = '';
    const driverId = props.jobDetails
      ? props.jobDetails.driverId
      : props.driverId;

    if (!driverId) {
      return;
    }
    useDriverMessageStore().setMessageContent(messageContent);
    operationsStore.setSelectedJobId(jobId);
    operationsStore.setSelectedJobDetailsDialogTab('MSG');
    operationsStore.setViewingJobDetailsDialog(true);
  }
}

/**
 * Opens the job review dialog.
 * @param jobId - The job ID.
 */
function viewSelectedJobInReview(jobId: number): void {
  if (props.windowMode) {
    emit('viewSelectedJobInReview', jobId);
  } else {
    operationsStore.setSelectedJobId(jobId);
    operationsStore.setSelectedJobDetailsDialogTab('PRI');
    operationsStore.setViewingJobDetailsDialog(true);
  }
}

/**
 * Updates allocation state based on work status, fleet asset, and driver.
 */
function setAllocationUpdates({
  workStatus = props.workStatus,
  fleetAssetId = props.fleetAssetId,
  driverId = props.driverId,
}: {
  workStatus?: WorkStatus;
  fleetAssetId?: string;
  driverId?: string;
} = {}): void {
  if (workStatus === WorkStatus.BOOKED) {
    selectedPreAllocatedDriver.value = '';
    selectedPreAllocatedVehicle.value = '';
    keyIncrementer.value++;
    return;
  }
  if (workStatus === WorkStatus.PREALLOCATED) {
    selectedPreAllocatedVehicle.value = fleetAssetId;
    selectedPreAllocatedDriver.value = driverId;
    keyIncrementer.value++;
    return;
  }
}

/**
 * Updates fleet asset rates if required based on job details and work status.
 */
function updateFleetAssetRatesIfRequired(jobDetails: JobDetails | null): void {
  if (!jobDetails) {
    return;
  }
  if (props.workStatus === WorkStatus.BOOKED) {
    const isDriverTripRate = jobDetails.accounting.isDriverTripRate;
    if (isDriverTripRate) {
      fleetAssetRates.value = deepCopy(jobDetails.accounting.fleetAssetRates);
    } else {
      fleetAssetRates.value = [new JobPrimaryRate()];
    }
  } else if (props.workStatus === WorkStatus.PREALLOCATED) {
    fleetAssetRates.value = deepCopy(jobDetails.accounting.fleetAssetRates);
  }
}

/**
 * Sets timeouts for job details loading and error display.
 */
function setJobDetailsTimeouts(): void {
  overlayTimeout.value = setTimeout(() => {
    overlayActive.value = true;
  }, 1500);
  noJobDetailsFoundTimeout.value = setTimeout(() => {
    if (props.jobDetails === null) {
      noJobDetailsFound.value = true;
    }
  }, 10000);
}

/**
 * Handles job status updates from deallocation and preallocation events.
 * @param payload - The job event summary.
 */
function handleJobStatusUpdate(payload: JobEventSummary | null): void {
  if (
    !payload?.event ||
    !payload?.jobDetails ||
    payload?.jobId !== props.jobId
  ) {
    return;
  }
  payload.jobDetails = initialiseJobDetails(payload.jobDetails);
  if (payload.event === 'DeallocateJob') {
    selectedPreAllocatedDriver.value = '';
    selectedPreAllocatedVehicle.value = '';
    setAllocationUpdates({
      workStatus: payload.jobDetails.workStatus,
      fleetAssetId: payload.jobDetails.fleetAssetId,
      driverId: payload.jobDetails.driverId,
    });
  }
  if (payload.event === 'PreAllocateJob') {
    if (pendingOutsideHireCommunication.value !== null) {
      const request: AddNoteToJobRequest = {
        jobId: props.jobId,
        note: pendingOutsideHireCommunication.value,
      };
      jobStore.addNoteToJob(request);
      pendingOutsideHireCommunication.value = null;
    }
    setAllocationUpdates({
      workStatus: payload.jobDetails.workStatus,
      fleetAssetId: payload.jobDetails.fleetAssetId,
      driverId: payload.jobDetails.driverId,
    });
  }
  if (payload.event === 'UpdateJobDetails') {
    updateFleetAssetRatesIfRequired(payload.jobDetails);
  }
}

/**
 * Handles division-level updates to job allocation.
 * @param jobIds - Array of job IDs.
 */
function handleAllocatedJobIds(jobIds: number[] | null): void {
  if (jobIds?.includes(props.jobId)) {
    setAllocationUpdates();
  }
}

/**
 * Used to handle the drop event when a driver or fleet asset is dropped into
 * the action buttons component, dragged from the fleet/driver list
 * @param event - The drag event that contains the driver or fleet asset data.
 */
function onDrop(event: DragEvent): void {
  try {
    // Parse the driver object from the drag event
    const driverData = event.dataTransfer?.getData('application/json');
    if (!driverData) {
      throw new Error('No driver data found in the drop event.');
    }

    callAllocationInChild(JSON.parse(driverData));
  } catch (error) {
    logConsoleError('Error parsing driver data from drop event:', error);
  }
}

/**
 * Calls the allocation methods in the child component based on the assetDetails
 * provided. This is used when a driver/fleet item is dragged from the
 * OperationsFleetList component into this one, indicating that the job should
 * be allocated to that driver/fleet asset.
 * @param assetDetails - The details of the asset or driver being allocated.
 */
async function callAllocationInChild(
  assetDetails: FleetAssetDriverListSummary,
): Promise<void> {
  try {
    const allocationComponent = allocateDriverComponent.value;

    if (!allocationComponent) {
      throw new Error('Allocation component is not available.');
    }
    // Get the allocation order so we know what order the inputs are being
    // presented in in the UI
    const allocationOrder =
      useCompanyDetailsStore().divisionCustomConfig?.operations
        ?.jobAllocationOrder ?? JobAllocationOrder.VEHICLE_FIRST;

    // Set the fleetAssetId and driverId that we will pass into the child, based
    // on the type of the dragged item
    const fleetAssetId =
      assetDetails.type === SubcontractorEntityType.FLEET_ASSET
        ? assetDetails.entityId
        : assetDetails.activeItem?.entityId ?? '';
    const driverId =
      assetDetails.type === SubcontractorEntityType.DRIVER
        ? assetDetails.entityId
        : assetDetails.activeItem?.entityId ?? '';

    // Depending on the type of the assetDetails, call the functions in child
    // component to validate selection
    if (allocationOrder === JobAllocationOrder.VEHICLE_FIRST) {
      // Set vehicle then driver
      allocationComponent.selectedFleetAssetId = fleetAssetId;
      allocationComponent.selectedDriverId = driverId;
      const isValid =
        await allocationComponent.validateSelectedVehicle(fleetAssetId);
      if (isValid) {
        allocationComponent.validateSelectedDriver(driverId, true);
      }
    } else {
      // Set driver then vehicle
      allocationComponent.selectedDriverId = driverId;
      allocationComponent.selectedFleetAssetId = fleetAssetId;
      const isValid = allocationComponent.validateSelectedDriver(
        driverId,
        true,
      );
      if (isValid) {
        allocationComponent.validateSelectedVehicle(fleetAssetId, true);
      }
    }
  } catch (error) {
    logConsoleError(
      `Error calling allocation methods in child component:`,
      error,
    );
  }
}

// ==================== Watchers & Lifecycle ====================

watch(
  () => props.openWindowActionIncrementer,
  () => {
    setAllocationUpdates({
      workStatus: props.workStatus,
      fleetAssetId: props.fleetAssetId,
      driverId: props.driverId,
    });
  },
);

watch(
  () => props.selectedJobId,
  (selectedJobId: number | null | undefined) => {
    if (selectedJobId === props.jobId) {
      updateFleetAssetRatesIfRequired(props.jobDetails);
    }
  },
  { immediate: true },
);

onMounted(() => {
  setJobDetailsTimeouts();
  setAllocationUpdates({ workStatus: props.workStatus });
});

onBeforeUnmount(() => {
  if (overlayTimeout.value) {
    clearTimeout(overlayTimeout.value);
  }
  if (noJobDetailsFoundTimeout.value) {
    clearTimeout(noJobDetailsFoundTimeout.value);
  }
});

useMittListener('allocatedPreAllocatedJobIds', handleAllocatedJobIds);
useMittListener('jobStatusUpdate', handleJobStatusUpdate);
</script>
<style scoped lang="scss">
.job-list-action-buttons {
  z-index: 99999 !important;
  position: relative;
  // overflow: hidden;
  .expanded-content__overlay {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 100%;
    width: 100%;
    transition: 0.3s;

    &.overlay-active {
      background-color: $summary-bg;
      pointer-events: all;
    }
    &.overlay-inactive {
      background-color: none;
      pointer-events: none;
    }
  }
  .expanded-content__container {
    // padding: 8px 8px 34px 8px;
    padding: 8px 8px;
    background-color: var(--background-color-250);
    width: 100%;
    display: flex;
    // height: 80px;
    // overflow: visible !important;

    // border-bottom: 0.5px solid $accent;

    &.vertical-list {
      padding: 0px;
      background-color: transparent;
      position: relative;
      .expanded-content__icon-container {
        padding: 4px 8px;
        margin: 0px;
        border: none;
        background-color: transparent;
        width: 100%;
        text-align: center;
        display: block;
        &:hover {
          background-color: $translucent;
          .expanded-content__icon-tooltip {
            right: 0% !important;
            visibility: visible;
            position: fixed !important;
            &.left-align {
              left: 115%;
              top: 0px;
            }
            &.right-align {
              left: 115%;
              top: 0px;
            }
          }
        }
      }
    }

    .expanded-content__icon-container {
      padding: 6px 10px;
      margin-right: 2px;
      margin-left: 2px;
      border: 1.5px solid #6b6d80;
      border-radius: $border-radius-base;
      position: relative;

      &.delete {
        border-color: $error !important;
        cursor: pointer;

        &:hover {
          background-color: $toast-error-bg;
        }
        .expanded-content__icon-tooltip {
          color: white;
          background-color: $toast-error-border;
        }
      }

      &.edit {
        border-color: $highlight !important;
        cursor: pointer;

        &:hover {
          background-color: $toast-info-bg;
        }
        .expanded-content__icon-tooltip {
          color: white;
          background-color: $toast-info-border;
        }
      }

      &.failure {
        border-color: var(--text-color) !important;
        cursor: pointer;

        &:hover {
          background-color: $toast-error-bg;
        }
        .expanded-content__icon-tooltip {
          color: white;
          background-color: $toast-error-border;
        }
      }

      &.jobDetails {
        border-color: var(--primary-light) !important;
        cursor: pointer;

        &:hover {
          background-color: var(--primary-dark);
        }
      }

      .expanded-content__text {
        font-size: $font-size-11;
        font-weight: 600;
        text-transform: uppercase;
        color: var(--text-color);
        padding-right: 5px;
      }

      &.disabled {
        pointer-events: none;
        opacity: 0.4;
      }

      &:hover {
        background-color: var(--primary-dark);
        cursor: pointer;
        filter: brightness(120%);
        border-color: var(--primary-light);

        .expanded-content__icon-tooltip {
          top: 110%;
          visibility: visible;
          position: fixed !important;
          &.left-align {
            left: 0px;
          }
          &.right-align {
            right: 0px;
          }
        }
      }
      .expanded-content__icon {
        font-size: $font-size-12;
        color: var(--text-color);
      }
      .expanded-content__icon-tooltip {
        position: absolute;
        visibility: hidden;
        padding: 4px 6px;
        background-color: var(--background-color-400);
        border: 2px solid $translucent;
        color: var(--text-color);
        border-radius: 20px;
        box-shadow: 0px 8px 32px var(--shadow-color) !important;
        background-color: var(--background-color-300);
        font-size: $font-size-large;
        font-weight: 500;
        width: 126px;

        text-align: center;

        &.left-align {
          left: 0px;
        }
        &.right-align {
          right: 0px;
        }
        &.driverCon {
          color: white;
          background-color: $toast-info-border;
        }
        &.cusCon {
          color: white;
          background-color: $toast-success-border;
        }
        &.jobAct {
          color: white;
          background-color: $toast-warning-bg;
        }
        &.jobDel {
          color: white;
          background-color: $toast-error-border;
        }
      }

      &.accent-type {
        background-color: var(--highlight);
        border: 1px solid $translucent;
        .expanded-content__text {
          color: var(--text-color);
        }
        .expanded-content__icon {
          color: var(--text-color);
        }
      }
    }
  }
}

.view-job-details-btn {
  width: 144px;
}
</style>
