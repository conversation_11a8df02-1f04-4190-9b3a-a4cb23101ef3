import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import OperationsSettingsDialog from '@/components/common/ui-elements/operations_settings_dialog.vue';
import JobListActionButtons from '@/components/operations/OperationDashboard/components/JobList/job_list_action_buttons/job_list_action_buttons.vue';
import {
  jobListHeadersPointManager,
  jobListHeadersWithStatus,
} from '@/components/operations/OperationDashboard/components/JobList/job_list_headers';
import UnassignedPudListActionButtons from '@/components/operations/OperationDashboard/components/JobList/unassigned_pud_list_action_buttons/index.vue';
import {
  LOCAL_STORAGE_POPOUT_JOB_LIST_GROUPING,
  LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS,
  SESSION_STORAGE_TOKEN,
  initJobListGroupingFromLocalStorage,
  initOperationsDashboardSettingsFromLocalStorage,
} from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { initialiseFleetAssetSummary } from '@/helpers/classInitialisers/InitialiseFleetAsset';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { returnStartOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  BroadcastChannelType,
  BroadcastIds,
  returnOperationsBroadcastChannelId,
} from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import { returnCurrentExactJobStatus } from '@/helpers/StatusHelpers/StatusHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { JobStatistics } from '@/interface-models/Generic/DivisionJobsSummary/JobsStatistics';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import { JobListStatusGrouping } from '@/interface-models/Generic/OperationScreenOptions/JobListStatusGrouping';
import OperationsDashboardSetting, {
  jobListDefaultSettings,
} from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { AccountingStatus } from '@/interface-models/Jobs/AccountingStatus';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobSourceType } from '@/interface-models/Jobs/JobSourceType';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { PUDItemShort } from '@/interface-models/Jobs/PUD/PUDItemShort';
import UnassignedPudItemResponse from '@/interface-models/Jobs/PUD/UnassignedPudItem/SaveUnassignedPudItemResponse';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStatisticsStore } from '@/store/modules/JobStatisticsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import * as jwt from 'jose';
import moment from 'moment-timezone';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

interface JobsList {
  name: string;
  jobs: OperationJobSummary[];
}

interface StatusFilterMapping {
  id: JobListStatusGrouping;
  match: WorkStatus | null;
  from: WorkStatus | null;
  to: WorkStatus | null;
  name: string;
}

enum JobListTabs {
  JOB_LIST = 'JOB_LIST',
  REQUIRES_ATTENTION = 'REQUIRES_ATTENTION',
  COMPLETED_WORK = 'COMPLETED_WORK',
  POINT_MANAGER = 'POINT_MANAGER',
  START_OF_DAY_CHECKLIST = 'START_OF_DAY_CHECKLIST',
  BOOKED_BY_ME = 'BOOKED_BY_ME',
  FILTERED_SELECTION = 'FILTERED_SELECTION',
}

@Component({
  components: {
    JobListActionButtons,
    DatePickerBasic,
    OperationsSettingsDialog,
    UnassignedPudListActionButtons,
  },
})
export default class JobMonitoringWindow extends Vue {
  @Prop() public windowMode: boolean;
  @Prop() public currentIndex: number;
  @Prop() public selectedJobDetails: JobDetails;

  public fleetAssetStore = useFleetAssetStore();
  public companyDetailsStore = useCompanyDetailsStore();
  public dataImportStore = useDataImportStore();
  public operationsStore = useOperationsStore();
  public driverDetailsStore = useDriverDetailsStore();
  public filterStore = useFilterStore();

  public JobSourceType = JobSourceType;
  public WorkStatus = WorkStatus;
  public filterSettings: OperationsDashboardSetting[] = jobListDefaultSettings;
  // public broadcast: BroadcastChannel | null = null;

  public driverInputText: string = '';
  public fleetAssetInputText: string = '';
  public currentServiceTypeId: number = 0;
  public currentRateTypeId: number = 0;

  public rerenderTrigger: boolean = true;
  public mainWindowRoute: string = 'operations_index';
  public openWindowActionIncrementer: number = 0;
  public selectedJobTypeTab: JobListTabs = JobListTabs.JOB_LIST;

  public requestingJobId: number = -1;

  public showActionsDisabledAlert: boolean = false;
  public actionsDisabledAlertTimeout: ReturnType<typeof setTimeout>;

  public localJobDetails: JobDetails | null = null;

  public activeUser = sessionManager.getActiveUser();

  get jobListSettings(): OperationsDashboardSetting[] {
    return JSON.parse(
      JSON.stringify(this.operationsStore.dashboardSettings.jobList),
    );
  }

  get jobDetails() {
    if (this.windowMode) {
      return this.localJobDetails;
    } else {
      return this.selectedJobDetails;
    }
  }

  get selectedStatusToFilter() {
    return this.operationsStore.selectedJobListStatusGrouping;
  }
  set selectedStatusToFilter(value: JobListStatusGrouping) {
    if (value) {
      this.operationsStore.updateJobListStatusGrouping(value);
    }
  }

  get activeSettingsGroupType(): string {
    const foundActive = this.jobListSettings.find((s) => s.active);
    const activeType = foundActive
      ? foundActive.id
      : this.jobListSettings[0]
        ? this.jobListSettings[0].id
        : '';
    this.scrollToListTop();
    return activeType;
  }

  get tableHeaders() {
    let headers: any[] = [];
    if (this.selectedTabController !== JobListTabs.POINT_MANAGER) {
      headers = jobListHeadersWithStatus;
      let colCount = 9;
      const foundStatusItem = headers.find((h) => h.value === 'status');
      if (foundStatusItem) {
        if (this.showStatusColumn) {
          colCount++;
          foundStatusItem.align = 'left';
        } else {
          foundStatusItem.align = ' d-none';
        }
      }
      const foundDispatchNoteItem = headers.find(
        (h) => h.value === 'dispatchNote',
      );
      if (foundDispatchNoteItem) {
        if (this.windowMode) {
          colCount++;
          foundDispatchNoteItem.align = 'left';
        } else {
          foundDispatchNoteItem.align = ' d-none';
        }
      }
      headers.map((h) => {
        h.class = `job-table-header--${colCount}`;
        return h;
      });
      return headers;
    } else {
      return jobListHeadersPointManager;
    }
  }

  /**
   * Returns a boolean which determines whether the job list table displays the
   * current status of the job. This is only displayed when the job list when
   * the grouping on the table doesn't make it clear what status hte job is at,
   * and only in the job list popout where we have the additional space.
   */
  get showStatusColumn(): boolean {
    const isAllJobsView =
      this.selectedTabController === JobListTabs.JOB_LIST &&
      this.selectedStatusToFilter === JobListStatusGrouping.ALL_JOBS;
    const isRequiresAttention =
      this.selectedTabController === JobListTabs.REQUIRES_ATTENTION;
    return (isAllJobsView || isRequiresAttention) && this.windowMode;
  }

  public pagination: VPagination = {
    sortBy: 'jobId',
    descending: false,
    rowsPerPage: -1,
  };

  get operationsOptions() {
    return this.operationsStore.operationOptions;
  }

  get driverList(): DriverDetailsSummary[] {
    return this.driverDetailsStore.getDriverList;
  }

  // Handler to scroll to top on tab change
  get selectedTabController(): JobListTabs {
    return this.selectedJobTypeTab;
  }
  set selectedTabController(value: JobListTabs) {
    if (value !== this.selectedJobTypeTab) {
      this.scrollToListTop();
    }
    // If the newly selected tab is not a filtered selection we clear the old selection in useJobStatisticsStore
    if (value !== JobListTabs.FILTERED_SELECTION) {
      useJobStatisticsStore().setFilteredSelection(null);
    }
    this.selectedJobTypeTab = value;
  }
  // Scrolls component back to top of list
  public scrollToListTop(): void {
    const element = document.getElementById('job-list-table-content');
    if (element) {
      element.scrollTop = 0;
    }
  }

  // List of all Fleet Assets
  get fleetAssetList() {
    return this.fleetAssetStore.getAllFleetAssetList ?? [];
  }

  get dateRange() {
    return this.operationsStore.jobListDateFilter;
  }
  // Handles emit from DatePickerBasic component. Uses the epochTime to set the
  // date range through which jobs are filtered
  public setDateRange(epoch: number) {
    this.operationsStore.setJobListDateFilter(epoch);
  }

  // Handle emit from settings dialog component
  // Update Operations Module
  public tableSettingsUpdated(settingsList: OperationsDashboardSetting[]) {
    this.operationsStore.updateDashboardSettingsJobList(settingsList);
  }

  public closeDriverValue() {
    this.driverInputText = '';
    this.fleetAssetInputText = '';
  }

  public openJobList() {
    this.$emit('openJobList');
  }
  // public openPudMaintenanceDialog() {
  //   const config: PudMaintenanceDialogConfig = {
  //     operationType: JobOperationType.NEW,
  //     pudType: PudMaintenanceType.UNASSIGNED,
  //   };
  //   this.operationsStore.setPudMaintenanceDialogConfig(config);
  //   this.operationsStore.setViewingPudMaintenanceDialog(true);
  // }

  get timezone() {
    return this.companyDetailsStore.userLocale;
  }

  public showLateJobHighlight(
    epochTime: number,
    status: string,
    minutesMinimum: number = 60,
  ): boolean {
    const allowedStatuses = [
      'UNALLOCATED',
      'DEALLOCATED',
      'ALLOCATED',
      'PREALLOCATED',
      'ACCEPTED',
    ];
    if (!allowedStatuses.includes(status)) {
      return false;
    }
    return this.returnTimeDifference(epochTime) > minutesMinimum;
  }
  public returnTimeDifference(epochTime: number | null): number {
    if (!epochTime) {
      return 0;
    }
    const timeNow = moment.tz(this.timezone).valueOf();
    const oneHour = moment.duration(8, 'hours').asMilliseconds();

    if (timeNow > epochTime && timeNow < epochTime + oneHour) {
      const lateness = timeNow - epochTime;
      const latenessAsMinutes = Math.floor(
        moment.duration(lateness).asMinutes(),
      );
      if (latenessAsMinutes > 0) {
        return latenessAsMinutes;
      }
    }
    return 0;
  }

  get allJobsList(): OperationJobSummary[] {
    return useJobStore().operationJobsList;
  }

  get statusTypes(): StatusFilterMapping[] {
    return [
      {
        id: JobListStatusGrouping.ALL_JOBS,
        match: null,
        from: WorkStatus.BOOKED,
        to: WorkStatus.REVIEWED,
        name: 'All Jobs',
      },
      {
        id: JobListStatusGrouping.UNALLOCATED,
        match: null,
        from: WorkStatus.BOOKED,
        to: WorkStatus.PREALLOCATED,
        name: 'Unallocated',
      },
      {
        id: JobListStatusGrouping.ALLOCATED,
        match: WorkStatus.ALLOCATED,
        from: null,
        to: null,
        name: 'Allocated',
      },
      {
        id: JobListStatusGrouping.ACCEPTED,
        match: WorkStatus.ACCEPTED,
        from: null,
        to: null,
        name: 'Accepted',
      },
      {
        id: JobListStatusGrouping.STARTED,
        match: WorkStatus.IN_PROGRESS,
        from: null,
        to: null,
        name: 'In-Progress',
      },
      {
        id: JobListStatusGrouping.COMPLETED,
        match: WorkStatus.DRIVER_COMPLETED,
        from: null,
        to: null,
        name: 'Completed',
      },
      {
        id: JobListStatusGrouping.REVIEWED,
        match: WorkStatus.REVIEWED,
        from: null,
        to: null,
        name: 'Reviewed',
      },
    ];
  }

  get jobStatisticsFilteredSelection(): JobStatistics | null {
    return useJobStatisticsStore().filteredSelection;
  }

  @Watch('jobStatisticsFilteredSelection')
  public jobStatisticsFilteredSelectionChange(value: JobStatistics) {
    if (value !== null) {
      this.selectedTabController = JobListTabs.FILTERED_SELECTION;
    }
  }

  // smallerWidthRequired is for width of the status filter select. It helps fix a design problem. eg. when "All Jobs" is selected there is a large space between the text and the drop down arrow. We use this computed property to set correct class name
  get smallerWidthRequired() {
    return this.selectedStatusToFilter !== JobListStatusGrouping.UNALLOCATED;
  }

  get totalUnassignedPudItems(): number {
    // Prevent unassignedPudListForToday getter from being called before the timezone broadcast message has arrived
    if (!this.isValidUserTimeZone) {
      return 0;
    }
    return this.dataImportStore.unassignedPudListForToday.length;
  }
  get totalJobsThatRequireAttention(): number {
    return this.requiresAttention.length;
  }
  get totalCompletedWorkJobs(): number {
    return this.completedWork.length;
  }
  get totalStartOfDayChecklistJobs(): number {
    return this.startOfDayCheckList.length;
  }

  get requiresAttention(): OperationJobSummary[] {
    return this.allJobsList.filter(
      (x: OperationJobSummary) =>
        x.statusList.includes(57) ||
        x.statusList.includes(45) ||
        x.isOutsideHire,
    );
  }

  // Jobs that have all legs finished but the driver is yet to complete the job.
  get completedWork(): OperationJobSummary[] {
    return this.allJobsList.filter(
      (x: OperationJobSummary) =>
        x.workStatus > WorkStatus.IN_PROGRESS ||
        (x.numOfCompletedLegs === x.totalNumberOfLegs &&
          x.workStatus < WorkStatus.DRIVER_COMPLETED),
    );
  }

  get startOfDayCheckList(): OperationJobSummary[] {
    return this.allJobsList.filter(
      (x: OperationJobSummary) => x.startOfDayCheckRequired,
    );
  }

  // returns jobs based on the selected job statistics filtering
  get selectedStatisticJobs(): OperationJobSummary[] {
    if (!this.jobStatisticsFilteredSelection) {
      return [];
    }

    const { key, workStatus, startDate, endDate } =
      this.jobStatisticsFilteredSelection;
    const isNoDateRangeApplied = startDate === 0 && endDate === 0;

    return this.allJobsList.filter((job: OperationJobSummary) => {
      const isWithinDateRange =
        isNoDateRangeApplied ||
        (returnStartOfDayFromEpoch(job.date) >= startDate &&
          returnStartOfDayFromEpoch(job.date) <= endDate);

      const isWithinTodayDateRange =
        isNoDateRangeApplied ||
        (returnStartOfDayFromEpoch(job.bookedAt) >= startDate &&
          returnStartOfDayFromEpoch(job.bookedAt) <= endDate);

      if (key === 'Booked Today By Me') {
        return job.bookedBy === this.activeUser && isWithinTodayDateRange;
      }

      if (!workStatus) {
        return isWithinDateRange;
      }

      return job.workStatus === workStatus && isWithinDateRange;
    });
  }

  // Returns a list of jobs that fit the selectedStatusToFilter. If the status filter fails it will return jobs filtered via date range.
  get jobsBySelectedStatusType(): OperationJobSummary[] {
    // If there is no date range and also no filter options, return all jobs.
    if (!this.dateRange) {
      return this.allJobsList;
    }
    // filter jobs by the date range
    const jobListByDate = this.allJobsList.filter((job) => {
      const startDate = this.dateRange!.startDate;
      const endDate = this.dateRange!.endDate;
      return job.date >= startDate && job.date <= endDate;
    });

    const statusFilterMapping: StatusFilterMapping | undefined =
      this.statusTypes.find(
        (mapping) => mapping.id === this.selectedStatusToFilter,
      );

    if (!statusFilterMapping) {
      return jobListByDate;
    }
    const { match, from, to } = statusFilterMapping;

    return jobListByDate.filter((job: OperationJobSummary) => {
      const workStatus = job.workStatus;
      const isEmptyFilter: boolean = !match && !from && !to;
      if (isEmptyFilter) {
        return false;
      }
      const isWorkStatusMatch = workStatus === match;

      if (isWorkStatusMatch) {
        return true;
      }

      if (!from && !to) {
        return false;
      }

      if ((from && workStatus < from) || (to && workStatus > to)) {
        return false;
      }

      return true;
    });
  }

  get filteredJobList(): OperationJobSummary[] {
    // if selectedTab is requires attention we return all jobs that require attention. No further date range or status filter is applied.
    if (this.selectedTabController === JobListTabs.REQUIRES_ATTENTION) {
      return this.requiresAttention;
    }
    // if selectedTab is COMPLETED_WORK we should display COMPLETED, REVIEWED, and jobs where the final pud item status is FINISHED
    if (this.selectedTabController === JobListTabs.COMPLETED_WORK) {
      return this.completedWork;
    }
    // if selectedTab is COMPLETED_WORK we should display COMPLETED, REVIEWED, and jobs where the final pud item status is FINISHED
    if (this.selectedTabController === JobListTabs.START_OF_DAY_CHECKLIST) {
      return this.startOfDayCheckList;
    }
    // if selectedTab is COMPLETED_WORK we should display COMPLETED, REVIEWED, and jobs where the final pud item status is FINISHED
    if (this.selectedTabController === JobListTabs.BOOKED_BY_ME) {
      return this.allJobsList.filter((j) => j.bookedBy === this.activeUser);
    }
    // if selectedTab is FILTERED_SELECTION we should display jobs that suit the filtered selection. this selection comes from the job statistics slider bar
    if (this.selectedTabController === JobListTabs.FILTERED_SELECTION) {
      return this.selectedStatisticJobs;
    }

    return this.jobsBySelectedStatusType;
  }

  public triggerRerender() {
    this.rerenderTrigger = false;
    this.$nextTick(() => {
      this.rerenderTrigger = true;
    });
  }

  get operationsHasDialogOpen() {
    return (
      this.operationsStore.viewingJobDetailsDialog ||
      this.operationsStore.viewingJobNotesDialog ||
      this.operationsStore.viewingOutsideHireDetailsDialog
    );
  }

  get jobTableData(): OperationJobSummary[] {
    if (this.windowMode && this.timezone === '') {
      return [];
    }
    if (this.selectedTabController !== JobListTabs.POINT_MANAGER) {
      return this.filterStore.filteredJobs(this.filteredJobList);
    }

    // If we are looking at point manager we convert the UnassignedPudItem into a OperationJobSummary
    const tableData: OperationJobSummary[] = [];
    // Prevent unassignedPudListForToday getter from being called before the timezone broadcast message has arrived
    if (!this.isValidUserTimeZone) {
      return [];
    }
    this.dataImportStore.unassignedPudListForToday.forEach((item) => {
      if (!item.additionalJobData) {
        return;
      }

      const unassignedPudDetails: PUDItemShort = {
        pudId: '',
        suburb: item.pudDetails.address.suburb,
        createdByDriver: false,
        status: item.pudDetails.status,
        geoLocation: item.pudDetails.address.geoLocation,
        timeDefinition: item.pudDetails.timeDefinition,
      };
      const tableItem: OperationJobSummary = Object.assign(
        new OperationJobSummary(),
        {
          _id: item.id,
          recurringJobId: null,
          driverId: '',
          fleetAssetId: '',
          date: item.pudDetails.epochTime,
          startOfDayCheckRequired: false,
          jobId: item.jobId ? item.jobId : 0,
          clientId: item.clientId,
          clientName: item.additionalJobData.clientName
            ? item.additionalJobData.clientName
            : '-',
          serviceTypeId: -1,
          isUnassignedPudType: true,
          reference: item.clientSuppliedId ? item.clientSuppliedId : '-',
          statusList: [],
          driverIsTripRate: false,
          clientRateTypeId: -1,
          additionalEquipment: [],
          pudItems: [unassignedPudDetails],
          driverName: item.driverName,
        },
      );
      tableData.push(tableItem);
    });

    return tableData;
  }

  get serviceTypeJobList(): JobsList[] {
    if (!this.jobTableData) {
      return [];
    }
    const filteredServiceTypes: JobsList[] = this.jobFilterHandler(
      this.jobTableData,
      this.selectedTabController === JobListTabs.COMPLETED_WORK,
      this.selectedTabController === JobListTabs.POINT_MANAGER,
      this.selectedTabController === JobListTabs.START_OF_DAY_CHECKLIST,
      this.selectedTabController === JobListTabs.FILTERED_SELECTION,
      this.selectedTabController === JobListTabs.BOOKED_BY_ME,
      this.activeSettingsGroupType,
    );
    return filteredServiceTypes;
  }

  public jobFilterHandler(
    jobTableData: OperationJobSummary[],
    isCompletedWorkTab: boolean,
    isPointManagerTab: boolean,
    isStartOfDayChecklistTab: boolean,
    isFilteredSelection: boolean,
    isBookedByMeTab: boolean,
    activeGroupingType: string,
  ): JobsList[] {
    let groupingType = activeGroupingType;
    const jobListGroups: JobsList[] = [];
    // If viewing the COMPLETED WORK tab, create three groups only
    if (isCompletedWorkTab) {
      jobListGroups.push({
        name: 'NO REMAINING STOPS',
        jobs: jobTableData.filter(
          (j) =>
            j.numOfCompletedLegs === j.totalNumberOfLegs &&
            j.workStatus < WorkStatus.DRIVER_COMPLETED,
        ),
      });
      jobListGroups.push({
        name: 'DRIVER COMPLETED',
        jobs: jobTableData.filter(
          (j) => j.workStatus === WorkStatus.DRIVER_COMPLETED,
        ),
      });
      jobListGroups.push({
        name: 'REVIEWED',
        jobs: jobTableData.filter((j) => j.workStatus === WorkStatus.REVIEWED),
      });
      return jobListGroups;
    }

    if (isBookedByMeTab) {
      jobListGroups.push({
        name: 'BOOKED BY ME',
        jobs: jobTableData,
      });
      return jobListGroups;
    }
    if (isStartOfDayChecklistTab) {
      const startOfDayCheckListWorkStatuses: WorkStatus[] = [
        WorkStatus.BOOKED,
        WorkStatus.PREALLOCATED,
        WorkStatus.ALLOCATED,
        WorkStatus.ACCEPTED,
        WorkStatus.IN_PROGRESS,
      ];
      startOfDayCheckListWorkStatuses.forEach((workStatus) => {
        const name: string = returnCurrentExactJobStatus(
          workStatus,
          AccountingStatus.NOT_REQUIRED,
          AccountingStatus.NOT_REQUIRED,
        );

        const jobs = jobTableData.filter((j) => j.status === name);

        if (jobs.length > 0) {
          jobListGroups.push({
            name,
            jobs,
          });
        }
      });

      return jobListGroups;
    }
    // If viewing the POINT MANAGER tab, disregard settings and sort by client only
    if (isPointManagerTab) {
      groupingType = 'sortByClient';
    }
    // Otherwise we should look at whatever the active grouping type filter
    if (groupingType === 'sortByServiceType') {
      // Add a blank service type. This so we can display jobs that do not
      // currently have a service type associated with it. (Imports)
      const blankServiceType: ServiceTypes = {
        _id: '',
        company: '',
        division: '',
        serviceTypeId: 0,
        shortServiceTypeName: 'NONE',
        longServiceTypeName: 'No Service Type',
        availableJobInputScreens: [1, 2],
        optionSelectName: 'NONE',
        recurringRequirement: false,
        fuelSurcharge: true,
        divisionService: true,
        allocationTypes: [
          {
            fleetAssetType: 1,
            subTypes: [],
          },
        ],
        displayOrder: 0,
      };
      const serviceTypeList = [
        blankServiceType,
        ...this.companyDetailsStore.getServiceTypesList,
      ];
      // Return a group for each unique service type, where at least one exists
      // for that service type.
      for (const type of serviceTypeList) {
        const filteredJobs = jobTableData.filter(
          (job) => job.serviceTypeId === type.serviceTypeId,
        );
        if (filteredJobs.length > 0) {
          const filteredItem: JobsList = {
            name: type.longServiceTypeName,
            jobs: filteredJobs,
          };
          jobListGroups.push(filteredItem);
        }
      }
    } else if (groupingType === 'sortByClient') {
      // Return a group for each unique client trading name
      const uniqueClientIds = [...new Set(jobTableData.map((s) => s.clientId))];
      uniqueClientIds.forEach((clientId) => {
        let clientName = '';
        const jobsForClient = jobTableData.filter(
          (job) => job.clientId === clientId,
        );
        if (jobsForClient.length > 0) {
          clientName = jobsForClient[0] ? jobsForClient[0].clientName : '-';
          const filteredClientItem = {
            name: clientName,
            jobs: jobsForClient,
          };
          jobListGroups.push(filteredClientItem);
        }
      });
    } else if (groupingType === 'sortByFleetAsset') {
      const allocatedWork = jobTableData.filter((jtd) => jtd.isAllocated);
      const unallocatedWork = jobTableData.filter((jtd) => !jtd.isAllocated);
      const uniqueFleetAssetIds = [
        ...new Set(allocatedWork.map((s) => s.vehicle)),
      ];
      if (unallocatedWork.length > 0) {
        const filteredClientItem = {
          name: 'UNALLOCATED',
          jobs: unallocatedWork,
        };
        jobListGroups.push(filteredClientItem);
      }
      uniqueFleetAssetIds.forEach((csrAssignedId) => {
        const jobsForFleet = allocatedWork.filter(
          (job) => job.vehicle === csrAssignedId,
        );
        if (jobsForFleet.length > 0) {
          const filteredClientItem = {
            name: csrAssignedId,
            jobs: jobsForFleet,
          };
          jobListGroups.push(filteredClientItem);
        }
      });
    } else if (groupingType === 'sortByJobStatus') {
      // Return a group for each status that contains jobs. Note that we require filtering on COMPLETED_ACTION_REQUIRED. This is currently problematic as COMPLETED_ACTION_REQUIRED is not a main job status.
      const defaultStatusOrder: Array<WorkStatus | string> = [
        WorkStatus.BOOKED,
        WorkStatus.PREALLOCATED,
        WorkStatus.ALLOCATED,
        WorkStatus.ACCEPTED,
        WorkStatus.IN_PROGRESS,
        WorkStatus.DRIVER_COMPLETED,
        'COMPLETED - ACTION REQUIRED',
        WorkStatus.REVIEWED,
      ];
      defaultStatusOrder.forEach((status) => {
        const isCompletedActionRequired =
          status === 'COMPLETED - ACTION REQUIRED';

        const jobsForStatus: OperationJobSummary[] = jobTableData.filter(
          (job) =>
            !isCompletedActionRequired
              ? job.workStatus === status && !job.statusList.includes(45)
              : job.statusList.includes(45),
        );
        // If a category has at least 1 job then add it to the list to be displayed
        if (jobsForStatus.length > 0) {
          const filteredClientItem = {
            name: !isCompletedActionRequired
              ? jobsForStatus[0].status
              : 'COMPLETED - ACTION REQUIRED',
            jobs: jobsForStatus,
          };

          jobListGroups.push(filteredClientItem);
        }
      });
    } else if (activeGroupingType === 'unsorted') {
      const allJobItems = {
        name: 'All Jobs',
        jobs: jobTableData,
      };
      jobListGroups.push(allJobItems);
    }
    return jobListGroups;
  }

  public jobSelected(jobId: number, isExpanded: boolean) {
    if (this.windowMode) {
      // Show warning and return if we are unable to perform actions from
      // window, based on the current main window route
      if (this.disableJobListActions) {
        this.showActionsDisabledAlert = true;
        if (this.actionsDisabledAlertTimeout) {
          clearTimeout(this.actionsDisabledAlertTimeout);
        }
        this.actionsDisabledAlertTimeout = setTimeout(() => {
          this.showActionsDisabledAlert = false;
        }, 5000);
        return;
      }

      const data = new BroadcastMessage(
        BroadcastIds.JOB_LIST.TO_MAIN.SELECTED_JOB_ID,
        jobId,
      );
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.JOB_LIST,
        data,
      );
    } else {
      // if the row item is opened we get the full job details. If the user is
      // just closing the expanded panel we do not require a request for the
      // full job details as it will already exist
      if (isExpanded) {
        this.operationsStore.getFullJobDetails(jobId);
        this.operationsStore.setSelectedJobId(jobId);
      }
    }
  }

  // Find and replace the selected JobDetails in the list
  public processUpdatedJobDetails(operationJobSummary: OperationJobSummary) {
    operationJobSummary.setAdditionalInformation();
    useJobStore().updateOperationsSummaryState(operationJobSummary);
    this.openWindowActionIncrementer++;
  }

  public editJobInBookingScreen(jobId: number) {
    const data = new BroadcastMessage('editJobInBookingScreen', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public allocatePreallocatedJob(jobIds: number[]) {
    const data = new BroadcastMessage('allocatePreallocatedJob', jobIds);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public deallocateJob(jobId: number) {
    const data = new BroadcastMessage('deallocateJob', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public addNoteToJob(jobNoteInformation: {
    jobId: number;
    serviceFailure: boolean;
    cancelJob: boolean;
    dispatchNote: boolean;
    startOfDayCheckNote: boolean;
  }) {
    const data = new BroadcastMessage('addNoteToJob', jobNoteInformation);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public sendMessageToDriver(jobId: number) {
    const data = new BroadcastMessage('sendMessageToDriver', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public viewSelectedJobInReview(jobId: number) {
    const data = new BroadcastMessage('viewSelectedJobInReview', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public viewJobDetailsDialog(jobId: number) {
    const data = new BroadcastMessage('viewJobDetailsDialog', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }

  // If not window mode, emit to parent to trigger collapse of all rows in
  // job_list and allocated_work tables where data-key is not equal to
  // selectedType If window mode, just collapse within this component
  public triggerRowCollapse(selectedType: string) {
    if (!this.windowMode) {
      this.$emit('closeAllExpandedRows', selectedType);
      return;
    } else {
      this.closeExpandedRows(selectedType);
    }
  }

  // Minimise all expanded rows in other tables (other than the table for the
  // name provided) ie. If you expand a row in the 1T section in the table, it
  // should minimize rows in all other tables and expand the selected
  public closeExpandedRows(selectedType: string) {
    const foundDataTables = this.$refs.expandableDataTable as any[];
    if (!foundDataTables) {
      return;
    }
    const filteredTables = foundDataTables.filter(
      (dt) => dt.$attrs['data-key'] !== selectedType,
    );

    filteredTables.forEach((table: any) => {
      if (table.expanded) {
        Object.keys(table.expanded).forEach((key) => {
          table.expanded[key] = false;
        });
      }
    });
  }

  get isValidUserTimeZone() {
    if (!this.windowMode) {
      return true;
    } else if (this.timezone === '') {
      return false;
    } else {
      return true;
    }
  }
  get jobListWindowOpen() {
    return this.operationsStore.operationOptions.jobListWindowOpen;
  }

  get disableJobListActions(): boolean {
    if (!this.windowMode) {
      return false;
    }
    const isOperationsPage = this.mainWindowRoute === 'operations_index';
    if (isOperationsPage && this.actionsDisabledAlertTimeout !== null) {
      this.showActionsDisabledAlert = false;
      clearTimeout(this.actionsDisabledAlertTimeout);
    }
    return !isOperationsPage;
  }

  public broadcastWindowClosed() {
    const data = new BroadcastMessage('jobListWindowOpen', false);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  // Refresh popout window. Closes broadcast channel and removes eventListener
  // so 'jobListWindowOpen' message is not sent to main window (which would
  // close popout window)
  public refreshPopoutWindow() {
    useBroadcastChannelStore().closeChannel(BroadcastChannelType.JOB_LIST);
    window.removeEventListener('beforeunload', this.broadcastWindowClosed);
    // Refresh
    location.reload();
  }

  public created() {
    if (this.windowMode) {
      this.setUserCredentialsForWindowMode();
      const data = new BroadcastMessage('jobListWindowOpen', true);
      const broadcast = useBroadcastChannelStore().initBroadcastChannel(
        BroadcastChannelType.JOB_LIST,
      );
      broadcast.postMessage(data);

      broadcast.onmessage = (message: any) => {
        console.log(`JobList window - '${message.data.id}'`);
        switch (message.data.id) {
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.FULL_JOB_LIST:
            this.operationsStore.setJobListWindow(true);
            useJobStore().updateOperationsJobListState(
              message.data.value,
              false,
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.UNASSIGNED_PUD_ITEM_LIST:
            if (message.data.value && message.data.value.length) {
              // Construct UnassignedPudItem list as response model so we can
              // pass into mutation
              const asResponseObject: UnassignedPudItemResponse = {
                unassignedPudItemList: message.data.value,
              };
              this.dataImportStore.setUnassignedPudItemList(asResponseObject);
            }
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.FLEET_ASSET_LIST:
            const fleetAssetList: FleetAssetSummary[] = (
              message.data.value ?? []
            ).map(initialiseFleetAssetSummary);
            this.fleetAssetStore.initialiseFleetAssetSummaryMap(fleetAssetList);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.ALL_DRIVERS:
            this.driverDetailsStore.setDriverSummaryList(
              message.data.value,
              false,
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.STATUS_TYPE_LIST:
            useRootStore().updateStatusConfigListState(message.data.value);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.SERVICE_TYPES_LIST:
            this.companyDetailsStore.setServiceTypeList(message.data.value);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.CLIENT_LIST:
            useClientDetailsStore().setClientSummaryList(
              message.data.value ?? [],
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.UPDATED_JOB_DETAILS:
            this.processUpdatedJobDetails(
              Object.assign(new OperationJobSummary(), message.data.value),
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.DIVISION_DETAILS:
            if (!message.data.value) {
              logConsoleError(
                'JobList - Division details not found in message data.',
              );
              return;
            }
            this.companyDetailsStore.setDivisionDetails(message.data.value);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.ACTIVE_USER_NAME:
            if (message.data.value) {
              sessionManager.setActiveUser(message.data.value);
            }
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.ROUTE_CHANGED:
            this.mainWindowRoute = message.data.value;
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.INITIAL_DATE_FILTER:
            this.operationsStore.setJobListDateFilter(
              message.data.value.startDate,
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.CLOSE_WINDOW:
            window.close();
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.SELECTED_JOB_DETAILS:
            this.localJobDetails = initialiseJobDetails(message.data.value);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.OWNER_LIST:
            useFleetAssetOwnerStore().setFleetAssetOwnerSummaryList(
              message.data.value,
            );
            break;
        }
      };
      // Add eventListener that sends a message to the main window when the
      // Before mounting of component, check Local Storage for existing settings and
      // set to store. This only occurs in windowMode when we don't have access to
      // the same store data.
      initOperationsDashboardSettingsFromLocalStorage(
        LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS,
      );
      initJobListGroupingFromLocalStorage(
        LOCAL_STORAGE_POPOUT_JOB_LIST_GROUPING,
      );

      // popout is closed, such that the boolean in the OperationsModule can be
      // updated
      window.addEventListener('beforeunload', this.broadcastWindowClosed);
    } else {
      Mitt.on('perMinuteTrigger', this.triggerRerender);
    }
  }
  public beforeDestroy() {
    Mitt.off('perMinuteTrigger', this.triggerRerender);
  }
  // In the popout window we need to commit token to the store so we can use the
  // values to create the BroadcastChannel. Session storage with the popout
  // window so we are able to access and commit to store.
  public setUserCredentialsForWindowMode() {
    try {
      // Get from session storage
      const token = sessionStorage.getItem(SESSION_STORAGE_TOKEN);
      if (token === null) {
        throw new Error('No token found in session storage for window mode.');
      }
      // Decode and commit to store in same format as the response after login
      const decoded = jwt.decodeJwt(token);
      sessionManager.setPropertiesFromToken({
        access_token: token,
        ...decoded,
      });

      // Construct id and commit to store
      const broadcastId = returnOperationsBroadcastChannelId(
        sessionManager.getUserName(),
        sessionManager.getJtiSessionId(),
      );

      useBroadcastChannelStore().setOperationsBroadcastId(broadcastId);
      // Create broadcast channel
      useBroadcastChannelStore().initBroadcastChannel(
        BroadcastChannelType.JOB_LIST,
      );
    } catch (error) {
      logConsoleError(
        'Error setting user credentials for window mode: ',
        error,
      );
    }
  }
}
