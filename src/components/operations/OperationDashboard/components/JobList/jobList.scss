.job-list-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-radius: $border-radius-sm;
  border: 1px solid $translucent;

  &.window-mode {
    padding: 10px;
    height: 100vh;
    background-color: var(--background-color-200);
  }

  .task-bar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    color: var(--text-color);
  }

  .filter-btn {
    &.clear {
      border-radius: 18px;
      color: $warning;
      height: 18px !important;
      width: 18px !important;
      border: 1.5px solid $warning;
    }
  }

  .table-content {
    overflow: hidden;
    height: 100%;

    .table-scrollable {
      height: 100%;
      max-height: 100%;
      flex-direction: column;
      overflow-y: auto;
      display: flex;
    }
  }

  .min-height {
    min-height: calc(33vh - 60px);
  }

  .service-name {
    height: 26px;
    background-color: var(--background-color-200);
    // border-bottom: 0.5px solid $translucent-light;
    border-radius: 0 !important;

    p {
      margin: 0;
      line-height: 1;
      font-size: $font-size-13;
      text-transform: uppercase;
      font-weight: 600;
      color: var(--bg-light);
    }
  }

  .job__visualindicators {
    visibility: visible;
    margin-left: 2px;
    align-items: center;
    justify-content: center;

    &.hidden {
      visibility: hidden;
    }
  }
  .jobList--table-row {
    color: var(--text-color) !important;
    position: relative;
    &.even-row {
      background-color: var(--background-color-450) !important;
      &:hover {
        background-color: var(--background-color-300) !important;
      }
    }
    &:hover {
      background-color: var(--background-color-300) !important;
    }
  }
  .dispatchnote-container {
    position: absolute;
    right: 6px;
    top: 1px;
  }
}

.date-filter-menu-container {
  height: 36px;
  overflow: visible;
  display: flex;
  align-items: center;
}
.date-filter-container {
  height: 18px;
  display: flex;
  align-items: center;
  overflow: visible;
}
#status-select-container {
  width: 100px;
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.smallerWidth {
  width: 100px !important;
}

.task-bar {
  max-height: 28px !important;
  // @media (min-width: 1264px) {
  //   max-height: 28px;
  // }
  // @media (max-width: 1263px) {
  //   max-height: auto;
  // }
}

.date-increment-btn {
  &:hover {
    color: $active-state;
  }
}

.table-row__expanded {
  td {
    background-color: var(--hover-bg) !important;
  }
}

.pre-allocated {
  color: var(--yellow) !important;
}

.add-italics {
  font-style: italic;
}

.tab-select-container {
  position: absolute;
  border-radius: $border-radius-base;
  left: 0;
  top: 0;

  .tab-selector {
    background-color: var(--background-color-200);
  }

  .tab-text {
    font-size: $font-size-medium;
    font-weight: 600;
    letter-spacing: 0.5px;
    color: var(--light-text-color);
  }

  .tab-active .tab-text {
    color: var(--text-color) !important;
  }
}

.filter-icon {
  color: var(--light-text-color) !important;
}
