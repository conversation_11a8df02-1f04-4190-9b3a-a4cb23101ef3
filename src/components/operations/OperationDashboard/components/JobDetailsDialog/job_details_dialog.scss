.job-details-dialog {
  padding: 0;
  position: relative;
}

.dialog-content {
  $toolbar-height: 42px;
  $body-height: 85vh;

  height: $body-height;
  position: relative;
  overflow: hidden;

  .side-column {
    display: flex;
    overflow-x: hidden;
    flex-direction: column;

    .side-column__button {
      padding: 10px 0px;

      .button-icon__container {
        padding-right: 16px;
        .button-icon__icon {
          transition: 0s;
          font-size: $font-size-18;
          &.icon-done {
            color: $success;
            font-weight: 700;
          }
          &.icon-pending {
            font-weight: 500;
            color: var(--background-color-650);
          }
        }
      }

      &.menu-item-disabled {
        .button-label {
          color: $enabled-color;
        }
        .button-icon__container {
          .button-icon__icon {
            color: $enabled-color;
          }
        }
      }

      &.active-state {
        background-color: var(--primary);
        color: white;
        .button-label {
          color: white;
        }
        .button-icon__container {
          .button-icon__icon {
            color: white;
          }
        }
      }
    }
  }

  .dialog-content__scrollable {
    min-height: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    .dialog-toolbar {
      max-height: $toolbar-height;
      min-height: $toolbar-height;

      background-color: var(--background-color-300);
    }
    .dialog-content__scrollable-section {
      min-height: calc(#{$body-height} - #{$toolbar-height});
      max-height: calc(#{$body-height} - #{$toolbar-height});
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      position: relative;
    }
  }

  .driver-chat-panel {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-left: 3px;

    .driver-chat-content {
      background-color: var(--background-color-200) !important;
      border-top: 2px double $border-color !important;
      margin-top: 5px;
      display: flex;
      flex-direction: column;
      flex-grow: 1;

      // Header title styling
      .header-title {
        text-transform: uppercase;
        font-weight: 500;
        letter-spacing: 0.5px;
        color: var(--light-text-color);
        padding-right: 4px;
      }
      // Actions styling (for the expand/collapse icon)
      .actions {
        border-radius: 100px;
      }

      .message-content {
        padding: 0px 4px;
        height: calc(30vh - 10px);
      }
    }

    // Driver info styling (time and city rows)
    .driver-info {
      display: flex;
      justify-content: start;
      align-items: center;
      color: grey;
      border-bottom: 1px solid grey;
      padding-left: 12px;
    }
  }

  .chat-header {
    display: flex;
    align-items: center;

    .header-title {
      display: flex;
      align-items: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }

    .icon-container {
      border-radius: 60px;
      padding: 0px 4px;

      &:hover {
        border: 1px solid $border-color;
        background-color: $app-dark-primary-600;
      }
    }

    .icon {
      flex-shrink: 0; // Prevents icons from shrinking
    }

    .driver-name {
      flex-grow: 1; // Allows text to take available space
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .v-btn-confirm-custom {
    color: var(--text-color);
  }
}
