<template>
  <div class="asset_management">
    <div class="content-scrollable" v-if="jobDetails">
      <v-layout wrap>
        <v-flex md12>
          <v-layout row wrap class="expansion-job-item">
            <v-flex md12 class="expansion-job-item__card-wrapper">
              <v-layout
                align-center
                class="expansion-job-item__card app-bgcolor--400 app-borderside--b app-bordercolor--600"
              >
                <span class="expansion-job-item__card--jobid"> Truck </span>

                <span
                  class="expansion-job-item__card--clientname"
                  v-if="jobDetails.workStatus < WorkStatus.PREALLOCATED"
                >
                  Unallocated
                </span>
                <span
                  class="expansion-job-item__card--clientname"
                  v-else-if="jobDetails.workStatus === WorkStatus.PREALLOCATED"
                >
                  Pre-Allocated
                </span>
                <span class="expansion-job-item__card--clientname" v-else>
                  {{ jobDetails.csrAssignedId }} ({{ jobDetails.driverName }})
                </span>
                <v-spacer></v-spacer>
                <span
                  class="expanded-content__icon-container"
                  @click="deallocateJob"
                  v-if="
                    jobDetails.workStatus >= WorkStatus.ALLOCATED &&
                    jobDetails.workStatus <= WorkStatus.IN_PROGRESS
                  "
                >
                  <v-icon class="expanded-content__icon">
                    far fa-step-backward
                  </v-icon>
                  <span class="expanded-content__icon-tooltip right-align">
                    Deallocate
                  </span>
                </span>
                <span
                  class="expanded-content__icon-container"
                  v-if="jobDetails.workStatus === WorkStatus.PREALLOCATED"
                  @click="finaliseAllocation"
                >
                  <v-icon class="expanded-content__icon">
                    far fa-play-circle
                  </v-icon>
                  <span class="expanded-content__icon-tooltip right-align">
                    Allocate Job
                  </span>
                </span>
              </v-layout>
            </v-flex>

            <v-flex md12 class="pa-3" v-if="!isAllocated">
              <AllocateDriver
                :type="ObjectToAllocate.JOB_DETAILS"
                :onValidSelection="OnValidAllocationTarget.SEND"
                :fleetAssetId.sync="jobDetails.fleetAssetId"
                :driverId.sync="jobDetails.driverId"
                :serviceTypeId="jobDetails.serviceTypeId"
                :rateTypeId="rateToApplyToDriver"
                :isFormDisabled="
                  jobDetails.workStatus >= WorkStatus.PREALLOCATED ||
                  jobDetails.statusList.includes(57)
                "
                :fleetAssetRates="fleetAssetAccountingRates"
                :clientId="jobDetails.client.id"
                :searchDate="jobDetails.jobRunEpoch"
                :jobId="jobDetails.jobId"
              ></AllocateDriver>
            </v-flex>
            <v-flex md12 v-if="isAllocated" class="pa-3">
              <AssetInformation
                :fleetAssetId="jobDetails.fleetAssetId"
                :key="jobDetails.fleetAssetId"
                :hideViewMoreButton="true"
              />
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12>
          <v-layout row wrap class="expansion-job-item">
            <v-flex md12 class="expansion-job-item__card-wrapper">
              <v-layout
                align-center
                class="expansion-job-item__card app-bgcolor--400 app-borderside--b app-bordercolor--600"
              >
                <span class="expansion-job-item__card--jobid">
                  Additional Equipment ({{
                    jobDetails.additionalAssets.length
                  }})
                </span>

                <v-spacer></v-spacer>
                <v-menu left>
                  <template v-slot:activator="{ on }">
                    <v-icon size="16" class="pl-3 pr-1" v-on="on"
                      >fas fa-ellipsis-v
                    </v-icon>
                  </template>
                  <v-list class="v-list-custom">
                    <v-list-tile
                      :disabled="menu.isDisabled"
                      dense
                      @click="selectEquipmentMenuItem(menu.id)"
                      v-for="menu of menuOptions"
                      :key="menu.id"
                    >
                      <v-list-tile-title class="pr-2">{{
                        menu.title
                      }}</v-list-tile-title>
                    </v-list-tile>
                  </v-list>
                </v-menu>
              </v-layout>

              <v-layout
                wrap
                v-if="selectedEquipmentMenu === 'ASE'"
                class="pa-3"
              >
                <v-flex md12 v-if="jobDetails.additionalAssets.length === 0">
                  <span
                    class="expansion-job-item__card--clientname"
                    style="display: block; padding-bottom: 40px"
                  >
                    No Additional Equipment Assigned
                  </span>
                </v-flex>
                <v-flex
                  md12
                  v-for="equipment of jobDetails.additionalAssets"
                  :key="equipment.contractId"
                >
                  <v-layout
                    align-center
                    justify-space-between
                    class="task-bar app-theme__center-content--header no-highlight"
                  >
                    <span class="pb-0 pl-1" style="display: block">
                      {{ assetTypeName(equipment.assetTypeId) }} -
                      {{ csrAssignedId(equipment.assetId) }}
                    </span>
                    <div>
                      <ConfirmationDialog
                        buttonText="Deallocate"
                        :message="
                          'Please confirm that you wish to remove this equipment from job # ' +
                          jobDetails.jobId
                        "
                        title="Confirm Removal of Equipment"
                        @confirm="
                          confirmRemovalOfEquipment(equipment.contractId)
                        "
                        :isSmallButton="true"
                        :buttonDisabled="disableHireEquipmentRemoval"
                        :isOutlineButton="true"
                        :isBlockButton="false"
                        :buttonColor="'error'"
                        :confirmationButtonText="'Confirm'"
                        :isCheckboxList="false"
                        :dialogIsActive="true"
                      ></ConfirmationDialog>
                    </div>
                  </v-layout>

                  <AssetInformation
                    :fleetAssetId="equipment.assetId"
                    :hideViewMoreButton="true"
                    :isFleetAssetList="false"
                  />
                </v-flex>
              </v-layout>

              <v-layout v-if="selectedEquipmentMenu === 'ADE'">
                <EquipmentHireAllocation :jobDetails="jobDetails" />
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </div>
</template>

<script lang="ts" setup>
import AllocateDriver from '@/components/common/allocate_driver/allocate_driver.vue';
import AssetInformation from '@/components/common/asset_information/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import EquipmentHireAllocation from '@/components/operations/AllocateJob/equipment_hire_allocation/index.vue';
import {
  ObjectToAllocate,
  OnValidAllocationTarget,
} from '@/helpers/AllocationHelpers.ts/AllocationHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import {
  FleetAssetTypes,
  fleetAssetTypes,
} from '@/interface-models/FleetAsset/static/FleetAssetTypes/FleetAssetTypes';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import AdditionalAsset from '@/interface-models/Jobs/AdditionalAsset';
import { JobEventType } from '@/interface-models/Jobs/Event/JobEventType';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useAllocationStore } from '@/store/modules/AllocationStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useMittListener } from '@/utils/useMittListener';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

const props = defineProps<{ job: JobDetails }>();

const jobStore = useJobStore();
const allocationStore = useAllocationStore();
const fleetAssetStore = useFleetAssetStore();

const fleetAssetAccountingRates: Ref<JobPrimaryRate[]> = ref([]);
const jobDetails: Ref<JobDetails> = ref(initialiseJobDetails(props.job));
const fleetAssetTypesList: FleetAssetTypes[] = fleetAssetTypes;
const selectedEquipmentMenu: Ref<string> = ref('ASE');

/**
 * Returns the rate type to apply to the driver based on job details.
 */
const rateToApplyToDriver: ComputedRef<JobRateType> = computed(() => {
  if (
    jobDetails.value.accounting.fleetAssetRates?.[0]?.rate?.rateTypeId ===
    JobRateType.TRIP
  ) {
    return JobRateType.TRIP;
  } else if (
    jobDetails.value.serviceTypeObject.rateTypeId === JobRateType.TRIP
  ) {
    return (
      useCompanyDetailsStore().divisionCustomConfig?.operations
        ?.defaultRateTypeId ?? JobRateType.TIME
    );
  } else {
    return jobDetails.value.serviceTypeObject.rateTypeId;
  }
});

/**
 * Menu options for equipment selection.
 */
const menuOptions: ComputedRef<
  { id: string; title: string; isDisabled: boolean }[]
> = computed(() => [
  {
    id: 'ASE',
    title: 'Assigned Equipment',
    isDisabled: selectedEquipmentMenu.value === 'ASE',
  },
  {
    id: 'ADE',
    title: 'Add Equipment',
    isDisabled:
      selectedEquipmentMenu.value === 'ADE' ||
      disableHireEquipmentRemoval.value,
  },
]);

/**
 * Selects an equipment menu item by id.
 * @param id - The menu id to select
 */
async function selectEquipmentMenuItem(id: string): Promise<void> {
  selectedEquipmentMenu.value = id;
}

/**
 * Returns the CSR assigned ID for a given fleet asset.
 * @param fleetAssetId - The fleet asset ID
 */
function csrAssignedId(fleetAssetId: string): string {
  return (
    fleetAssetStore.getFleetAssetFromFleetAssetId(fleetAssetId)
      ?.csrAssignedId || ''
  );
}

/**
 * Whether hire equipment removal is disabled based on job status.
 */
const disableHireEquipmentRemoval: ComputedRef<boolean> = computed(() => {
  return jobDetails.value.workStatus >= WorkStatus.REVIEWED;
});

/**
 * Updates the local JobDetails copy after an update response was received.
 * @param details - The updated job details to set
 */
function setJobDetails(details: JobDetails): void {
  jobDetails.value = initialiseJobDetails(details);
  if (!jobDetails.value.additionalAssets) {
    jobDetails.value.additionalAssets = [];
  }
}

/**
 * Removes a trailer (additional equipment item) from the Job using the
 * provided contractId to match, then sends update request.
 * @param contractId - The contractId of the trailer to remove
 */
async function confirmRemovalOfEquipment(contractId: string): Promise<void> {
  const indexToRemove = jobDetails.value.additionalAssets.findIndex(
    (x: AdditionalAsset) => x.contractId === contractId,
  );

  if (indexToRemove > -1) {
    jobDetails.value.additionalAssets.splice(indexToRemove, 1);
  }
  // Send update request
  jobStore.updateJobDetails(jobDetails.value);
}

/**
 * Handles response to a job update operation (removal of equipment,
 * deallocation or preallocation).
 * @param response - The response from the server containing the updated job details or status
 */
function handleUpdateResponse(response: JobEventSummary | null): void {
  if (!response) {
    return;
  }
  const { event, jobDetails: updatedJobDetails } = response;
  if (updatedJobDetails && jobDetails.value.jobId === updatedJobDetails.jobId) {
    if (
      ['DeallocateJob', 'PreAllocateJob', 'UpdateJobDetails'].includes(event)
    ) {
      setJobDetails(updatedJobDetails);
      if (event === 'UpdateJobDetails') {
        selectEquipmentMenuItem('ASE');
      }
    }
  }
}

/**
 * Whether the job is allocated.
 */
const isAllocated: ComputedRef<boolean> = computed(() => {
  return jobDetails.value.workStatus > WorkStatus.PREALLOCATED;
});

/**
 * Returns the asset type name for a given asset type ID.
 * @param assetTypeId - The asset type ID
 */
function assetTypeName(assetTypeId: number): string {
  const assetType = fleetAssetTypesList.find(
    (x: FleetAssetTypes) => x.id === assetTypeId,
  );
  if (!assetType) {
    return 'Equipment';
  }
  return assetType.longName;
}

/**
 * Sends request to deallocate the job from the driver and asset.
 */
async function deallocateJob(): Promise<void> {
  jobStore.updateJobStatus(jobDetails.value.jobId, JobEventType.DeallocateJob);
}

/**
 * Finalises allocation for the current job.
 */
function finaliseAllocation(): void {
  if (!jobDetails.value?.jobId) {
    return;
  }
  allocationStore.allocatePreAllocatedJobIds([jobDetails.value.jobId]);
}

/**
 * Handles division-level updates to job allocation.
 * @param jobIds - The list of allocated job IDs
 */
function handleAllocatedJobIds(jobIds: number[] | null): void {
  if (!jobDetails.value?.jobId || !jobIds) {
    return;
  }
  if (jobIds.includes(jobDetails.value.jobId)) {
    jobDetails.value.workStatus = WorkStatus.ALLOCATED;
  }
}

useMittListener('allocatedPreAllocatedJobIds', handleAllocatedJobIds);
useMittListener('jobStatusUpdate', handleUpdateResponse);

onMounted(() => {
  setJobDetails(props.job);
});
</script>
<style scoped lang="scss">
.expansion-job-item {
  .expansion-job-item__card-wrapper {
    padding-top: 1px;
    &.dialog-view {
      .expansion-job-item__card {
        padding: 16px;
      }
    }
    &.pud-type {
      padding-top: 1px;

      .expansion-job-item__card {
        padding: 12px;
      }
    }
    .expansion-job-item__card {
      padding: 12px;

      font-size: $font-size-12;
      text-transform: uppercase;
      &--jobid {
        font-weight: 500;
        color: var(--warning);
      }
      &--clientname {
        font-weight: 500;
        color: var(--text-color);
        padding-left: 6px;
      }
      &--time {
        color: #9997a7;
        font-weight: 400;
        padding-left: 6px;
      }
      &--status {
        font-size: $font-size-11;
        font-weight: 700;
      }
    }
  }
}

.content-scrollable {
  max-height: 100%;
  flex-direction: column;
  overflow-y: auto;
  display: flex;
  flex-grow: 1;
}

.asset_management {
  height: 100%;
}

.expanded-content__icon-container {
  padding: 7px 10px;
  margin: 0px 4px;
  border: 2px solid var(--primary-light);
  border-radius: $border-radius-base;
  position: relative;
  width: 29px;
  height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;

  &.disabled {
    pointer-events: none;
    opacity: 0.4;
  }

  &:hover {
    cursor: pointer;
    filter: brightness(120%);
    background-color: var(--primary-dark);

    .expanded-content__icon-tooltip {
      top: 115%;
      visibility: visible;
      z-index: 10000000000000000000;
      &.left-align {
        left: 0px;
      }
      &.right-align {
        right: 0px;
      }
    }
  }
  .expanded-content__icon {
    font-size: $font-size-11;
    color: var(--text-color);
  }
  .expanded-content__icon-tooltip {
    display: block;
    position: absolute;
    visibility: hidden;
    padding: 4px 6px;
    background-color: var(--primary-dark);
    border: 1px solid var(--primary);
    color: white;
    border-radius: 3px;
    font-size: 1em;
    font-weight: 500;
    width: 90px;

    text-align: center;
    z-index: 10000000000000000000;
    border-radius: $border-radius-base;

    &.left-align {
      left: 0px;
    }
    &.right-align {
      right: 0px;
    }
  }
}
</style>
