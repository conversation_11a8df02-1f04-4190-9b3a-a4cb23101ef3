import {
  DRIVER_LIST_ORDER_CLIENT,
  DRIVER_LIST_ORDER_EQUIPMENT_HIRE,
  DRIVER_LIST_ORDER_INTERNAL,
  DRIVER_LIST_ORDER_NATIONAL_CLIENT,
  DRIVER_LIST_ORDER_OUTSIDE_HIRE,
  isStaticServiceType,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import type { ClientSearchSummary } from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import DriverOperationsListSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverOperationsListSummary';
import type { HireContract } from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import { FleetAssetAvailability } from '@/interface-models/FleetAsset/FleetAssetCurrentWorkStatus';
import type { FleetAssetDriverListSummary } from '@/interface-models/FleetAsset/FleetAssetDriverListSummary';
import type { OpsListAssociation } from '@/interface-models/FleetAsset/OpsListAssociation';
import FleetOperationsListSummary from '@/interface-models/FleetAsset/Summary/FleetOperationsListSummary';
import type FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { affiliationTypes } from '@/interface-models/Generic/Affiliation/Affiliations';
import type { DriverOnlineStatus } from '@/interface-models/Generic/DriverOnlineStatus/DriverOnlineStatus';
import type OperationsDashboardSetting from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import type { GpsMarkerDetails } from '@/interface-models/Generic/Position/GpsMarkerDetails';
import type { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import { AdditionalAsset } from '@/interface-models/Jobs/AdditionalAsset';
import type { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import moment from 'moment-timezone';

type EntitiesByType =
  | {
      type: SubcontractorEntityType.FLEET_ASSET;
      entityList: FleetOperationsListSummary[];
    }
  | {
      type: SubcontractorEntityType.DRIVER;
      entityList: DriverOperationsListSummary[];
    };

type AssociatedEntity =
  | {
      driverId: string;
      associatedEntityType: SubcontractorEntityType.FLEET_ASSET;
      associatedEntity: FleetOperationsListSummary;
    }
  | {
      fleetAssetId: string;
      associatedEntityType: SubcontractorEntityType.DRIVER;
      associatedEntity: DriverOperationsListSummary;
    };

export enum GroupType {
  service = 'Service',
  client = 'Client',
  nationalClient = 'National Client',
  affiliation = 'Affiliation',
}

export interface FleetListKeyData {
  allFleetAssets: FleetOperationsListSummary[];
  gpsData: Map<string, GpsMarkerDetails>;
  onlineDrivers: Map<string, DriverOnlineStatus>;
  inProgressJobs: OperationJobSummary[];
  allocatedWork: OperationJobSummary[];
  settings: OperationsDashboardSetting[];
  fleetActiveDrivers: Map<string, string>;
  ownerAffiliationMap: Map<string, string>;
  nationalClientNames: Map<string, string>;
  serviceTypesMap: Map<number, ServiceTypes>;
  divisionContracts: HireContract[];
  equipmentHireOwners: FleetAssetOwnerSummary[];
  selectedClientIds: string[];
  selectedVehicleClass: string[];
  categoryListOrder: number[];
  allDrivers: DriverOperationsListSummary[];
}

export interface FleetAssetList {
  serviceTypeId: number;
  name: string;
  groupType: GroupType;
  assets: FleetAssetDriverListSummary[];
}

/**
 * Filters the provided list of fleet assets or drivers based on the selected
 * client IDs. If no client IDs are selected, returns all entities. Otherwise,
 * only returns entities associated with at least one of the selected client
 * IDs. Overloaded to handle both FleetOperationsListSummary and DriverOperationsListSummary
 * types.
 *
 * @param params - The parameters for filtering.
 * @returns The filtered list of fleet asset summaries or driver summaries.
 */
export function getClientFilteredFleetAssets(params: {
  toFilter: {
    type: SubcontractorEntityType.FLEET_ASSET;
    entityList: FleetOperationsListSummary[];
  };
  selectedFilterClientIds: string[];
  operationsJobList: OperationJobSummary[];
}): FleetOperationsListSummary[];
export function getClientFilteredFleetAssets(params: {
  toFilter: {
    type: SubcontractorEntityType.DRIVER;
    entityList: DriverOperationsListSummary[];
  };
  selectedFilterClientIds: string[];
  operationsJobList: OperationJobSummary[];
}): DriverOperationsListSummary[];

/**
 * Implementation for filtering either fleet assets or drivers by selected client IDs.
 */
export function getClientFilteredFleetAssets(params: {
  toFilter: EntitiesByType;
  selectedFilterClientIds: string[];
  operationsJobList: OperationJobSummary[];
}): FleetOperationsListSummary[] | DriverOperationsListSummary[] {
  const { toFilter, selectedFilterClientIds, operationsJobList } = params;

  // If no client filter is applied, return all entities
  if (selectedFilterClientIds.length === 0) {
    return toFilter.entityList;
  }

  // Helper to build a map from entityId to set of clientIds
  const buildEntityToClientIdsMap = (
    jobs: OperationJobSummary[],
    entityType: SubcontractorEntityType,
  ): Map<string, Set<string>> => {
    const map = new Map<string, Set<string>>();
    jobs.forEach((job) => {
      const entityId =
        entityType === SubcontractorEntityType.DRIVER
          ? job.driverId
          : job.fleetAssetId;
      if (entityId && job.clientId) {
        if (!map.has(entityId)) {
          map.set(entityId, new Set());
        }
        map.get(entityId)!.add(job.clientId);
      }
    });
    return map;
  };

  const entityType = toFilter.type;
  const entityToClientIds = buildEntityToClientIdsMap(
    operationsJobList,
    entityType,
  );

  // Filter entities based on whether they are linked to any selected client
  if (entityType === SubcontractorEntityType.DRIVER) {
    return toFilter.entityList.filter((driver) => {
      const clientIds = entityToClientIds.get(driver.driverId);
      return (
        !!clientIds && selectedFilterClientIds.some((id) => clientIds.has(id))
      );
    });
  } else {
    return toFilter.entityList.filter((asset) => {
      const clientIds = entityToClientIds.get(asset.fleetAssetId);
      return (
        !!clientIds && selectedFilterClientIds.some((id) => clientIds.has(id))
      );
    });
  }
}
/**
 * For the provided list of entities, returns them sorted into groups based on
 * which clients has them in their preferred drivers/vehicles list.
 * Conditionally checks preAssignedDriverIds or preAssignedVehicleIds depending
 * on type we’re sorting.
 * @param params - The parameters for grouping.
 *   - entitiesByType - The entities to group (must be FLEET_ASSET or DRIVER type).
 *   - showNationalClientPreferred - Whether to show national client preferred groups.
 *   - clientSummaryList - The list of client summaries to check for preferred entities.
 *   - keyData - Additional key data for filtering and mapping.
 * @returns - An array of FleetAssetList, each representing a group for a client.
 */
export function getClientPreferredGroups({
  entitiesByType,
  showNationalClientPreferred,
  clientSummaryList,
  keyData,
}: {
  entitiesByType: EntitiesByType;
  showNationalClientPreferred: boolean;
  clientSummaryList: ClientSearchSummary[];
  keyData: FleetListKeyData;
}): FleetAssetList[] {
  return clientSummaryList
    .map((client) => {
      // ========================================================================
      // =============================  DRIVER  =================================
      // ========================================================================
      if (entitiesByType.type === SubcontractorEntityType.DRIVER) {
        if (client.nationalClientId && showNationalClientPreferred) {
          return null;
        }
        const preferredDrivers = (client.preAssignedDriverIds ?? [])
          .map((id) =>
            entitiesByType.entityList.find(
              (d: DriverOperationsListSummary) => d.driverId === id,
            ),
          )
          .filter(Boolean) as DriverOperationsListSummary[];
        return preferredDrivers.length
          ? returnListFormattedFleetAssets({
              entitiesByType: {
                type: SubcontractorEntityType.DRIVER,
                entityList: preferredDrivers,
              },
              serviceTypeId: DRIVER_LIST_ORDER_CLIENT,
              groupTitle: client.clientDisplayName,
              groupType: GroupType.client,
              isOutsideHire: false,
              isVisible: true,
              keyData,
            })
          : null;
      } else {
        // ========================================================================
        // ===========================  FLEET ASSET  ==============================
        // ========================================================================
        if (client.nationalClientId && showNationalClientPreferred) {
          return null;
        }
        const preferredAssets = (client.preAssignedVehicleDetails ?? [])
          .map((id) =>
            entitiesByType.entityList.find(
              (a: FleetOperationsListSummary) => a.fleetAssetId === id,
            ),
          )
          .filter(Boolean) as FleetOperationsListSummary[];
        const filteredPreferredAssets = preferredAssets.filter(
          (asset) =>
            keyData.selectedVehicleClass?.length === 0 ||
            (asset.truckClass &&
              keyData.selectedVehicleClass.includes(asset.truckClass)),
        );
        return preferredAssets.length
          ? returnListFormattedFleetAssets({
              entitiesByType: {
                type: SubcontractorEntityType.FLEET_ASSET,
                entityList: filteredPreferredAssets,
              },
              serviceTypeId: DRIVER_LIST_ORDER_CLIENT,
              groupTitle: client.clientDisplayName,
              groupType: GroupType.client,
              isOutsideHire: false,
              isVisible: true,
              keyData,
            })
          : null;
      }
    })
    .filter(Boolean) as FleetAssetList[];
}

/**
 * For the provided list of entities, returns them sorted into groups based on
 * which national clients has clients within it, which have the specified
 * entities in their preferred drivers/vehicles list. Conditionally checks
 * client.preAssignedDriverIds or preAssignedVehicleIds depending on type we’re
 * sorting.
 * @param param - The parameters for grouping.
 *   - entitiesByType - The entities to group (must be FLEET_ASSET or DRIVER type).
 *   - clientSummaryList - The list of client summaries to check for preferred entities.
 *   - keyData - Additional key data for filtering and mapping.
 * @returns - An array of FleetAssetList, each representing a group for a national client.
 */
export function getNationalClientGroups({
  entitiesByType,
  clientSummaryList,
  keyData,
}: {
  entitiesByType: EntitiesByType;
  clientSummaryList: ClientSearchSummary[];
  keyData: FleetListKeyData;
}): FleetAssetList[] {
  // Build a map: nationalClientId -> Set of preferred entity IDs
  const preferredEntityIdsMap = clientSummaryList.reduce(
    (acc, client) => {
      const natId = client.nationalClientId;
      if (!natId) {
        return acc;
      }
      if (!acc[natId]) {
        acc[natId] = new Set<string>();
      }
      const ids =
        entitiesByType.type === SubcontractorEntityType.DRIVER
          ? client.preAssignedDriverIds
          : client.preAssignedVehicleDetails;
      ids?.forEach((id) => acc[natId].add(id));
      return acc;
    },
    {} as Record<string, Set<string>>,
  );

  return Object.entries(preferredEntityIdsMap)
    .map(([nationalClientId, assetIds]) => {
      // ========================================================================
      // =============================  DRIVER  =================================
      // ========================================================================
      if (entitiesByType.type === SubcontractorEntityType.DRIVER) {
        const preferredDrivers = entitiesByType.entityList.filter(
          (a: DriverOperationsListSummary) => assetIds.has(a.driverId),
        );
        return preferredDrivers.length
          ? returnListFormattedFleetAssets({
              entitiesByType: {
                type: SubcontractorEntityType.DRIVER,
                entityList: preferredDrivers,
              },
              serviceTypeId: parseInt(nationalClientId),
              groupTitle: getNationalClientName(
                nationalClientId,
                keyData.nationalClientNames,
              ),
              groupType: GroupType.nationalClient,
              isOutsideHire: false,
              isVisible: true,
              keyData: keyData,
            })
          : null;
      } else {
        // ========================================================================
        // ===========================  FLEET ASSET  ==============================
        // ========================================================================
        const preferredAssets = entitiesByType.entityList.filter(
          (a: FleetOperationsListSummary) => assetIds.has(a.fleetAssetId),
        );
        const filteredPreferredAssets = preferredAssets.filter(
          (asset) =>
            keyData.selectedVehicleClass?.length === 0 ||
            (asset.truckClass &&
              keyData.selectedVehicleClass.includes(asset.truckClass)),
        );
        return preferredAssets.length
          ? returnListFormattedFleetAssets({
              entitiesByType: {
                type: SubcontractorEntityType.FLEET_ASSET,
                entityList: filteredPreferredAssets,
              },
              serviceTypeId: parseInt(nationalClientId),
              groupTitle: getNationalClientName(
                nationalClientId,
                keyData.nationalClientNames,
              ),
              groupType: GroupType.nationalClient,
              isOutsideHire: false,
              isVisible: true,
              keyData: keyData,
            })
          : null;
      }
    })
    .filter(Boolean) as FleetAssetList[];
}

/**
 * Groups fleet assets by service type, applying affiliation, driver association, and filter logic.
 * Returns an array of FleetAssetList, each representing a group for a service type.
 *
 * @param params - The parameters for grouping.
 *    - entitiesByType - The entities to group (must be FLEET_ASSET type).
 *    - serviceTypeList - The list of service types to group by.
 *    - showAllDrivers - Whether to show all drivers regardless of client/national client preference.
 *    - showInternalActive - Whether to show only internal assets.
 *    - clientPreferredFleetIds - Set of fleet asset IDs preferred by the client.
 *    - keyData - Additional key data for filtering and mapping.
 * @returns An array of FleetAssetList, each representing a group for a service type.
 */
export function getServiceTypeGroups({
  entitiesByType,
  serviceTypeList,
  showAllDrivers,
  showInternalActive,
  clientPreferredFleetIds,
  keyData,
}: {
  entitiesByType: EntitiesByType;
  serviceTypeList: ServiceTypes[];
  showAllDrivers: boolean;
  showInternalActive: boolean;
  clientPreferredFleetIds: Set<string>;
  keyData: FleetListKeyData;
}): FleetAssetList[] {
  // ========================================================================
  // =============================  DRIVER  =================================
  // ========================================================================
  if (entitiesByType.type === SubcontractorEntityType.DRIVER) {
    return [];
  }

  // ========================================================================
  // ===========================  FLEET ASSET  ==============================
  // ========================================================================
  const filterStore = useFilterStore();
  return serviceTypeList
    .map((type) => {
      if (isStaticServiceType(type.serviceTypeId)) {
        return returnFleetAssetListForStaticTypes({
          serviceType: type,
          allFleetAssets: entitiesByType.entityList,
          keyData,
        });
      } else {
        // Subcontractor
        const affiliationAssets = entitiesByType.entityList.filter(
          (fleetAsset: FleetOperationsListSummary) => {
            const affiliation = keyData.ownerAffiliationMap.get(
              fleetAsset.fleetAssetOwnerId,
            );
            return (
              affiliation === '1' &&
              fleetAsset.associatedDrivers.length > 0 &&
              fleetAsset.truckClass === type.shortServiceTypeName &&
              (!showAllDrivers ||
                !clientPreferredFleetIds.has(fleetAsset.fleetAssetId)) &&
              (filterStore.selectedVehicleClass?.length === 0 ||
                filterStore.selectedVehicleClass?.includes(
                  fleetAsset.truckClass,
                ))
            );
          },
        );
        // Internal
        const affiliationInternalAssets = entitiesByType.entityList.filter(
          (fleetAsset: FleetOperationsListSummary) => {
            const affiliation = keyData.ownerAffiliationMap.get(
              fleetAsset.fleetAssetOwnerId,
            );
            return (
              affiliation === '4' &&
              fleetAsset.truckClass === type.shortServiceTypeName &&
              (!showAllDrivers ||
                !clientPreferredFleetIds.has(fleetAsset.fleetAssetId)) &&
              (filterStore.selectedVehicleClass?.length === 0 ||
                filterStore.selectedVehicleClass?.includes(
                  fleetAsset.truckClass,
                ))
            );
          },
        );
        const combinedAssets = [
          ...affiliationAssets,
          ...affiliationInternalAssets,
        ].sort((a, b) => sortByCsrAssignedId(a, b));
        const finalAssets = showInternalActive
          ? affiliationAssets
          : combinedAssets;
        return returnListFormattedFleetAssets({
          entitiesByType: {
            type: SubcontractorEntityType.FLEET_ASSET,
            entityList: finalAssets,
          },
          serviceTypeId: type.serviceTypeId,
          groupTitle: type.longServiceTypeName,
          groupType: GroupType.service,
          isOutsideHire: false,
          isVisible: true,
          keyData: keyData,
        });
      }
    })
    .filter((f) => !!f && f.assets.length > 0) as FleetAssetList[];
}

/**
 * Groups fleet assets by service type, applying affiliation, driver association, and filter logic.
 * Returns an array of FleetAssetList, each representing a group for a service type.
 *
 * @param params - The parameters for grouping.
 * @param params.entitiesByType - The entities to group (must be FLEET_ASSET type).
 * @param params.serviceTypeList - The list of service types to group by.
 * @param params.showAllDrivers - Whether to show all drivers regardless of client/national client preference.
 * @param params.showInternalActive - Whether to show only internal assets.
 * @param params.clientPreferredFleetIds - Set of fleet asset IDs preferred by the client.
 * @param params.keyData - Additional key data for filtering and mapping.
 * @returns An array of FleetAssetList, each representing a group for a service type.
 */
export function getDriverPrimaryGroup({
  entitiesByType,
  keyData,
}: {
  entitiesByType: EntitiesByType;
  keyData: FleetListKeyData;
}): FleetAssetList[] {
  // ========================================================================
  // =============================  FLEET ASSET  =================================
  // ========================================================================
  if (entitiesByType.type === SubcontractorEntityType.FLEET_ASSET) {
    return [];
  }

  // ========================================================================
  // ===========================  DRIVER  ==============================
  // ========================================================================

  return [
    returnListFormattedFleetAssets({
      entitiesByType,
      serviceTypeId: -1,
      groupTitle: 'Drivers',
      groupType: GroupType.service,
      isOutsideHire: false,
      isVisible: true,
      keyData: keyData,
    }),
  ];
}

/**
 * Used to generate a FleetAssetList object for Outside Hire, Internal Hire
 * and Equipment Hire types.
 * @param type - A mock ServiceTypes object for one of the above types, as
 * these aren't actually service types.
 * @param allFleetAssets - The list of all fleet assets for the group type
 * @returns The formatted list of fleet assets or null if the service type is
 * not found.
 */
function returnFleetAssetListForStaticTypes({
  serviceType: type,
  allFleetAssets,
  keyData,
}: {
  serviceType: ServiceTypes;
  allFleetAssets: FleetOperationsListSummary[];
  keyData: FleetListKeyData;
}): FleetAssetList | null {
  const filterStore = useFilterStore();
  // Map the static service type value to its associated affiliation type
  const getAffiliationType = (serviceTypeId: number) => {
    const idMap: { [key: number]: string } = {
      [DRIVER_LIST_ORDER_INTERNAL]: '4',
      [DRIVER_LIST_ORDER_OUTSIDE_HIRE]: '3',
      [DRIVER_LIST_ORDER_EQUIPMENT_HIRE]: '2',
    };
    const id = idMap[serviceTypeId] || '1';
    return affiliationTypes.find((a) => a.id === id);
  };

  // Get a list of all FleetAssets for the provided affiliationId
  const filterFleetAssets = (
    affiliationId: string,
  ): FleetOperationsListSummary[] => {
    return allFleetAssets.filter((f: FleetOperationsListSummary) => {
      const affiliation = keyData.ownerAffiliationMap.get(f.fleetAssetOwnerId);

      return (
        affiliation === affiliationId &&
        (filterStore.selectedVehicleClass?.length === 0 ||
          (f.truckClass &&
            filterStore.selectedVehicleClass.includes(f.truckClass)))
      );
    });
  };

  const affType = getAffiliationType(type.serviceTypeId);
  if (!affType) {
    return null;
  }
  // If the affiliation type is either Outside Hire (3) or Internal (4), and
  // it's not the case that the affiliation type is Outside Hire (3) and the
  // 'showOutsideHire' setting is not active
  if (affType.id === '3' || affType.id === '4') {
    const isActive =
      affType.id === '3'
        ? isSettingActive('showOutsideHire', keyData.settings)
        : isSettingActive('showInternalDrivers', keyData.settings);
    // Internal and Outside Hire
    const fleetAssets = filterFleetAssets(affType.id).sort((a, b) =>
      sortByCsrAssignedId(a, b),
    );
    const group = returnListFormattedFleetAssets({
      entitiesByType: {
        type: SubcontractorEntityType.FLEET_ASSET,
        entityList: fleetAssets,
      },
      serviceTypeId: type.serviceTypeId,
      groupTitle: affType.longName,
      groupType: GroupType.affiliation,
      isOutsideHire: affType.id === '3',
      isVisible: isActive,
      keyData: keyData,
    });
    return group;
  } else if (affType.id === '2') {
    const isActive = isSettingActive('showEquipmentHire', keyData.settings);
    // 2 is equipment hire
    const equipmentHireAssets: FleetAssetDriverListSummary[] = [];
    const hireOwners = keyData.equipmentHireOwners.filter(
      (owner: FleetAssetOwnerSummary) => owner.affiliation === affType.id,
    );
    const fleetAssets: FleetOperationsListSummary[] = keyData.allFleetAssets;

    hireOwners.forEach((owner) => {
      const hireAssets = fleetAssets.filter(
        (x) => x.fleetAssetOwnerId === owner.ownerId,
      );

      hireAssets.forEach((asset) => {
        const contracts = keyData.divisionContracts.filter(
          (contract: HireContract) =>
            contract.fleetAssetId === asset.fleetAssetId,
        );
        if (contracts.length > 0) {
          const summary: FleetAssetDriverListSummary = {
            type: SubcontractorEntityType.FLEET_ASSET,
            entityId: asset.fleetAssetId,
            displayName: asset.csrAssignedId,
            registrationNumber: asset.registrationNumber,
            activeItem: null,
            associatedListItems: [],
            allocationAvailability: trailerWorkLoadStatus({
              fleetAssetId: asset.fleetAssetId,
              inProgressJobs: keyData.inProgressJobs,
              allocatedWork: keyData.allocatedWork,
            }),
            isOutsideHire: false,
            isEquipmentHire: true,
            ownerName: owner.name,
            isVisible: isActive,
          };
          equipmentHireAssets.push(summary);
        }
      });
    });

    return {
      serviceTypeId: type.serviceTypeId,
      name: affType.longName,
      groupType: GroupType.affiliation,
      assets: equipmentHireAssets,
    };
  } else {
    return null;
  }
}

/**
 * Builds the ordered list of fleet asset groups based on the given category order.
 *
 * @param categoryListOrder - Array of group IDs in desired order.
 * @param clientPreferredGroups - Client preferred groups.
 * @param nationalClientGroups - National client groups.
 * @param filteredServiceTypes - Service type groups.
 * @returns Ordered array of FleetAssetList.
 */
export function buildFinalGroups({
  categoryListOrder,
  clientPreferredGroups,
  nationalClientGroups,
  filteredServiceTypes,
}: {
  categoryListOrder: number[];
  clientPreferredGroups: FleetAssetList[];
  nationalClientGroups: FleetAssetList[];
  filteredServiceTypes: FleetAssetList[];
}): FleetAssetList[] {
  const finalGroups: FleetAssetList[] = [];
  categoryListOrder.forEach((orderId) => {
    if (orderId === DRIVER_LIST_ORDER_CLIENT) {
      finalGroups.push(...clientPreferredGroups);
    } else if (orderId === DRIVER_LIST_ORDER_NATIONAL_CLIENT) {
      finalGroups.push(...nationalClientGroups);
    } else {
      const group = filteredServiceTypes.find(
        (g) => g.serviceTypeId === orderId,
      );
      if (group) {
        finalGroups.push(group);
      }
    }
  });
  return finalGroups;
}

/**
 * Appends missing client and national client groups to the final groups list
 * based on the provided category order.
 *
 * @param finalGroups - The current list of fleet asset groups to be updated.
 * @param categoryListOrder - The order of categories to determine which groups are present.
 * @param clientPreferredGroups - Groups preferred by the client to append if missing.
 * @param nationalClientGroups - National client groups to append if missing.
 * @returns The updated list of fleet asset groups with any missing groups appended.
 */
export function appendMissingGroups({
  finalGroups,
  categoryListOrder,
  clientPreferredGroups,
  nationalClientGroups,
  remainingGroups,
}: {
  finalGroups: FleetAssetList[];
  categoryListOrder: number[];
  clientPreferredGroups: FleetAssetList[];
  nationalClientGroups: FleetAssetList[];
  remainingGroups?: FleetAssetList[];
}) {
  if (!categoryListOrder.includes(DRIVER_LIST_ORDER_CLIENT)) {
    finalGroups.push(...clientPreferredGroups);
  }
  if (!categoryListOrder.includes(DRIVER_LIST_ORDER_NATIONAL_CLIENT)) {
    finalGroups.push(...nationalClientGroups);
  }
  if (remainingGroups?.length) {
    finalGroups.push(...remainingGroups);
  }
  return finalGroups;
}

/**
 * Returns the current workload status of a trailer based on whether it's
 * allocated to any in-progress or allocated work.
 * @param fleetAssetId - The ID of the fleet asset (trailer).
 * @param inProgressJobs - The list of jobs that are currently in progress.
 * @param allocatedWork - The list of jobs that are currently allocated.
 * @returns - The workload status of the trailer as a FleetAssetAvailability enum.
 */
function trailerWorkLoadStatus({
  fleetAssetId,
  inProgressJobs,
  allocatedWork,
}: {
  fleetAssetId: string;
  inProgressJobs: OperationJobSummary[];
  allocatedWork: OperationJobSummary[];
}): FleetAssetAvailability {
  // Check if currently on STARTED job
  if (
    inProgressJobs.find(
      (x: OperationJobSummary) =>
        x.additionalAssets &&
        x.additionalAssets.find(
          (asset: AdditionalAsset) => asset.assetId === fleetAssetId,
        ),
    ) !== undefined
  ) {
    return FleetAssetAvailability.NONE;
  }
  // Check if currently ALLOCATED or PREALLOCATED to job
  if (
    allocatedWork.find(
      (x: OperationJobSummary) =>
        x.additionalAssets &&
        x.additionalAssets.find(
          (asset: AdditionalAsset) => asset.assetId === fleetAssetId,
        ),
    ) !== undefined
  ) {
    return FleetAssetAvailability.PARTIAL;
  }
  // If neither of the above are true, then this truck is fully available
  return FleetAssetAvailability.FULL;
}

// Constructs a FleetAssetList object using the title and list of FleetAsset
// objects supplies.
export function returnListFormattedFleetAssets({
  entitiesByType,
  serviceTypeId,
  groupTitle,
  groupType,
  // fleetAssets,
  isOutsideHire = false,
  isVisible,
  keyData,
}: {
  entitiesByType: EntitiesByType;
  serviceTypeId: number;
  groupTitle: string;
  groupType: GroupType;
  // fleetAssets: FleetOperationsListSummary[];
  isOutsideHire: boolean;
  isVisible: boolean;
  keyData: FleetListKeyData;
}): FleetAssetList {
  const {
    gpsData,
    onlineDrivers,
    inProgressJobs,
    allocatedWork,
    settings,
    fleetActiveDrivers,
    ownerAffiliationMap,
  } = keyData;
  const listFormatAssets: FleetAssetDriverListSummary[] = [];

  // Check if each group is visible based on the settings
  const [
    showOnlineDrivers,
    showOfflineDrivers,
    showHasCurrentWork,
    showNoCurrentWork,
  ] = [
    'showOnlineDrivers',
    'showOfflineDrivers',
    'hasCurrentWork',
    'hasNoCurrentWork',
  ].map((settingId) => isSettingActive(settingId, settings));

  // ========================================================================
  // =============================  DRIVER  =================================
  // ========================================================================
  if (entitiesByType.type === SubcontractorEntityType.DRIVER) {
    entitiesByType.entityList.forEach((driver: DriverOperationsListSummary) => {
      // Check if there is a pinned vehicle manually set by the user
      const pinnedFleetAssetId = fleetActiveDrivers.get(driver.driverId);

      // Now create summary objects for all associated fleet assets
      const fleetListItems: OpsListAssociation[] = [];
      driver.associatedFleetAssets.forEach((fleetAssetSummary) => {
        // Create summary object (will not be default driver)
        // Push into allAssociatedDrivers array
        const assocFleetSummary: OpsListAssociation | undefined =
          returnAssociatedListItemForEntity({
            associatedEntity: {
              driverId: driver.driverId,
              associatedEntityType: SubcontractorEntityType.FLEET_ASSET,
              associatedEntity: fleetAssetSummary,
            },
            inProgressJobs,
            allocatedWork,
            onlineDrivers,
            gpsData,
            isDefault: false,
            isPinned:
              !!pinnedFleetAssetId &&
              fleetAssetSummary.fleetAssetId === pinnedFleetAssetId,
          });
        if (assocFleetSummary) {
          fleetListItems.push(assocFleetSummary);
        }
      });

      // Continue if there are no vehicles associated with this driver
      if (!fleetListItems.length) {
        return;
      }

      // If there is a pinned vehicle defined in the store, use that. Otherwise
      // call getActiveVehicleForDriver to determine the active driver.
      const pinnedEntity = driver.associatedFleetAssets.find(
        (f) => !!pinnedFleetAssetId && f.fleetAssetId === pinnedFleetAssetId,
      );
      const activeVehicle: OpsListAssociation | undefined | null = pinnedEntity
        ? returnAssociatedListItemForEntity({
            associatedEntity: {
              driverId: driver.driverId,
              associatedEntityType: SubcontractorEntityType.FLEET_ASSET, // Assuming this is a driver summary
              associatedEntity: pinnedEntity,
            },
            inProgressJobs,
            allocatedWork,
            onlineDrivers,
            gpsData,
            isDefault: false,
          })
        : getActiveVehicleForDriver<
            OpsListAssociation,
            FleetOperationsListSummary
          >({
            driverId: driver.driverId,
            inProgressJobs,
            allocatedWork,
            associatedFleetAssets: fleetListItems,
            fleetAssetSummaries: driver.associatedFleetAssets,
          });
      if (!activeVehicle) {
        return;
      }
      // Format display name: truncate with ellipsis if over max length
      const MAX_DISPLAY_NAME_LENGTH = 14;
      let displayName = driver.displayName;
      if (displayName.length > MAX_DISPLAY_NAME_LENGTH) {
        displayName = displayName.slice(0, MAX_DISPLAY_NAME_LENGTH) + '…';
      }

      // Create the summary object for the Fleet Asset
      const summary: FleetAssetDriverListSummary = {
        type: SubcontractorEntityType.DRIVER,
        entityId: driver.driverId,
        displayName: displayName,
        registrationNumber: '',
        activeItem: activeVehicle,
        associatedListItems: fleetListItems,
        allocationAvailability: returnAllocationAvailabilityForFleetAsset({
          fleetAssetId: activeVehicle.entityId,
          driverId: driver.driverId,
          inProgressJobs,
          allocatedWork,
        }),
        isOutsideHire,
        isEquipmentHire: false,
        isVisible,
        truckClass: '',
        affiliation: '',
      };
      const isVisibleFromSettings =
        isVisible &&
        filterAssetsForCheckboxSelection(
          summary,
          showOfflineDrivers,
          showOnlineDrivers,
          showHasCurrentWork,
          showNoCurrentWork,
        );
      if (isVisibleFromSettings) {
        summary.isVisible = true; // Explicitly set to true as it's been filtered in
        // Only push to listFormatAssets if the summary is visible according to the filters
        listFormatAssets.push(summary);
      } else {
        summary.isVisible = false; // Explicitly set to false if not visible
      }
    });
  } else {
    // ========================================================================
    // ===========================  FLEET ASSET  ==============================
    // ========================================================================
    entitiesByType.entityList.forEach(
      (fleetAsset: FleetOperationsListSummary) => {
        const defaultDriverId = fleetAsset.defaultDriver;
        // Check if there is a pinned driver manually set by the user
        const pinnedDriverId = fleetActiveDrivers.get(fleetAsset.fleetAssetId);

        // Now create summary objects for all drivers
        const allAssociatedDrivers: OpsListAssociation[] = [];
        fleetAsset.associatedDriverSummaries.forEach((summary) => {
          // Create summary object (will not be default driver)
          // Push into allAssociatedDrivers array
          const assocDriverSummary: OpsListAssociation | undefined =
            returnAssociatedListItemForEntity({
              associatedEntity: {
                fleetAssetId: fleetAsset.fleetAssetId,
                associatedEntityType: SubcontractorEntityType.DRIVER, // Assuming this is a driver summary
                associatedEntity: summary,
              },
              inProgressJobs,
              allocatedWork,
              onlineDrivers,
              gpsData,
              isDefault: summary.driverId === defaultDriverId,
              isPinned: !!pinnedDriverId && summary.driverId === pinnedDriverId,
            });
          if (assocDriverSummary) {
            allAssociatedDrivers.push(assocDriverSummary);
          }
        });

        // Continue if there are no drivers associated with this fleet asset
        if (!allAssociatedDrivers.length) {
          return;
        }

        // If there is a pinned driver defined in the store, use that. Otherwise
        // call returnActiveDriverSummary to determine the active driver.
        const pinnedEntity = fleetAsset.associatedDriverSummaries.find(
          (d) => !!pinnedDriverId && d.driverId === pinnedDriverId,
        );
        const activeDriver = pinnedEntity
          ? returnAssociatedListItemForEntity({
              associatedEntity: {
                fleetAssetId: fleetAsset.fleetAssetId,
                associatedEntityType: SubcontractorEntityType.DRIVER,
                associatedEntity: pinnedEntity,
              },
              inProgressJobs,
              allocatedWork,
              onlineDrivers,
              gpsData,
              isDefault: pinnedDriverId === defaultDriverId,
            })
          : returnActiveDriverSummary(allAssociatedDrivers, defaultDriverId);
        if (!activeDriver) {
          return;
        }

        // Create the summary object for the Fleet Asset
        const summary: FleetAssetDriverListSummary = {
          type: SubcontractorEntityType.FLEET_ASSET,
          entityId: fleetAsset.fleetAssetId,
          displayName: fleetAsset.csrAssignedId,
          registrationNumber: fleetAsset.registrationNumber,
          activeItem: activeDriver,
          associatedListItems: allAssociatedDrivers,
          allocationAvailability: returnAllocationAvailabilityForFleetAsset({
            fleetAssetId: fleetAsset.fleetAssetId,
            driverId: activeDriver.entityId,
            inProgressJobs,
            allocatedWork,
          }),
          isOutsideHire,
          isEquipmentHire: false,
          isVisible,
          truckClass: fleetAsset.truckClass,
          affiliation: ownerAffiliationMap.get(fleetAsset.fleetAssetOwnerId),
        };
        const isVisibleFromSettings =
          isVisible &&
          filterAssetsForCheckboxSelection(
            summary,
            showOfflineDrivers,
            showOnlineDrivers,
            showHasCurrentWork,
            showNoCurrentWork,
          );
        if (isVisibleFromSettings) {
          summary.isVisible = true; // Explicitly set to true as it's been filtered in
          // Only push to listFormatAssets if the summary is visible according to the filters
          listFormatAssets.push(summary);
        } else {
          summary.isVisible = false; // Explicitly set to false if not visible
        }
      },
    );
  }

  const filteredItem: FleetAssetList = {
    serviceTypeId,
    name: groupTitle,
    groupType: groupType,
    assets: listFormatAssets,
  };

  return filteredItem;
}

/**
 * Filters the FleetAssetDriverListSummary object based on the current settings.
 * Returns true if the asset should be displayed, false otherwise.
 */
function filterAssetsForCheckboxSelection(
  summary: FleetAssetDriverListSummary,
  showOfflineDrivers: boolean,
  showOnlineDrivers: boolean,
  showHasCurrentWork: boolean,
  showNoCurrentWork: boolean,
): boolean {
  if (!summary.activeItem) {
    return false;
  }

  let isVisible = false;

  // Online/Offline Filters
  const isDriverOnline = summary.activeItem.isOnline;
  if (showOnlineDrivers && showOfflineDrivers) {
    isVisible = true; // Both online and offline drivers should be visible
  } else if (showOnlineDrivers && isDriverOnline) {
    isVisible = true; // Show only online drivers
  } else if (showOfflineDrivers && !isDriverOnline) {
    isVisible = true; // Show only offline drivers
  }

  // Current Work Filters
  const hasCurrentWork =
    summary.allocationAvailability !== FleetAssetAvailability.NONE;
  if (showHasCurrentWork && showNoCurrentWork) {
    // If both are selected, show all drivers regardless of work status, but respect the online/offline visibility.
    return isVisible;
  } else if (showNoCurrentWork && hasCurrentWork) {
    // Show only drivers with current work, filtered by online/offline visibility.
    return isVisible;
  } else if (showHasCurrentWork && !hasCurrentWork) {
    // Show only drivers with no current work, filtered by online/offline visibility.
    return isVisible;
  }

  return false; // Default to not visible if no conditions match.
}

// Handle emit from settings dialog component
export function isSettingActive(
  settingId: string,
  settings: OperationsDashboardSetting[],
): boolean {
  const foundSetting = settings.find((s) => s.id === settingId);
  return foundSetting !== undefined && foundSetting.active;
}

// Check if the supplied fleet asset allocated to an In Progress or Allocated job.
// Return the associated enum value based on the result;
function returnAllocationAvailabilityForFleetAsset({
  fleetAssetId,
  driverId,
  inProgressJobs,
  allocatedWork,
}: {
  fleetAssetId: string;
  driverId: string;
  inProgressJobs: OperationJobSummary[];
  allocatedWork: OperationJobSummary[];
}): FleetAssetAvailability {
  // Check if currently on IN PROGRESS job
  if (
    inProgressJobs.some(
      (j) => j.fleetAssetId === fleetAssetId && j.driverId === driverId,
    )
  ) {
    return FleetAssetAvailability.NONE;
  } else if (
    allocatedWork.some(
      (j) => j.fleetAssetId === fleetAssetId && j.driverId === driverId,
    )
  ) {
    // Check if currently ALLOCATED or PREALLOCATED to job
    return FleetAssetAvailability.PARTIAL;
  } else {
    // If neither of the above are true, then this truck is fully available
    return FleetAssetAvailability.FULL;
  }
}

/**
 * Called to construct the `associatedListItems` that go in a
 * FLeetAssetDriverListSummary. When the fleet/driver list items are grouped by
 * driver, the associated list items are based on FleetOperationsListSummary objects, and
 * when grouped by fleet asset, the associated list items are based on
 * DriverOperationsListSummary objects.
 *
 * @param associatedEntity
 * @param inProgressJobs
 * @param allocatedWork
 * @param onlineDrivers
 * @param gpsData
 * @param isDefault
 * @param isPinned
 * @returns
 */
function returnAssociatedListItemForEntity({
  associatedEntity,
  inProgressJobs,
  allocatedWork,
  onlineDrivers,
  gpsData,
  isDefault = false,
  isPinned = false,
}: {
  associatedEntity: AssociatedEntity;
  inProgressJobs: OperationJobSummary[];
  allocatedWork: OperationJobSummary[];
  onlineDrivers: Map<string, DriverOnlineStatus>;
  gpsData: Map<string, GpsMarkerDetails>;
  isDefault?: boolean;
  isPinned?: boolean;
}): OpsListAssociation | undefined {
  if (!associatedEntity?.associatedEntity) {
    return;
  }
  // ========================================================================
  // =============================  DRIVER  =================================
  // ========================================================================
  if (
    associatedEntity.associatedEntityType === SubcontractorEntityType.DRIVER
  ) {
    const driverSummary = associatedEntity.associatedEntity;
    const driverId = driverSummary.driverId;
    // Use driverId (from associated item) to find online status
    const isOnline = onlineDrivers.get(driverId);
    // Use fleetAssetId (from main entity id) to find last location update
    const lastLocationUpdate = gpsData.get(associatedEntity.fleetAssetId);
    const allocationAvailability = returnAllocationAvailabilityForFleetAsset({
      fleetAssetId: associatedEntity.fleetAssetId,
      driverId,
      inProgressJobs,
      allocatedWork,
    });
    const summary: OpsListAssociation = {
      entityId: driverSummary.driverId,
      displayName: driverSummary.displayName,
      contactNumber: driverSummary.mobile
        ? driverSummary.mobile.replace(/\s+/g, '')
        : '',
      isDefault,
      isOnline: isOnline ? isOnline.isOnline : false,
      lastLocationUpdate: lastLocationUpdate
        ? lastLocationUpdate.timestamp
        : null,
      allocationAvailability: allocationAvailability
        ? allocationAvailability
        : 0,
      isPinned: isPinned,
    };
    return summary;
  } else {
    // ========================================================================
    // ===========================  FLEET ASSET  ==============================
    // ========================================================================
    const fleetAssetSummary = associatedEntity.associatedEntity;
    const associatedEntityId = fleetAssetSummary.fleetAssetId;
    // Use driverId (from main entity id) to find online status
    const isOnline = onlineDrivers.get(associatedEntity.driverId);
    // Use fleetAssetId (from associated item) to find last location update
    const lastLocationUpdate = gpsData.get(associatedEntityId);
    const allocationAvailability = returnAllocationAvailabilityForFleetAsset({
      fleetAssetId: associatedEntityId,
      driverId: associatedEntity.driverId,
      inProgressJobs,
      allocatedWork,
    });
    // Format display name: `csrAssignedId (truckClass) - registrationNumber`
    let displayName = fleetAssetSummary.csrAssignedId;
    if (fleetAssetSummary.truckClass?.trim()) {
      displayName += ` (${fleetAssetSummary.truckClass.trim()})`;
    }
    if (fleetAssetSummary.registrationNumber?.trim()) {
      displayName += ` - ${fleetAssetSummary.registrationNumber.trim()}`;
    }
    displayName = displayName.trim();

    // Create the summary object for the Fleet Asset
    const summary: OpsListAssociation = {
      entityId: fleetAssetSummary.fleetAssetId,
      displayName: displayName,
      contactNumber: fleetAssetSummary.registrationNumber || '',
      isDefault,
      isOnline: isOnline?.isOnline ?? false,
      lastLocationUpdate: lastLocationUpdate?.timestamp ?? null,
      allocationAvailability: allocationAvailability
        ? allocationAvailability
        : FleetAssetAvailability.FULL,
      isPinned: isPinned,
    };
    return summary;
  }
}

/**
 * Determines what the default active driver should be for the provided list
 * of associated drivers. Used to determine which driver should be pinned to
 * the top of the list, when no pinned driver has been manually set by the
 * user.
 * @param allAssociatedDrivers - The full list of all drivers associated with
 * the Fleet Asset
 * @param defaultDriverId - The default driver ID for the Fleet Asset
 * @returns - The FleetAssetDriver object for the active driver
 */
function returnActiveDriverSummary(
  allAssociatedDrivers: OpsListAssociation[],
  defaultDriverId?: string | null,
): OpsListAssociation {
  // Find which drivers have current Allocated Work
  const driversWithAllocatedWork = allAssociatedDrivers.filter(
    (driver) =>
      // driver.allocationAvailability === FleetAssetAvailability.NONE,
      driver.allocationAvailability === FleetAssetAvailability.NONE ||
      driver.allocationAvailability === FleetAssetAvailability.PARTIAL,
  );
  const allOnlineDrivers = allAssociatedDrivers.filter(
    (driver) => driver.isOnline,
  );

  // Step 1. Check if there are any drivers with allocated work
  if (driversWithAllocatedWork.length > 0) {
    // If only one driver has allocated work, pin this driver
    // If more than one driver has allocated work, then check if one of them is online
    if (driversWithAllocatedWork.length === 1) {
      return driversWithAllocatedWork[0];
    } else if (driversWithAllocatedWork.length > 1) {
      const onlineWithAllocatedWork = driversWithAllocatedWork.filter(
        (driver) => driver.isOnline,
      );
      // If more than one driver is online, then check for a default driver
      // If there is no default driver, pin the first driver in the list
      if (onlineWithAllocatedWork.length === 1) {
        return onlineWithAllocatedWork[0];
      } else if (onlineWithAllocatedWork.length > 1) {
        const defaultDriver = onlineWithAllocatedWork.find(
          (driver) => driver.entityId === defaultDriverId,
        );
        return defaultDriver || onlineWithAllocatedWork[0];
      }
    }
  }
  // Step 2. Check if there are any drivers that are online
  if (allOnlineDrivers.length > 0) {
    // If there is only one online driver, return them
    if (allOnlineDrivers.length === 1) {
      return allOnlineDrivers[0];
    } else {
      // If there are multiple online drivers, check for a default driver
      // If there is no default driver, return the first online driver
      const defaultDriver = allOnlineDrivers.find(
        (driver) => driver.entityId === defaultDriverId,
      );
      return defaultDriver || allOnlineDrivers[0];
    }
  }
  // Step 3. If there are no drivers with allocated work or no drivers
  // that are online, try to use the default driver. If there is none,
  // return the first driver in the list
  const defaultDriver = allAssociatedDrivers.find(
    (driver) => driver.entityId === defaultDriverId,
  );
  return defaultDriver || allAssociatedDrivers[0];
}

/**
 * Determines the most relevant active vehicle (fleet asset) for a given driver
 * based on their job assignments and fleet associations.
 *
 * The function prioritizes vehicles in the following order:
 * 1. Vehicle assigned to an in-progress job for the driver today.
 * 2. Vehicle assigned to an upcoming (not completed or cancelled) job for the
 *    driver today.
 * 3. Vehicle assigned to any job for the driver tomorrow.
 * 4. Vehicle assigned to any job for the driver yesterday.
 * 5. Vehicle where the driver is set as the default driver in the fleet asset
 *    summary.
 * 6. The first associated fleet asset as a fallback.
 *
 * @param params - An object containing:
 *   @param driverId - The ID of the driver to find the active vehicle for.
 *   @param inProgressJobs - List of jobs currently in progress.
 *   @param allocatedWork - List of jobs allocated to the driver.
 *   @param associatedFleetAssets - List of fleet assets associated with the
 *   driver.
 *   @param fleetAssetSummaries - Summaries of fleet assets, including default
 *   driver information.
 * @returns The most relevant `OpsListAssociation` (vehicle) for the driver, or
 * `null` if none found.
 */
export function getActiveVehicleForDriver<
  T extends { entityId: string },
  S extends { fleetAssetId: string; defaultDriver?: string | null },
>({
  driverId,
  inProgressJobs,
  allocatedWork,
  associatedFleetAssets,
  fleetAssetSummaries,
}: {
  driverId: string;
  inProgressJobs: OperationJobSummary[];
  allocatedWork: OperationJobSummary[];
  associatedFleetAssets: T[];
  fleetAssetSummaries: S[];
}): T | null {
  // Toggle this flag to enable/disable debug logging
  const DEBUG_LOG = false;

  if (associatedFleetAssets.length === 0) {
    if (DEBUG_LOG) {
      console.log(
        '[getActiveVehicleForDriver] No associated fleet assets, returning null',
      );
    }
    return null;
  }

  const getDayRange = (offsetDays: number, tz: string = moment.tz.guess()) => {
    const start = moment
      .tz(tz)
      .startOf('day')
      .add(offsetDays, 'days')
      .valueOf();
    const end = moment
      .tz(tz)
      .startOf('day')
      .add(offsetDays, 'days')
      .endOf('day')
      .valueOf();
    return { start, end };
  };
  const tz = useCompanyDetailsStore().userLocale;

  const todayRange = getDayRange(0, tz);
  const tomorrowRange = getDayRange(1, tz);
  const yesterdayRange = getDayRange(-1, tz);

  const allJobs = [...inProgressJobs, ...allocatedWork];

  const jobsByDate = (range: { start: number; end: number }) =>
    allJobs.filter(
      (job) =>
        job.driverId === driverId &&
        job.date >= range.start &&
        job.date <= range.end,
    );

  const findVehicleByJob = (job: OperationJobSummary): T | null =>
    associatedFleetAssets.find((v) => v.entityId === job.fleetAssetId) || null;

  // --- Step 1: Jobs today ---
  const todayJobs = jobsByDate(todayRange);
  if (todayJobs.length) {
    // 1a. In-progress
    const inProgress = todayJobs.find(
      (job) => job.workStatus === WorkStatus.IN_PROGRESS,
    );
    if (inProgress) {
      if (DEBUG_LOG) {
        console.log(
          '[getActiveVehicleForDriver] Selected by in-progress job today:',
          inProgress,
        );
      }
      return findVehicleByJob(inProgress);
    }

    // 1b. Upcoming
    const upcoming = todayJobs.find(
      (job) =>
        job.workStatus !== WorkStatus.COMPLETED &&
        job.workStatus !== WorkStatus.CANCELLED,
    );
    if (upcoming) {
      if (DEBUG_LOG) {
        console.log(
          '[getActiveVehicleForDriver] Selected by upcoming job today:',
          upcoming,
        );
      }
      return findVehicleByJob(upcoming);
    }
  }

  // --- Step 2: Jobs tomorrow ---
  const tomorrowJobs = jobsByDate(tomorrowRange);
  if (tomorrowJobs.length) {
    if (DEBUG_LOG) {
      console.log(
        '[getActiveVehicleForDriver] Selected by job tomorrow:',
        tomorrowJobs[0],
      );
    }
    return findVehicleByJob(tomorrowJobs[0]);
  }

  // --- Step 3: Jobs yesterday ---
  const yesterdayJobs = jobsByDate(yesterdayRange);
  if (yesterdayJobs.length) {
    if (DEBUG_LOG) {
      console.log(
        '[getActiveVehicleForDriver] Selected by job yesterday:',
        yesterdayJobs[0],
      );
    }
    return findVehicleByJob(yesterdayJobs[0]);
  }

  // --- Step 4: Default vehicles ---
  const defaultVehicles = associatedFleetAssets.filter(
    (v) =>
      fleetAssetSummaries.find((f) => f.fleetAssetId === v.entityId)
        ?.defaultDriver === driverId,
  );
  if (defaultVehicles.length) {
    if (DEBUG_LOG) {
      console.log(
        '[getActiveVehicleForDriver] Selected by default driver:',
        defaultVehicles[0],
      );
    }
    return defaultVehicles[0];
  }

  // --- Step 5: Allocated vehicles ---
  if (DEBUG_LOG) {
    console.log(
      '[getActiveVehicleForDriver] Selected fallback (first associated fleet asset):',
      associatedFleetAssets[0],
    );
  }
  return associatedFleetAssets[0];
}
// get national client name for nationalClientId
function getNationalClientName(
  nationalClientId: string | undefined,
  nationalClientNames: Map<string, string>,
): string {
  if (!nationalClientId) {
    return '';
  }
  const name = nationalClientNames.get(nationalClientId);
  return name || '';
}

/**
 * Sorts the FleetAsset based on their csrAssignedId. If csrAssignedId is
 * numeric, then we should try to sort them by ascending order
 *
 * @param a - The first FleetOperationsListSummary object to compare.
 * @param b - The second FleetOperationsListSummary object to compare.
 * @returns A number indicating the sort order.
 */
export function sortByCsrAssignedId(
  a: FleetOperationsListSummary,
  b: FleetOperationsListSummary,
): number {
  if (a.csrAssignedId && b.csrAssignedId) {
    const aIsNumeric = /^\d+$/.test(a.csrAssignedId);
    const bIsNumeric = /^\d+$/.test(b.csrAssignedId);
    if (aIsNumeric && bIsNumeric) {
      return parseInt(a.csrAssignedId, 10) - parseInt(b.csrAssignedId, 10);
    } else if (aIsNumeric) {
      return -1;
    } else if (bIsNumeric) {
      return 1;
    }
  }
  return a.csrAssignedId.localeCompare(b.csrAssignedId);
}
