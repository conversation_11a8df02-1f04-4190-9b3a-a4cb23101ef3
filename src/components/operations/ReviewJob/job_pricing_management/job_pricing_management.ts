import AddAdditionalCharge from '@/components/common/additional_charge/add_additional_charge.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import BreakDurationManagement from '@/components/operations/ReviewJob/break_duration_management/index.vue';
import CurrentAppliedCharges from '@/components/operations/ReviewJob/current_applied_charges.vue';
import DistanceManagement from '@/components/operations/ReviewJob/distance_management.vue';
import EquipmentHirePricingSummary from '@/components/operations/ReviewJob/equipment_hire_pricing_summary.vue';
import IntegrationRequirements from '@/components/operations/ReviewJob/integration_requirements/index.vue';
import PointToPointManagement from '@/components/operations/ReviewJob/point_to_point_management/index.vue';
import RateVariationTooltip from '@/components/operations/ReviewJob/rate_variation_tooltip.vue';
import RouteTimeManagement from '@/components/operations/ReviewJob/route_time_management.vue';
import TripRateManagement from '@/components/operations/ReviewJob/trip_rate_management/index.vue';
import UnitManagement from '@/components/operations/ReviewJob/unit_management.vue';
import ZoneManagement from '@/components/operations/ReviewJob/zone_management.vue';
import ZoneToZoneManagement from '@/components/operations/ReviewJob/zone_to_zone_management.vue';
import { RoundCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  initialiseBreakDuration,
  initialiseJobAccountingDetails,
} from '@/helpers/classInitialisers/InitialiseJobDetails';
import {
  isToday,
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  returnCorrectDuration,
  returnFormattedVarianceDuration,
  returnFormattedVariancePercent,
} from '@/helpers/DateTimeHelpers/DurationHelpers';
import { allAdditionalAssetsValidForPricing } from '@/helpers/HireContractHelpers';
import { outsideMetroChargeApplies } from '@/helpers/JobBooking/JobBookingPudHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  applyAdditionalChargeUpdateToAccounting,
  tollAdminAndServicesFeeHandler,
} from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import { adjustStandbyDemurrageForBreaks } from '@/helpers/RateHelpers/DemurrageHelpers';
import { returnFuelSurchargeForAccounting } from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import {
  JobKeyRateData,
  JobRateDataType,
  retrieveJobRateDataForPricing,
} from '@/helpers/RateHelpers/PricingRateRequestHelpers';
import {
  calculateBreakDuration,
  calculateJobPrimaryRates,
  findCorrectPointToPointRate,
  setFuelSurchargeRate,
} from '@/helpers/RateHelpers/RateHelpers';
import { ALLOWED_TRAVEL_VARIANCE_PERCENT } from '@/helpers/RouteHelpers/JobRouteHelpers';
import { returnServiceTypeLongNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { UpdateAdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeUpdateOperation';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { AdditionalChargeList } from '@/interface-models/Generic/Accounting/AdditionalChargeList';
import FinishedJobData from '@/interface-models/Generic/Accounting/FinishedJobDetails/FinishedJobData';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import { JobAccountingTotals } from '@/interface-models/Generic/Accounting/JobAccountingTotals/JobAccountingTotals';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { TimeTypeJobRateData } from '@/interface-models/Generic/Accounting/JobRateData/TimeTypeJobRateData';
import StandbyDuration from '@/interface-models/Generic/Accounting/Standby/StandbyDuration';
import Communication from '@/interface-models/Generic/Communication/Communication';
import LoadedReview from '@/interface-models/Generic/LoadedApplication/LoadedReview';
import {
  JobRateType,
  serviceTypeRates,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { StartAndReturnLegsEnum } from '@/interface-models/Generic/StartAndReturnLegs/StartAndReturnLegs';
import AdditionalAsset from '@/interface-models/Jobs/AdditionalAsset';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { DemurrageRateData } from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

enum SummaryRowType {
  PUD = 'PUD',
  PRE_FIRST_LEG = 'PRE_FIRST_LEG',
  POST_LAST_LEG = 'POST_LAST_LEG',
}

interface PudSummary {
  id: string; // pudId or id
  rowType: SummaryRowType;
  isOverallRow: boolean;
  title: string;
  subtitle: string;
  arrival: string;
  departure: string;
  isFirst: boolean;
  isLast: boolean;
  estimatedTravel?: string;
  actualTravel?: string;
  travelDifferenceTime?: string;
  travelDifferencePercent?: string;
  travelIsError?: boolean;
  estimatedLoad?: string;
  actualLoad?: string;
  loadDifferenceTime?: string;
  loadDifferencePercent?: string;
  loadIsError?: boolean;
}

interface RateTypeSelectOptions {
  id: number;
  longName: string;
}

interface JobServiceConfig {
  clientId: string;
  jobServiceTypeId: number;
  fleetAssetServiceTypeId: number;
  jobRateTypeId: number;
  fleetAssetRateTypeId: number;
}
@Component({
  components: {
    RouteTimeManagement,
    PointToPointManagement,
    UnitManagement,
    TripRateManagement,
    AddAdditionalCharge,
    CurrentAppliedCharges,
    ZoneManagement,
    EquipmentHirePricingSummary,
    ConfirmationDialog,
    DatePickerBasic,
    BreakDurationManagement,
    IntegrationRequirements,
    DistanceManagement,
    ZoneToZoneManagement,
    RateVariationTooltip,
  },
})
export default class JobPricingManagement extends Vue {
  @Prop() public jobDetails: JobDetails;
  @Prop() public clientDetails: ClientDetails | null;
  @Prop() public jobAccountingDetails: JobAccountingDetails;
  @Prop() public truckDetails: FleetAsset;
  @Prop() public fleetAssetOwner: FleetAssetOwnerSummary;
  @Prop() public accountingTotalsComputed: boolean;
  @Prop() public driverRegisteredForGst: boolean;
  @Prop({ default: true }) public clientMinimumDurationMet: boolean;
  @Prop({ default: true }) public driverMinimumDurationMet: boolean;
  @Prop({ default: false }) public readOnlyView: boolean;
  @Prop({ default: true }) public showDriverRates: boolean;
  @Prop({ default: false }) public showEDIConfirmation: boolean;
  @Prop({ default: () => [] }) public hireContracts: HireContract[];

  public fleetAssetStore = useFleetAssetStore();
  public companyDetailsStore = useCompanyDetailsStore();

  public returnFormattedDate = returnFormattedDate;
  public WorkStatus = WorkStatus;
  public jobHasBeenPreviouslyReviewed: boolean = false;
  public isUsingPreviouslyReviewedData: boolean = false;
  public rateSearchEpoch: number = 0;
  public viewingDateSelectDialog: boolean = false;
  public rateSearchEpochTemp: number = 0;

  public clientServiceRate: ClientServiceRate = new ClientServiceRate();
  public clientFuelSurcharges: ClientFuelSurchargeRate[] = [];
  public fleetAssetServiceRate: FleetAssetServiceRate =
    new FleetAssetServiceRate();
  public fleetAssetFuelSurcharges: FleetAssetFuelSurchargeRate[] = [];
  public clientServiceRateVariations: ClientServiceRateVariations[] = [];
  public loadedData: LoadedReview = new LoadedReview();

  public isRequestingJobRateData: boolean = false;
  public isOutsideHire: boolean = false;

  public initialJobAccountingDetails: JobAccountingDetails | null = null;
  public initialRateDataActivated: boolean = false;

  public pudItems: PUDItem[] = [];
  public jobReferences: JobReferenceDetails[] = [];

  public addChargeDialogIsOpen: boolean = false;
  public viewingAdditionalCharge: number = -1;
  public newJobNotes: Communication[] = [];

  public rateRetrievalErrors: string[] = [];

  // public summaryCardList: PudEventTimeSummary[] = [];
  // public legDurationsCardListClient: SummaryItemCard[] = [];
  // public legDurationsCardListDriver: SummaryItemCard[] = [];
  // public jobDelayCardList: PudDelaySummary[] = [];

  public summaryTableData: PudSummary[] = [];

  public viewingExpandedEventInfo: boolean = false;

  public recalculateTotalsTimeout: ReturnType<typeof setTimeout>;
  public forceOutsideMetroChargeClient: boolean = false;
  public forceOutsideMetroChargeFleetAsset: boolean = false;

  public viewingBreakDurationDialog: boolean = false;

  // Used to indicate whether the order of the pudItems on the job is consistent
  // with the order of the eventList items
  public jobLegsInCorrectOrder: boolean = true;

  public originalJobValues: JobServiceConfig = {
    clientId: '',
    jobServiceTypeId: 1,
    fleetAssetServiceTypeId: 1,
    jobRateTypeId: 1,
    fleetAssetRateTypeId: 1,
  };

  get isViewingDateSelectDialog() {
    return this.viewingDateSelectDialog;
  }
  set isViewingDateSelectDialog(value: boolean) {
    if (this.rateSearchEpochTemp !== this.rateSearchEpoch) {
      this.rateSearchEpochTemp = this.rateSearchEpoch;
    }
    this.viewingDateSelectDialog = value;
  }

  get allTotalsComputed() {
    if (this.readOnlyView) {
      return true;
    }
    return this.accountingTotalsComputed;
  }
  set allTotalsComputed(value: boolean) {
    this.$emit('accountingTotalsComputedUpdated', value);
  }

  // Check for certain conditions. If more than one error exists in the list
  // then we can display these to the user. If there are no errors then return
  // undefined.
  get hasErrors(): string[] | undefined {
    if (this.readOnlyView) {
      return;
    }
    const errors: string[] = [];
    if (!this.jobDetails.pudItems || this.jobDetails.pudItems.length === 1) {
      errors.push(
        'You cannot price a job with only one leg. Please add any missing legs then try again.',
      );
    }
    // Show error if any arrived or finished pud events are missing
    if (!this.allPudItemsFinished()) {
      errors.push(
        'One or more legs of this job have not been marked as FINISHED. Please go to Status Management fill in any missing Arrival or Departure times, then try again.',
      );
    }
    // Show error if any arrived or finished pud events are missing
    if (!this.allPudZoneDetailsSet()) {
      errors.push(
        'One or more stops does not have a valid ZONE set. Please Edit this job and ensure you have selected a Zone for every stop.',
      );
    }

    // Show error if we don't have a planned route
    const plannedRoute = this.jobDetails.plannedRoute;
    const routeErrorMsg =
      'This job has invalid route/distance information. This is usually the result of an incorrectly entered suburb. Please edit the job and check that all addresses have been correctly entered. After updating the job with valid addresses, please try again.';
    try {
      if (
        !plannedRoute ||
        !plannedRoute.routes ||
        !plannedRoute.routes[0] ||
        !plannedRoute.routes[0].summary ||
        !plannedRoute.routes[0].segments ||
        !plannedRoute.routes[0].segments.length ||
        plannedRoute.routes[0].segments.length !==
          this.jobDetails.pudItems.length - 1
      ) {
        errors.push(routeErrorMsg);
      }
    } catch (e) {
      console.error(e);
      errors.push(routeErrorMsg);
    }

    // =========================================================================
    // Service rate retriever errors
    // =========================================================================
    if (this.rateRetrievalErrors.length > 0) {
      errors.push(...this.rateRetrievalErrors);
    }

    // =========================================================================
    // FLEET ASSET SERVICE RATES/FUEL NO VALID RATES FOUND
    // We handle this differently because there are no default rates
    // =========================================================================
    if (
      this.loadedData.fleetAssetServiceRate &&
      this.loadedData.fleetAssetFuelSurcharge &&
      (!this.fleetAssetServiceRate._id || !this.fleetAssetFuelSurcharges.length)
    ) {
      const fleetRatesErrorMessage = (rateType: string) =>
        `The FLEET ASSET has no valid ${rateType} for the booking date of this job. Please contact Head Office to ensure a ${rateType} exists for this Fleet Asset, and is current as of the booking date of this job.`;
      if (!this.fleetAssetServiceRate._id) {
        errors.push(fleetRatesErrorMessage('RATE CARD'));
      } else if (!this.fleetAssetFuelSurcharges.length) {
        errors.push(fleetRatesErrorMessage('FUEL SURCHARGE'));
      }
    }

    // =============================================================================
    // Service Rates found, but can't find rateTypeId/serviceTypeId combination
    // =============================================================================
    const serviceTypeName = returnServiceTypeLongNameFromId(
      this.jobDetails.serviceTypeId,
      'the selected service',
    );
    const clientRateTypeName = this.jobDetails.rateTypeName;
    const fleetAssetRateTypeName = this.jobDetails.fleetAssetRateTypeName;
    const serviceRatesErrorMessage = (entityType: string, rateType: string) =>
      `Valid ${entityType} rate could not be found for ${serviceTypeName} (${rateType}). Please contact Head Office to ensure ${rateType} rates for ${serviceTypeName} have been added, and are current as of the booking date of this job.`;
    // Get rateTypeId from clientRates
    const clientRateTypeId =
      this.jobDetails.accounting?.clientRates?.[0]?.rate?.rateTypeId ?? 0;
    // If we can find the rateTypeId for client and it is NOT 6 (for TRIP/Quoted RATE)
    if (clientRateTypeId && clientRateTypeId !== 6) {
      try {
        // Error message for missing client service rate
        const clientRateToApply: RateTableItems | null =
          this.clientServiceRate.rateToApplyToJob(
            this.jobDetails.serviceTypeId,
            clientRateTypeId,
          );
        if (this.clientServiceRate._id && !clientRateToApply) {
          errors.push(serviceRatesErrorMessage('CLIENT', clientRateTypeName));
        }
      } catch (e) {
        console.error(e);
        errors.push(serviceRatesErrorMessage('CLIENT', clientRateTypeName));
      }
    }
    // Get rateTypeId from fleetAssetRates
    const fleetAssetRateTypeId = this.originalJobValues.fleetAssetRateTypeId;

    // If we can find the rateTypeId for fleetAsset and it is NOT 6 (for TRIP/Quoted RATE)
    if (fleetAssetRateTypeId && fleetAssetRateTypeId !== 6) {
      try {
        // Error message for missing fleetAsset service rate
        const fleetAssetRateToApply: RateTableItems | null =
          this.fleetAssetServiceRate.rateToApplyToJob({
            serviceTypeId: this.originalJobValues.fleetAssetServiceTypeId,
            rateTypeId: fleetAssetRateTypeId,
            operationsCustomConfig:
              this.companyDetailsStore.divisionCustomConfig?.operations,
          });
        if (this.fleetAssetServiceRate._id && !fleetAssetRateToApply) {
          errors.push(
            serviceRatesErrorMessage('FLEET ASSET', fleetAssetRateTypeName),
          );
        }
      } catch (e) {
        console.error(e);
        errors.push(
          serviceRatesErrorMessage('FLEET ASSET', fleetAssetRateTypeName),
        );
      }
    }
    if (errors.length > 0) {
      return errors;
    }
  }
  // Returns true if all pud items have status FINISHED, and ARRIVED and FINISHED
  // events exist and are non-zero
  public allPudItemsFinished(): boolean {
    return this.jobDetails.pudItems.every((p) => {
      const arr = this.jobDetails.returnSpecifiedEvent('ARRIVED', p.pudId);
      const fin = this.jobDetails.returnSpecifiedEvent('FINISHED', p.pudId);
      return (
        p.status === 'FINISHED' &&
        !!arr?.correctEventTime &&
        !!fin?.correctEventTime
      );
    });
  }
  // Checks that all pud items have zoneReference set and not -1 when the job is
  // a zone type job. Used to display an error message
  public allPudZoneDetailsSet(): boolean {
    const clientRateTypeId =
      this.jobDetails.accounting?.clientRates?.[0]?.rate.rateTypeId ??
      this.jobDetails.serviceTypeObject.rateTypeId;
    const fleetAssetRateTypeId =
      this.jobDetails.accounting?.fleetAssetRates?.[0]?.rate.rateTypeId ??
      this.jobDetails.serviceTypeObject.rateTypeId;

    // Check if either client or fleet asset are on UNIT or ZONE rate
    const zoneRefRequired =
      clientRateTypeId === 2 ||
      clientRateTypeId === 5 ||
      fleetAssetRateTypeId === 2 ||
      fleetAssetRateTypeId === 5;

    // If required, then check if all puds have zone reference set
    if (zoneRefRequired) {
      return this.jobDetails.pudItems.every(
        (p) =>
          p.rateDetails.zoneReference !== null &&
          p.rateDetails.zoneReference !== -1,
      );
    }
    // If not a zone rate then return true so we don't show error message
    return true;
  }

  // Called from DatePicker component to set the epoch holder variable (from the dialog)
  // rateSearchEpochTemp will eventually get set to rateSearchEpoch if confirmed in the dialog
  public setRateSearchEpoch(epoch: number | null): void {
    if (epoch !== null) {
      this.rateSearchEpochTemp = returnEndOfDayFromEpoch(epoch);
    }
  }

  public applyRateSearchAndRefresh(): void {
    this.rateSearchEpoch = this.rateSearchEpochTemp;
    this.isViewingDateSelectDialog = false;
    this.refreshRatesToDefault();
  }

  // Response from ServiceRateRetriever component, containing rate objects
  public allRateResponsesReceived(rateData: JobKeyRateData | null) {
    if (this.readOnlyView) {
      this.loadedData.clientServiceRate = true;
      this.loadedData.clientFuelSurcharge = true;
      this.loadedData.fleetAssetServiceRate = true;
      this.loadedData.fleetAssetFuelSurcharge = true;
      this.loadedData.breakDurations = true;
    }
    if (rateData === null) {
      this.isRequestingJobRateData = false;
      return;
    }
    // Check if any of the rates have error messages. If so, return early
    const errorMessages: string[] = [
      ...(rateData.errorMessages ?? []),
      ...(rateData.clientServiceRate.errorMessages ?? []),
      ...(rateData.clientFuelSurcharges.errorMessages ?? []),
      ...(rateData.fleetAssetServiceRate.errorMessages ?? []),
      ...(rateData.fleetAssetFuelSurcharges.errorMessages ?? []),
    ];
    if (errorMessages.length > 0) {
      this.rateRetrievalErrors = errorMessages;
      this.isRequestingJobRateData = false;
      return;
    }

    // TODO: Replace with VIC Outside Metro implementation (tracked by OPS-1190).
    // See http://jira.godesta.lan/browse/OPS-1190
    // For VIC we will set the default value of the boolean to false
    // always, for Client and Fleet Asset Outside Metro. This is a temporary
    // solution to be removed ASAP.
    const outsideMetroOverrideVic: boolean = this.jobDetails.division === 'VIC';

    // TODO: Replace with Client Outside Metro implementation (tracked by OPS-1259).
    // See http://jira.godesta.lan/browse/OPS-1259
    // For Bunnings we will set the default value of the boolean to false
    // always, for Client and Fleet Asset Outside Metro. This is a temporary
    // solution to be removed ASAP.
    const clientIdsToOverride = ['8019', '70004', '7681', '70005', '7859'];
    const outsideMetroBunningsOverride: boolean =
      this.jobDetails.division === 'QLD' &&
      clientIdsToOverride.includes(this.jobDetails.client.id);

    const outsideMetroAreaHit: boolean = outsideMetroChargeApplies(
      this.companyDetailsStore.insideMetroSuburbs,
      this.jobDetails.pudItems,
    );
    // If either of the two temporary overrides are true, then force default
    // outsideMetro boolean to false
    if (outsideMetroOverrideVic || outsideMetroBunningsOverride) {
      this.forceOutsideMetroChargeClient = false;
      this.forceOutsideMetroChargeFleetAsset = false;
    } else {
      if (outsideMetroAreaHit) {
        this.forceOutsideMetroChargeClient = true;
        this.forceOutsideMetroChargeFleetAsset = true;
      }
    }
    // Valid emitted payloads and set their values to local variables
    // Client service rate
    if (
      rateData.clientServiceRate.type === JobRateDataType.CLIENT_SERVICE_RATE &&
      rateData.clientServiceRate.data !== null
    ) {
      this.clientServiceRate = rateData.clientServiceRate.data;
    }
    // Client fuel surcharge
    if (
      rateData.clientFuelSurcharges.type ===
        JobRateDataType.CLIENT_FUEL_SURCHARGE &&
      rateData.clientFuelSurcharges.data !== null
    ) {
      this.clientFuelSurcharges = rateData.clientFuelSurcharges.data;

      this.jobAccountingDetails.additionalCharges.clientFuelSurcharge =
        returnFuelSurchargeForAccounting({
          jobDetails: this.jobDetails,
          isFuelApplicable: true,
          fuelSurcharges: this.clientFuelSurcharges,
          selectedFuelBracketId:
            this.jobDetails.accounting?.additionalCharges?.clientFuelSurcharge
              ?.appliedRateBracketId,
        });
    }

    // Fleet Asset service rate
    if (
      rateData.fleetAssetServiceRate.type ===
        JobRateDataType.FLEET_ASSET_SERVICE_RATE &&
      rateData.fleetAssetServiceRate.data !== null
    ) {
      this.fleetAssetServiceRate = rateData.fleetAssetServiceRate.data;
    }

    // Fleet Asset fuel surcharge
    if (
      rateData.fleetAssetFuelSurcharges.type ===
        JobRateDataType.FLEET_ASSET_FUEL_SURCHARGE &&
      rateData.fleetAssetFuelSurcharges.data !== null
    ) {
      this.fleetAssetFuelSurcharges = rateData.fleetAssetFuelSurcharges.data;

      this.jobAccountingDetails.additionalCharges.fleetAssetFuelSurcharge =
        returnFuelSurchargeForAccounting({
          jobDetails: this.jobDetails,
          isFuelApplicable: true,
          fuelSurcharges: this.fleetAssetFuelSurcharges,
          selectedFuelBracketId:
            this.jobAccountingDetails.additionalCharges.fleetAssetFuelSurcharge
              ?.appliedRateBracketId,
        });
    }

    // Service Rate Variations
    if (
      rateData.clientServiceRateVariations?.type ===
        JobRateDataType.CLIENT_SERVICE_RATE_VARIATIONS &&
      !!rateData.clientServiceRateVariations?.data?.length
    ) {
      this.clientServiceRateVariations =
        rateData.clientServiceRateVariations.data;
    }

    this.loadedData.clientServiceRate = true;
    this.loadedData.clientFuelSurcharge = true;
    this.loadedData.fleetAssetServiceRate = true;
    this.loadedData.fleetAssetFuelSurcharge = true;

    this.isRequestingJobRateData = false;
    this.setJobAccountingDetails(this.isUsingPreviouslyReviewedData);
  }

  public updateFullJobAccountingDetails(value: JobAccountingDetails) {
    const newValue = initialiseJobAccountingDetails(value);
    this.$emit('updateFullJobAccountingDetails', newValue);
  }

  /**
   * Returns a list of the selected clients common address list.
   *  @returns {ClientCommonAddress[]} List of ClientCommonAddress
   */
  get clientCommonAddressList(): ClientCommonAddress[] {
    const clientDetailsStore = useClientDetailsStore();
    if (
      !this.clientDetails ||
      !clientDetailsStore.clientCommonAddresses ||
      clientDetailsStore.clientCommonAddresses.length === 0 ||
      clientDetailsStore.clientCommonAddresses[0].clientId !==
        this.clientDetails.clientId
    ) {
      return [];
    }
    const clientCommonAddressList: ClientCommonAddress[] =
      clientDetailsStore.clientCommonAddresses;
    return clientCommonAddressList;
  }

  // When all required ServiceRates and FuelSurcharges have arrived,
  // begin calculating accounting details
  public async setJobAccountingDetails(useExistingRates: boolean = false) {
    if (
      useExistingRates &&
      this.jobDetails.accounting.clientRates[0] &&
      this.jobDetails.accounting.fleetAssetRates[0]
    ) {
      try {
        // Because the job was already reviewed we need to update the existing
        // rate defined inside our accounting details for both client and fleet
        // asset.
        let clientRateId =
          this.jobDetails.accounting.clientRates[0].rate.rateTypeId;
        let fleetAssetRateId = this.originalJobValues.fleetAssetRateTypeId;

        if (clientRateId !== JobRateType.TRIP) {
          const clientRateToApply: RateTableItems | null =
            this.clientServiceRate.rateToApplyToJob(
              this.jobDetails.serviceTypeId,
              this.jobDetails.accounting.clientRates[0].rate.rateTypeId,
            );

          if (clientRateToApply) {
            clientRateId = clientRateToApply.rateTypeId;
            if (
              clientRateId === JobRateType.POINT_TO_POINT &&
              this.clientDetails
            ) {
              const p2pRate = findCorrectPointToPointRate(
                clientRateToApply.rateTypeObject as PointToPointRateType[],
                this.jobDetails,
                this.clientCommonAddressList,
              );
              clientRateToApply.rateTypeObject = p2pRate;
            }
            this.jobAccountingDetails.clientRates[0].rate = clientRateToApply;
            this.jobAccountingDetails.clientRates[0].outsideMetroRate =
              this.clientServiceRate.outsideMetroRate;
            this.jobAccountingDetails.clientRates[0].rateTableName = this
              .clientServiceRate.name
              ? this.clientServiceRate.name
              : '';
            if (!this.jobAccountingDetails.clientRates[0].outsideMetroRate) {
              this.jobAccountingDetails.clientRates[0].outsideMetroRate = 0;
            }
          }
        }
        this.jobAccountingDetails.additionalCharges.clientFuelSurcharge ||=
          returnFuelSurchargeForAccounting({
            jobDetails: this.jobDetails,
            isFuelApplicable: true,
            fuelSurcharges: this.clientFuelSurcharges,
          });
        if (!this.jobAccountingDetails.additionalCharges.clientFuelSurcharge) {
          throw new Error('No Client Fuel Surcharge found');
        }
        // Set fuel surcharge rate for client
        setFuelSurchargeRate({
          type: RateEntityType.CLIENT,
          rate: this.jobAccountingDetails.clientRates[0].rate,
          fuelSurcharge:
            this.jobAccountingDetails.additionalCharges.clientFuelSurcharge!,
          pudItems: this.jobDetails.pudItems,
        });

        // Set service rate variations
        this.jobAccountingDetails.clientServiceRateVariations =
          this.jobAccountingDetails.clientRates[0].rate.getRateVariation(
            this.clientServiceRateVariations,
          );

        // If driver is not a trip/Quoted rate we need to update the service rate data in
        // accounting for the driver/fleet asset. This is so we don't use the
        // rates saved into the job when it was first reviewed.
        if (fleetAssetRateId !== 6) {
          const fleetAssetRateToApply =
            this.fleetAssetServiceRate.rateToApplyToJob({
              serviceTypeId: this.originalJobValues.fleetAssetServiceTypeId,
              rateTypeId: fleetAssetRateId,
              operationsCustomConfig:
                this.companyDetailsStore.divisionCustomConfig?.operations,
            });

          if (fleetAssetRateToApply) {
            fleetAssetRateId = fleetAssetRateToApply.rateTypeId;
            if (fleetAssetRateId === 5) {
              // Set reviewed fleet asset unit rate percentage to incoming rate
              (
                fleetAssetRateToApply.rateTypeObject as UnitRate[]
              )[0].fleetAssetPercentage = (
                this.jobDetails.accounting.fleetAssetRates[0].rate
                  .rateTypeObject as UnitRate[]
              )[0].fleetAssetPercentage;
            }

            if (fleetAssetRateId === 2) {
              // Set reviewed fleet asset unit rate percentage to incoming rate
              (
                fleetAssetRateToApply.rateTypeObject as ZoneRateType[]
              )[0].percentage = (
                this.jobDetails.accounting.fleetAssetRates[0].rate
                  .rateTypeObject as ZoneRateType[]
              )[0].percentage;
            }

            if (fleetAssetRateId === 4) {
              fleetAssetRateToApply.rateTypeObject = (
                fleetAssetRateToApply.rateTypeObject as PointToPointRateType[]
              )[0];
            }

            this.jobAccountingDetails.fleetAssetRates[0].rate =
              fleetAssetRateToApply;
            this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate =
              this.fleetAssetServiceRate.outsideMetroRate;
            this.jobAccountingDetails.fleetAssetRates[0].isGstRegistered =
              this.driverRegisteredForGst;
            this.jobAccountingDetails.fleetAssetRates[0].rateTableName = this
              .fleetAssetServiceRate.name
              ? this.fleetAssetServiceRate.name
              : '';
            if (
              !this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate
            ) {
              this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate = 0;
            }
          }
        }
        this.jobAccountingDetails.additionalCharges.fleetAssetFuelSurcharge ||=
          returnFuelSurchargeForAccounting({
            jobDetails: this.jobDetails,
            isFuelApplicable: true,
            fuelSurcharges: this.fleetAssetFuelSurcharges,
          });
        if (
          !this.jobAccountingDetails.additionalCharges.fleetAssetFuelSurcharge
        ) {
          throw new Error('No Fleet Asset Fuel Surcharge found');
        }

        // Set fuel surcharge rate for client
        setFuelSurchargeRate({
          type: RateEntityType.FLEET_ASSET,
          rate: this.jobAccountingDetails.fleetAssetRates[0].rate,
          fuelSurcharge:
            this.jobAccountingDetails.additionalCharges.fleetAssetFuelSurcharge,
          clientFuelSurchargeRate:
            this.jobAccountingDetails.additionalCharges.clientFuelSurcharge
              ?.appliedFuelSurchargeRate,
          pudItems: this.jobDetails.pudItems,
        });

        this.jobAccountingDetails.clientRates[0].standbyDuration =
          Object.assign(
            new StandbyDuration(),
            this.jobAccountingDetails.clientRates[0].standbyDuration,
          );
        this.jobAccountingDetails.fleetAssetRates[0].standbyDuration =
          Object.assign(
            new StandbyDuration(),
            this.jobAccountingDetails.fleetAssetRates[0].standbyDuration,
          );
        for (
          let i = 0;
          i < this.jobAccountingDetails.clientRates[0].rateData.length;
          i++
        ) {
          this.jobAccountingDetails.clientRates[0].rateData[i] = Object.assign(
            new TimeTypeJobRateData(),
            this.jobAccountingDetails.clientRates[0].rateData[i],
          );
        }
        for (
          let i = 0;
          i < this.jobAccountingDetails.fleetAssetRates[0].rateData.length;
          i++
        ) {
          this.jobAccountingDetails.fleetAssetRates[0].rateData[i] =
            Object.assign(
              new TimeTypeJobRateData(),
              this.jobAccountingDetails.fleetAssetRates[0].rateData[i],
            );
        }

        this.jobAccountingDetails.clientRates[0].breakDuration =
          initialiseBreakDuration(
            this.jobAccountingDetails.clientRates[0].breakDuration,
          );
        this.jobAccountingDetails.fleetAssetRates[0].breakDuration =
          initialiseBreakDuration(
            this.jobAccountingDetails.fleetAssetRates[0].breakDuration,
          );
        this.generateHeaderContent(
          this.jobAccountingDetails.clientRates[0],
          this.jobAccountingDetails.fleetAssetRates[0],
        );
        this.initialJobAccountingDetails = initialiseJobAccountingDetails(
          this.jobAccountingDetails,
        );
        // If we are still missing any rate information or rateData, we should
        // do a refresh of the rates anyway
        if (
          this.jobAccountingDetails.clientRates[0] === undefined ||
          this.jobAccountingDetails.fleetAssetRates[0] === undefined ||
          !this.jobAccountingDetails.clientRates[0].rateData.length ||
          !this.jobAccountingDetails.fleetAssetRates[0].rateData.length
        ) {
          throw new Error('Invalid rate information');
        }
      } catch (e) {
        console.error(`Something went wrong: ${e}`);
        showNotification(
          'Existing rate information is invalid. Please review details.',
          { title: 'Job Pricing' },
        );
        await this.generateTimeData();
      }
    } else {
      await this.generateTimeData();
    }

    this.initialRateDataActivated = true;
    this.allTotalsComputed = false;
    this.computeTotalsFromCurrentAccountingDetails();
  }

  /**
   * Generate the content that will be displayed at the top of the component, containing
   * a summary of times / delays throughout the job.
   * @param clientRate - Client rate data
   * @param fleetAssetRate - Fleet asset rate data
   */
  public generateHeaderContent(
    clientRate?: JobPrimaryRate,
    fleetAssetRate?: JobPrimaryRate,
  ) {
    try {
      if (!clientRate) {
        throw new Error(
          'Client rate is undefined. Cannot generate header data.',
        );
      }
      if (!fleetAssetRate) {
        throw new Error(
          'Fleet asset rate is undefined. Cannot generate header data.',
        );
      }
      const firstLastLegRows = this.generateReturnLegCardData(
        clientRate,
        fleetAssetRate,
      );
      const firstLegRow = firstLastLegRows.filter(
        (row) => row.rowType === SummaryRowType.PRE_FIRST_LEG,
      );
      const lastLegRow = firstLastLegRows.filter(
        (row) => row.rowType === SummaryRowType.POST_LAST_LEG,
      );

      this.summaryTableData = [
        ...firstLegRow,
        ...this.generateSummaryData(
          this.jobDetails.additionalJobData?.routeProgress,
        ),
        ...lastLegRow,
      ];
    } catch (error) {
      logConsoleError('Error generating header content', error);
    }
  }

  public viewAssetManagement() {
    useOperationsStore().setSelectedJobDetailsDialogTab('ASM');
  }

  // return the jobs correct rates from a service rate table
  public appliedRate(
    serviceRate: ClientServiceRate | FleetAssetServiceRate,
    rateId: number,
    serviceId: number,
  ) {
    // if rate is pallet rate we need to set the correct service
    if (rateId === 5) {
      serviceId = 4;
    }
    const foundRate = serviceRate.rateTableItems.find(
      (rate: RateTableItems) =>
        rate.serviceTypeId === serviceId && rate.rateTypeId === rateId,
    );
    if (!foundRate) {
      showNotification('No applicable rates found.', { title: 'Job Pricing' });
      return foundRate;
    }

    return foundRate;
  }

  // Called when we need JobPrimaryRate and other rate information to be
  // initialised for the first time. Called when pricing job for the first time,
  // when force refreshing rates, or if either clientRates or fleetAssetRates
  // are an empty array
  public async generateTimeData(
    overrideFleetAssetRateTypeId: number | null = null,
  ) {
    try {
      if (this.jobDetails.pudItems.length === 0) {
        throw new Error('Cannot generate time data for job with no PUD items.');
      }
      if (!this.jobAccountingDetails.additionalCharges?.clientFuelSurcharge) {
        throw new Error(
          'Client Fuel Surcharge is not set. Cannot generate time data.',
        );
      }
      if (
        !this.jobAccountingDetails.additionalCharges?.fleetAssetFuelSurcharge
      ) {
        throw new Error(
          'Fleet Asset Fuel Surcharge is not set. Cannot generate time data.',
        );
      }
      const fleetAsset = this.fleetAssetStore.getFleetAssetFromFleetAssetId(
        this.jobDetails.fleetAssetId,
      );
      const clientRatePrimaryData: JobPrimaryRate = new JobPrimaryRate();
      const fleetAssetRatePrimaryData: JobPrimaryRate = new JobPrimaryRate();

      fleetAssetRatePrimaryData.isGstRegistered = this.driverRegisteredForGst;
      const fleetAssetRates = this.jobDetails.accounting.fleetAssetRates[0];
      let fleetAssetRateId: number;
      // Use rates from JobPrimaryRate if we can find it
      if (fleetAssetRates) {
        fleetAssetRateId = fleetAssetRates.rate.rateTypeId;
      } else {
        fleetAssetRateId = this.jobDetails.serviceTypeObject.rateTypeId;
      }

      // Pass in clean JobPrimaryRate objects and populate with values from
      // service rates. We call this with empty array for workDiaryList because we
      // want a clean slate. Records will be re-requested in
      // computeTotalsFromCurrentAccountingDetails with the reset times, so this
      // will be populated again when that response comes in.
      await calculateJobPrimaryRates({
        jobDetails: this.jobDetails,
        clientRatePrimaryData: clientRatePrimaryData,
        fleetAssetRatePrimaryData: fleetAssetRatePrimaryData,
        fleetAsset: fleetAsset,
        clientServiceRateTable: this.clientServiceRate,
        fleetAssetServiceRateTable: this.fleetAssetServiceRate,
        fleetAssetRateId: fleetAssetRateId,
        overrideFleetAssetRateTypeId: overrideFleetAssetRateTypeId,
        clientFuelSurcharge:
          this.jobAccountingDetails.additionalCharges.clientFuelSurcharge,
        fleetAssetFuelSurcharge:
          this.jobAccountingDetails.additionalCharges.fleetAssetFuelSurcharge,
        workDiaryList: [],
        clientCommonAddressList: this.clientCommonAddressList,
      });

      this.jobAccountingDetails.clientRates = [];
      this.jobAccountingDetails.clientRates.push(clientRatePrimaryData);
      this.jobDetails.accounting.clientRates = [];
      this.jobDetails.accounting.clientRates.push(clientRatePrimaryData);

      this.jobAccountingDetails.clientServiceRateVariations =
        clientRatePrimaryData.rate.getRateVariation(
          this.clientServiceRateVariations,
        );

      this.jobAccountingDetails.fleetAssetRates = [];
      this.jobAccountingDetails.fleetAssetRates.push(fleetAssetRatePrimaryData);
      this.jobDetails.accounting.fleetAssetRates = [];
      this.jobDetails.accounting.fleetAssetRates.push(
        fleetAssetRatePrimaryData,
      );

      this.generateHeaderContent(
        clientRatePrimaryData,
        fleetAssetRatePrimaryData,
      );
      this.initialJobAccountingDetails = initialiseJobAccountingDetails(
        this.jobAccountingDetails,
      );
    } catch (error) {
      logConsoleError('Error generating time data', error);
    }
  }

  // Add timeout to prevent computation while user is typing
  public refreshAccountingTotals(): void {
    clearTimeout(this.recalculateTotalsTimeout);
    this.recalculateTotalsTimeout = setTimeout(() => {
      this.computeTotalsFromCurrentAccountingDetails();
    }, 500);
  }
  // Check if we should display the Copy Times functionality in
  // RouteTimeManagement component
  get enableCopyForTimeRate(): boolean {
    if (this.readOnlyView) {
      return false;
    }
    // Check that both Client and Fleet Asset are on time rates
    return (
      this.jobRateData !== undefined &&
      this.jobRateData.client.time &&
      this.jobRateData.fleetAsset.time
    );
  }
  // Captures the latest changes from emit from BreakDurationManagement component
  public breakDurationsUpdated(summaryList: WorkDiarySummary[]) {
    if (
      !this.jobAccountingDetails.clientRates[0] ||
      !this.jobAccountingDetails.fleetAssetRates[0]
    ) {
      return;
    }
    if (!this.loadedData.breakDurations) {
      this.loadedData.breakDurations = true;
    }
    // Set updated summaryList to fleet asset rates
    if (
      this.jobAccountingDetails.fleetAssetRates[0].breakDuration &&
      this.jobAccountingDetails.fleetAssetRates[0].breakDuration
        .breakSummaryList
    ) {
      this.jobAccountingDetails.fleetAssetRates[0].breakDuration.breakSummaryList =
        summaryList;
    }
    // Set updated summaryList to client rates
    if (
      this.jobAccountingDetails.clientRates[0].breakDuration &&
      this.jobAccountingDetails.clientRates[0].breakDuration.breakSummaryList
    ) {
      this.jobAccountingDetails.clientRates[0].breakDuration.breakSummaryList =
        summaryList;
    }

    calculateBreakDuration(this.jobAccountingDetails);
    adjustStandbyDemurrageForBreaks(this.jobAccountingDetails, summaryList);
    this.refreshAccountingTotals();
  }

  // For Time type job, copy the entered time from FA to client or vice versa
  public copyTimeRateDataFromOther(copyToClient: boolean): void {
    let copyFromRates: JobPrimaryRate;
    let foundRouteTimeManagement;
    // Set the rates that we are copying from, and the child component that we are copying to

    if (copyToClient) {
      // If we're copying to client, then use the fleetAssetRates and use ref for clientRouteTimeManagement component
      copyFromRates = this.jobAccountingDetails.fleetAssetRates[0];
      foundRouteTimeManagement = this.$refs.clientRouteTimeManagement as any;
    } else {
      // If copying to fleetAsset, then use the clientRates and use ref for fleetAssetRouteTimeManagement component
      copyFromRates = this.jobAccountingDetails.clientRates[0];
      foundRouteTimeManagement = this.$refs
        .fleetAssetRouteTimeManagement as any;
    }
    // If we successfully find the component using the ref, then pass in the rates into the child's method
    if (foundRouteTimeManagement) {
      foundRouteTimeManagement.pushTimesFromParent(copyFromRates);
    }
  }

  // Calculates the totals and other values previously held by
  // FinalisedJobDetails object
  public computeTotalsFromCurrentAccountingDetails() {
    this.syncDemurrageTimesFromClientToFleet(this.jobAccountingDetails);
    if (
      this.jobAccountingDetails.clientRates[0] !== undefined &&
      this.jobAccountingDetails.fleetAssetRates[0] !== undefined &&
      this.jobAccountingDetails.clientRates[0].rateData.length > 0 &&
      this.jobAccountingDetails.fleetAssetRates[0].rateData.length > 0
    ) {
      const chargeTypeList = useRootStore().additionalChargeTypeList
        ? useRootStore().additionalChargeTypeList
        : [];
      const outsideMetroAreaHit: boolean = outsideMetroChargeApplies(
        this.companyDetailsStore.insideMetroSuburbs,
        this.jobDetails.pudItems,
      );
      const clientOutsideMetroRate =
        this.jobAccountingDetails.clientRates[0] &&
        this.jobAccountingDetails.clientRates[0].outsideMetroRate &&
        outsideMetroAreaHit &&
        this.forceOutsideMetroChargeClient &&
        this.jobAccountingDetails.clientRates[0].rate.rateTypeId === 1
          ? this.jobAccountingDetails.clientRates[0].outsideMetroRate
          : 0;

      this.jobAccountingDetails.clientRates[0].outsideMetroRate =
        clientOutsideMetroRate;
      const fleetAssetOutsideMetroRate =
        this.jobAccountingDetails.fleetAssetRates[0] &&
        this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate &&
        outsideMetroAreaHit &&
        this.forceOutsideMetroChargeFleetAsset &&
        this.jobAccountingDetails.fleetAssetRates[0].rate.rateTypeId === 1
          ? this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate
          : 0;

      this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate =
        fleetAssetOutsideMetroRate;
      const jobInformation = this.jobAccountingDetails.returnItemizedTotals(
        this.jobDetails,
        this.fleetAssetOwner,
        chargeTypeList,
        this.hireContracts,
        clientOutsideMetroRate,
        fleetAssetOutsideMetroRate,
        this.clientCommonAddressList,
      );

      if (jobInformation?.jobDurationData && jobInformation?.totals) {
        if (!this.jobAccountingDetails.finishedJobData) {
          this.jobAccountingDetails.finishedJobData = new FinishedJobData();
        }
        this.jobAccountingDetails.finishedJobData.loadDurations =
          jobInformation.jobDurationData.loadDurations;

        this.jobAccountingDetails.finishedJobData.clientDurations =
          jobInformation.jobDurationData.clientDurations;

        this.jobAccountingDetails.finishedJobData.fleetAssetDurations =
          jobInformation.jobDurationData.fleetAssetDurations;
        this.jobAccountingDetails.totals = jobInformation.totals;
      }
      // Add breaks to primary rate breakDurations
      calculateBreakDuration(this.jobAccountingDetails);
      // Send request to child to request work diary history if times have changed
      this.requestWorkDiaryHistory(
        this.jobAccountingDetails.fleetAssetRates[0].rateData,
      );
    } else {
      if (!this.jobAccountingDetails.clientRates?.length) {
        logConsoleError('Client Rates are empty');
      }
      if (!this.jobAccountingDetails.fleetAssetRates?.length) {
        logConsoleError('Fleet Asset Rates are empty');
      }
      if (!this.jobAccountingDetails.clientRates?.[0]?.rateData?.length) {
        logConsoleError('Client Rate Data is empty');
      }
      if (!this.jobAccountingDetails.fleetAssetRates?.[0]?.rateData?.length) {
        logConsoleError('Fleet Asset Rate Data is empty');
      }
    }
    this.allTotalsComputed = true;
  }

  // function to match clientDemurrageBreakdown to fleetAssetDemurrageBreakdown
  public syncDemurrageTimesFromClientToFleet(jobAccountingDetails): void {
    if (
      jobAccountingDetails.finishedJobData.fleetAssetDemurrageBreakdown &&
      jobAccountingDetails.finishedJobData.clientDemurrageBreakdown
    ) {
      const fleetAssetDemurrageBreakdown =
        jobAccountingDetails.finishedJobData.fleetAssetDemurrageBreakdown;
      const clientDemurrageBreakdown =
        jobAccountingDetails.finishedJobData.clientDemurrageBreakdown;
      clientDemurrageBreakdown.forEach((client: DemurrageRateData) => {
        const fleetEntry = fleetAssetDemurrageBreakdown.find(
          (fleet: DemurrageRateData) => fleet.pudId === client.pudId,
        );
        if (fleetEntry) {
          fleetEntry.startTimeInEpoch = client.startTimeInEpoch;
          fleetEntry.endTimeInEpoch = client.endTimeInEpoch;
        }
      });
    }
  }

  public outsideHireFuelSurchargeChanged(value: boolean) {
    this.jobAccountingDetails.fleetAssetRates[0].rate.fuelSurcharge = value;
    if (!value) {
      this.jobAccountingDetails.additionalCharges.fleetAssetFuelSurcharge =
        undefined;
    } else {
      if (
        this.jobAccountingDetails.additionalCharges.fleetAssetFuelSurcharge ===
        undefined
      ) {
        this.jobAccountingDetails.additionalCharges.fleetAssetFuelSurcharge =
          new FleetAssetFuelSurchargeRate();
      }
    }
  }

  /**
   * Returns true if all hire contracts in the additionalAssets list of the job
   * have their associated documents in the appliedHireContracts list, AND are
   * valid for the workDate of the job
   */
  get allHireContractsValid(): boolean {
    return allAdditionalAssetsValidForPricing(
      this.jobDetails.additionalAssets,
      this.hireContracts,
      this.jobDetails.workDate,
    );
  }

  get jobRateData() {
    const { clientRates, fleetAssetRates } = this.jobAccountingDetails;
    if (
      !clientRates ||
      clientRates.length < 1 ||
      !fleetAssetRates ||
      fleetAssetRates.length < 1
    ) {
      return;
    }

    const client = {
      time: clientRates[0].rate.rateTypeId === JobRateType.TIME,
      p2p: clientRates[0].rate.rateTypeId === JobRateType.POINT_TO_POINT,
      distance: clientRates[0].rate.rateTypeId === JobRateType.DISTANCE,
      unitRate: clientRates[0].rate.rateTypeId === JobRateType.UNIT,
      zone: clientRates[0].rate.rateTypeId === JobRateType.ZONE,
      tripRate: clientRates[0].rate.rateTypeId === JobRateType.TRIP,
      zoneToZone: clientRates[0].rate.rateTypeId === JobRateType.ZONE_TO_ZONE,
    };
    const fleetAsset = {
      time: fleetAssetRates[0].rate.rateTypeId === JobRateType.TIME,
      p2p: fleetAssetRates[0].rate.rateTypeId === JobRateType.POINT_TO_POINT,
      distance: fleetAssetRates[0].rate.rateTypeId === JobRateType.DISTANCE,
      unitRate: fleetAssetRates[0].rate.rateTypeId === JobRateType.UNIT,
      zone: fleetAssetRates[0].rate.rateTypeId === JobRateType.ZONE,
      tripRate: fleetAssetRates[0].rate.rateTypeId === JobRateType.TRIP,
      zoneToZone:
        fleetAssetRates[0].rate.rateTypeId === JobRateType.ZONE_TO_ZONE,
    };

    return {
      client,
      fleetAsset,
    };
  }

  get switchRateTypeSelectOptions(): RateTypeSelectOptions[] {
    const validOptions: RateTypeSelectOptions[] = [];
    if (!this.fleetAssetServiceRate) {
      return validOptions;
    }
    const timeRateTypeId = 1;
    const originalRateTypeId = this.jobDetails.serviceTypeObject.rateTypeId;
    const validRateTypes = [timeRateTypeId, originalRateTypeId];
    return validRateTypes.map((i) => {
      const rateName: ServiceTypeRates | undefined = serviceTypeRates.find(
        (sr) => sr.rateTypeId === i,
      );
      return {
        id: i,
        longName: rateName ? rateName.longName : '',
      };
    });
  }

  get fleetAssetHasValidPercentageTypeRate(): boolean {
    if (
      !this.switchRateTypeSelectOptions ||
      this.switchRateTypeSelectOptions.length < 1
    ) {
      return false;
    }
    const serviceTypeId =
      this.jobDetails.serviceTypeObject.rateTypeId === 5
        ? 4
        : this.jobDetails.serviceTypeId;
    const rateTypeId = this.jobDetails.serviceTypeObject.rateTypeId;

    const foundRate = this.fleetAssetServiceRate.rateTableItems.find(
      (rate) =>
        rate.rateTypeId === rateTypeId && rate.serviceTypeId === serviceTypeId,
    );

    if (!foundRate) {
      return false;
    }

    return true;
  }

  get dataHasLoaded() {
    if (this.readOnlyView) {
      return true;
    }
    return this.loadedData.finishedLoading && this.initialRateDataActivated;
  }

  public async changeFleetAssetRateToTime(): Promise<void> {
    await this.generateTimeData(1);
  }

  public setClientMinimumDurationMet(value: boolean) {
    this.$emit('setClientMinimumDurationMet', value);
  }

  public setDriverMinimumDurationMet(value: boolean) {
    this.$emit('setDriverMinimumDurationMet', value);
  }

  /**
   * Handles emit from JobBookingPricingSummary component to increment, decrement
   * or remove an additional charge that is currently in the accountingDetails
   * charge list.
   * @param payload - Contains the update type (INCREMENT, DECREMENT or REMOVE)
   * and the id of the charge to update
   */
  public updateExistingAdditionalCharge(payload: UpdateAdditionalChargeItem) {
    const isUpdateSuccess = applyAdditionalChargeUpdateToAccounting(
      payload,
      this.jobAccountingDetails,
    );
    if (!isUpdateSuccess) {
      return;
    }
    // Update the toll admin and handling charge in the job
    this.refreshTollAdminAndServicesFee();
  }

  /**
   * Refreshes the toll and admin handling charge within the additional charges
   * list
   */
  public refreshTollAdminAndServicesFee() {
    tollAdminAndServicesFeeHandler(
      this.jobAccountingDetails.additionalCharges,
      this.tollAdminAndHandlingChargeItem,
      this.tollAdminAndHandlingId,
      useRootStore().tollChargeTypeId,
    );
  }

  // ===========================================================================
  // Additional Charges
  // ===========================================================================
  /**
   * Handles emit from AddAdditionalCharge component to add a list of charges to the
   * job. Adds the full objects and the ids to the accounting details, then
   * recalculates the toll admin and handling charge if the new charge is a toll
   * charge.
   * @param items The new charges to add to the job
   *
   */
  public addAdditionalChargesToJob(items: AdditionalChargeItem[]) {
    if (!items || items.length === 0) {
      return;
    }
    // Iterate over the items and add them to the charge list
    items.forEach((item: AdditionalChargeItem) => {
      // Check if the charge already exists in the list.
      const foundExistingCharge =
        this.jobAccountingDetails.additionalCharges.chargeList.find(
          (x) => x._id === item._id,
        );

      // If it does, increment the quantity. If not, add it to the list
      if (foundExistingCharge) {
        foundExistingCharge.quantity++;
      } else {
        this.jobAccountingDetails.additionalCharges.chargeList.push(item);
        this.jobAccountingDetails.additionalCharges.chargeIdList.push(
          item._id!,
        );
      }
    });
    // Refresh the toll and admin handling charge
    this.refreshTollAdminAndServicesFee();
  }

  get additionalChargeTypeList() {
    return useRootStore().additionalChargeTypeList;
  }

  /**
   * Returns the AdditionalChargeItem associated with the toll admin and
   * handling charge id (tollAdminAndHandlingId)
   */
  get tollAdminAndHandlingChargeItem() {
    return this.additionalChargeItems.find(
      (x: AdditionalChargeItem) => x._id === this.tollAdminAndHandlingId,
    );
  }

  /**
   * Returns the mongo id of the toll admin and handling charge item
   */
  get tollAdminAndHandlingId(): string | undefined {
    return useRootStore().tollAdminAndHandlingId;
  }

  public updateForceOutsideMetroChargeClient(forceCharge: boolean) {
    this.forceOutsideMetroChargeClient = forceCharge;
    // If emitted boolean is true, then set the outsideMetroRate if it is
    // currently zero
    if (forceCharge) {
      // If the outsideMetroRate value is zero, then set the rate from the value
      // from service rates. If the value is NOT zero, this means the user has
      // edited it so we should not replace the value
      if (
        this.jobAccountingDetails.clientRates &&
        this.jobAccountingDetails.clientRates[0] &&
        this.jobAccountingDetails.clientRates[0].outsideMetroRate === 0
      ) {
        this.jobAccountingDetails.clientRates[0].outsideMetroRate =
          this.clientServiceRate && this.clientServiceRate.outsideMetroRate
            ? this.clientServiceRate.outsideMetroRate
            : 0;
      }
    } else {
      this.jobAccountingDetails.clientRates[0].outsideMetroRate = 0;
    }
  }

  public updateForceOutsideMetroChargeFleetAsset(forceCharge: boolean) {
    this.forceOutsideMetroChargeFleetAsset = forceCharge;
    // If emitted boolean is true, then set the outsideMetroRate if it is
    // currently zero
    if (forceCharge) {
      // If the outsideMetroRate value is zero, then set the rate from the value
      // from service rates. If the value is NOT zero, this means the user has
      // edited it so we should not replace the value
      if (
        this.jobAccountingDetails.fleetAssetRates &&
        this.jobAccountingDetails.fleetAssetRates[0] &&
        this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate === 0
      ) {
        this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate =
          this.fleetAssetServiceRate &&
          this.fleetAssetServiceRate.outsideMetroRate
            ? this.fleetAssetServiceRate.outsideMetroRate
            : 0;
      }
    } else {
      this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate = 0;
    }
  }

  get additionalChargeItems(): AdditionalChargeItem[] {
    return useRootStore().additionalChargeItemList;
  }

  get isEquipmentHire() {
    if (!this.jobDetails.additionalAssets) {
      return false;
    }
    // CHECK FOR EQUIPMENT HIRE
    const equipmentHireAssets = this.jobDetails.additionalAssets.filter(
      (x: AdditionalAsset) => x.contractId !== '',
    );

    return equipmentHireAssets.length > 0;
  }

  // ===========================================================================
  // Mounted
  // ===========================================================================
  public async prepareData() {
    if (
      (this.clientDetails && this.clientDetails.clientId) ||
      this.jobDetails.client.id === 'CS'
    ) {
      this.loadedData.clientDetails = true;
    }

    // CHECK FOR OUTSIDE HIRE
    if (this.truckDetails) {
      if (
        this.truckDetails.outsideHire !== null &&
        this.truckDetails.outsideHire !== undefined
      ) {
        this.isOutsideHire = this.truckDetails.outsideHire;
      } else {
        const owner: FleetAssetOwnerSummary | undefined =
          useFleetAssetOwnerStore().getOwnerFromOwnerId(
            this.truckDetails.fleetAssetOwnerId,
          );
        if (!owner || !owner.affiliation) {
          this.isOutsideHire = false;
        } else {
          this.isOutsideHire = owner.affiliation === '3';
        }
      }
    } else {
      this.isOutsideHire = false;
    }

    this.pudItems = deepCopy(this.jobDetails.pudItems);
    this.jobReferences = deepCopy(this.jobDetails.jobReference);
    this.jobLegsInCorrectOrder = this.jobDetails.isProgressInOrder();

    if (this.isUsingPreviouslyReviewedData) {
      this.updateFullJobAccountingDetails(this.jobDetails.accounting);
    }
  }

  /**
   * Generates summary card data for the additional travel legs ("Pre-First Leg"
   * and "Post-Last Leg") based on the provided client and fleet asset rates.
   *
   * This method determines the relevant durations for the first and last legs
   * of a job, formats them, and constructs summary rows. Returns nothing if the
   * rates aren't TIME.
   *
   * @param clientRate - The primary rate object for the client, containing
   * first/last leg configs
   * @param fleetAssetRate - The primary rate object for the fleet asset,
   * containing first/last leg configs
   * @returns Rows for additional travel legs, or an empty
   * array if not
   * applicable
   */
  public generateReturnLegCardData(
    clientRate: JobPrimaryRate,
    fleetAssetRate: JobPrimaryRate,
  ): PudSummary[] {
    const result: PudSummary[] = [];

    // Prefixes for client and fleet asset
    const CLIENT_PREFIX = 'C: ';
    const FLEET_PREFIX = 'F: ';

    // Helper to get duration string for a rate
    const getDuration = (
      rate: JobPrimaryRate,
      leg: 'first' | 'last',
    ): string | undefined => {
      if (rate.rate.rateTypeId !== JobRateType.TIME) {
        return undefined;
      }
      const rateTypeObject = rate.rate.rateTypeObject as TimeRateType;
      if (leg === 'first') {
        if (
          (rateTypeObject.firstLegTypeId ??
            StartAndReturnLegsEnum.NOT_APPLICABLE) ===
          StartAndReturnLegsEnum.DEPOT
        ) {
          return returnCorrectDuration(
            this.jobDetails.legDurations.depotToFirstLeg,
          );
        }
      } else {
        const lastLegTypeId =
          rateTypeObject.lastLegTypeId ?? StartAndReturnLegsEnum.NOT_APPLICABLE;
        if (lastLegTypeId === StartAndReturnLegsEnum.DEPOT) {
          return returnCorrectDuration(
            this.jobDetails.legDurations.depotToLastLeg,
          );
        } else if (lastLegTypeId === StartAndReturnLegsEnum.RETURN) {
          return returnCorrectDuration(
            this.jobDetails.legDurations.returnToFirstPud,
          );
        }
      }
      return undefined;
    };

    // First leg row
    const clientFirst = getDuration(clientRate, 'first');
    const fleetFirst = getDuration(fleetAssetRate, 'first');
    if (clientFirst || fleetFirst) {
      result.push({
        id: `first-leg`,
        rowType: SummaryRowType.PRE_FIRST_LEG,
        isOverallRow: false,
        title: 'Pre-First Leg',
        subtitle: '',
        arrival: '',
        departure: '',
        isFirst: true,
        isLast: false,
        actualTravel: [
          clientFirst ? `${CLIENT_PREFIX}${clientFirst}` : undefined,
          fleetFirst ? `${FLEET_PREFIX}${fleetFirst}` : undefined,
        ]
          .filter(Boolean)
          .join('; '),
      });
    }

    // Last leg row
    const clientLast = getDuration(clientRate, 'last');
    const fleetLast = getDuration(fleetAssetRate, 'last');
    if (clientLast || fleetLast) {
      result.push({
        id: `last-leg`,
        rowType: SummaryRowType.POST_LAST_LEG,
        isOverallRow: false,
        title: 'Post-Last Leg',
        subtitle: '',
        arrival: '',
        departure: '',
        isFirst: false,
        isLast: true,
        actualTravel: [
          clientLast ? `${CLIENT_PREFIX}${clientLast}` : undefined,
          fleetLast ? `${FLEET_PREFIX}${fleetLast}` : undefined,
        ]
          .filter(Boolean)
          .join('; '),
      });
    }
    // Add a row that sums the first AND last leg durations, with 'Pre-job' and 'Post-job' text
    if (clientFirst || fleetFirst || clientLast || fleetLast) {
      const preJobParts = [
        clientFirst ? `${CLIENT_PREFIX}${clientFirst}` : undefined,
        fleetFirst ? `${FLEET_PREFIX}${fleetFirst}` : undefined,
      ].filter(Boolean);
      const postJobParts = [
        clientLast ? `${CLIENT_PREFIX}${clientLast}` : undefined,
        fleetLast ? `${FLEET_PREFIX}${fleetLast}` : undefined,
      ].filter(Boolean);

      let actualTravel = '';
      if (preJobParts.length) {
        actualTravel += `Pre-job - ${preJobParts.join('; ')}`;
      }
      if (postJobParts.length) {
        if (actualTravel) {
          actualTravel += ' | ';
        }
        actualTravel += `Post-job - ${postJobParts.join('; ')}`;
      }

      result.push({
        id: `first-last-leg`,
        rowType: SummaryRowType.POST_LAST_LEG,
        isOverallRow: true,
        title: 'Additional Times',
        subtitle: '',
        arrival: '',
        departure: '',
        isFirst: false,
        isLast: false,
        actualTravel,
      });
    }

    return result;
  }

  /**
   * Generates a combined summary list for each PUD item, including both event times and delay information.
   * Returns an array of PudSummary objects, including an overall summary row at the top.
   */
  public generateSummaryData(routeProgress?: JobRouteProgress[]): PudSummary[] {
    // Event times
    const getEventTime = (
      eventType: string,
      pudId: string,
      isOverall?: boolean,
    ) => {
      const event = this.jobDetails.returnSpecifiedEvent(eventType, pudId);
      if (!event) {
        return 'Unknown';
      }
      // Show date format if the event is not today
      const format =
        !isToday(event.correctEventTime) && isOverall
          ? 'DD/MM/YYYY hh:mm a'
          : 'hh:mm a';
      return returnFormattedTime(event.correctEventTime, format);
    };

    const pudItems = this.jobDetails.pudItems.filter(
      (p) => p.legTypeFlag === 'P' || p.legTypeFlag === 'D',
    );
    const isLastIndex = pudItems.length - 1;

    // Calculate overall values
    let overallEstimatedLoadMs = 0;
    let overallActualLoadMs = 0;
    let overallLoadDiffMs = 0;
    let overallEstimatedTravelMs = 0;
    let overallActualTravelMs = 0;
    let overallTravelDiffMs = 0;

    if (routeProgress && pudItems.length > 1) {
      for (const pud of pudItems) {
        const progress = routeProgress.find((r) => r.pudId === pud.pudId);
        if (progress) {
          overallEstimatedTravelMs += progress.estimatedTravelTime ?? 0;
          overallActualTravelMs += progress.actualTravelTime ?? 0;
          overallTravelDiffMs += progress.differenceInTravelTimeMs ?? 0;
          overallEstimatedLoadMs += pud.loadTime ?? 0;
          overallActualLoadMs += progress.actualLoadTime ?? 0;
          overallLoadDiffMs += progress.differenceInLoadTime ?? 0;
        }
      }
    }

    // Calculate overall travel percent (average if multiple legs)
    let overallTravelPercentDisplay = '';
    let overallTravelIsError = false;
    let overallLoadPercentDisplay = '';
    let overallLoadIsError = false;
    if (routeProgress && pudItems.length > 1) {
      // TRAVEL
      const overallTravelDiffPercent = RoundCurrencyValue(
        ((overallActualTravelMs - overallEstimatedTravelMs) /
          overallEstimatedTravelMs) *
          100,
      );
      overallTravelPercentDisplay = returnFormattedVariancePercent(
        overallTravelDiffPercent,
      );
      overallTravelIsError =
        overallTravelDiffPercent > ALLOWED_TRAVEL_VARIANCE_PERCENT;

      // LOAD
      const overallLoadDiffPercent = RoundCurrencyValue(
        ((overallActualLoadMs - overallEstimatedLoadMs) /
          overallEstimatedLoadMs) *
          100,
      );
      overallLoadPercentDisplay = returnFormattedVariancePercent(
        overallLoadDiffPercent,
      );
      overallLoadIsError =
        overallLoadDiffPercent > ALLOWED_TRAVEL_VARIANCE_PERCENT;
    }

    // Overall row
    const overallRow: PudSummary = {
      id: 'overall',
      rowType: SummaryRowType.PUD,
      isOverallRow: true,
      title:
        pudItems.length > 1
          ? `${pudItems[0].address?.suburb ?? '-'} → ${
              pudItems[isLastIndex].address?.suburb ?? '-'
            }`
          : '-',
      subtitle: 'Overall',
      arrival: getEventTime('ARRIVED', pudItems[0].pudId, true),
      departure: getEventTime(
        'FINISHED',
        pudItems[pudItems.length - 1].pudId,
        true,
      ),
      isFirst: true,
      isLast: true,
      estimatedTravel: overallEstimatedTravelMs
        ? returnCorrectDuration(overallEstimatedTravelMs)
        : '',
      actualTravel: overallActualTravelMs
        ? returnCorrectDuration(overallActualTravelMs)
        : '',
      travelDifferenceTime: overallTravelDiffMs
        ? returnCorrectDuration(overallTravelDiffMs)
        : '',
      travelDifferencePercent: overallTravelPercentDisplay,
      travelIsError: overallTravelIsError,
      estimatedLoad: overallEstimatedLoadMs
        ? returnCorrectDuration(overallEstimatedLoadMs)
        : '',
      actualLoad: overallActualLoadMs
        ? returnCorrectDuration(overallActualLoadMs)
        : '',
      loadDifferenceTime: overallLoadDiffMs
        ? returnFormattedVarianceDuration(overallLoadDiffMs)
        : '',
      loadDifferencePercent: overallLoadPercentDisplay,
      loadIsError: overallLoadIsError,
    };

    const pudRows = pudItems.map((pud, index) => {
      const arriveStr = getEventTime('ARRIVED', pud.pudId);
      const departStr = getEventTime('FINISHED', pud.pudId);

      // Delay info (if available)
      let delay: Partial<PudSummary> = {};
      if (routeProgress) {
        const progress = routeProgress.find((r) => r.pudId === pud.pudId);
        if (progress) {
          const estimatedLoadTime = pud.loadTime;
          delay = {
            estimatedTravel: progress.estimatedTravelTimeReadable ?? '',
            actualTravel: progress.actualTravelTimeReadable ?? '',
            travelDifferenceTime: progress.differenceInTravelTimeMs
              ? returnCorrectDuration(progress.differenceInTravelTimeMs)
              : '',
            travelDifferencePercent:
              progress.differenceInTravelTimePercentReadable,
            travelIsError:
              (progress.differenceInTravelTimePercent ?? 0) >
              ALLOWED_TRAVEL_VARIANCE_PERCENT,
            estimatedLoad: estimatedLoadTime
              ? returnCorrectDuration(estimatedLoadTime)
              : '',
            actualLoad: progress.actualLoadTimeReadable,
            loadDifferenceTime: progress.differenceInLoadTime
              ? returnFormattedVarianceDuration(progress.differenceInLoadTime)
              : '',
            loadDifferencePercent: progress.differenceInLoadTimePercentReadable,
            loadIsError:
              (progress.differenceInLoadTimePercent ?? 0) >
              ALLOWED_TRAVEL_VARIANCE_PERCENT,
          };
        }
      }

      return {
        id: pud.pudId,
        rowType: SummaryRowType.PUD,
        isOverallRow: false,
        title: `${index + 1}. ${pud.address?.suburb ?? '-'}`,
        subtitle: pud.customerDeliveryName ? pud.customerDeliveryName : '-',
        arrival: arriveStr,
        departure: departStr,
        isFirst: index === 0,
        isLast: index === isLastIndex,
        ...delay,
      };
    });

    return [overallRow, ...pudRows];
  }

  // Return rates to their default states, clear custom values
  public refreshRatesToDefault() {
    this.isUsingPreviouslyReviewedData = false;
    this.resetState();
    this.updateFullJobAccountingDetails(new JobAccountingDetails());
    this.initialiseRateData();
  }

  public resetState() {
    this.allTotalsComputed = false;
    this.clientServiceRate = new ClientServiceRate();
    this.clientFuelSurcharges = [];
    this.fleetAssetServiceRate = new FleetAssetServiceRate();
    this.fleetAssetFuelSurcharges = [];
    this.loadedData = new LoadedReview();
    this.loadedData.breakDurations = true;
    this.rateRetrievalErrors = [];
  }

  // Set the rateSearchEpoch back to the value of the first ARRIVED pud event
  public setDefaultsFromJob() {
    if (this.jobDetails.workDate) {
      this.rateSearchEpoch = this.jobDetails.workDate;
      this.rateSearchEpochTemp = this.jobDetails.workDate;
    }
    // Set original serviceTypeId/rateTypeId details to local variable for comparison
    this.originalJobValues.clientId = this.jobDetails.client.id;
    this.originalJobValues.jobServiceTypeId = this.jobDetails.serviceTypeId;
    this.originalJobValues.jobRateTypeId =
      this.jobDetails.serviceTypeObject.rateTypeId;
    this.originalJobValues.fleetAssetRateTypeId =
      this.jobDetails.fleetAssetRateTypeId !== null
        ? this.jobDetails.fleetAssetRateTypeId
        : this.jobDetails.serviceTypeObject.rateTypeId;
    this.originalJobValues.fleetAssetServiceTypeId = this.jobDetails
      .fleetAssetServiceTypeId
      ? this.jobDetails.fleetAssetServiceTypeId
      : this.jobDetails.serviceTypeId;
  }
  // Set visibility of the break dialog
  public setViewingBreakDurationDialog(value: boolean) {
    this.viewingBreakDurationDialog = value;
  }

  // Search for break work diary that are between the current start and end times
  public requestWorkDiaryHistory(rateData: TimeTypeJobRateData[]) {
    if (!rateData.length || !this.jobDetails.driverId) {
      return;
    }
    // Find break duration component using ref
    const foundBreakDurationManagement = this.$refs
      .breakDurationManagement as any;
    // If we successfully find the component using the ref, then call method in
    // child with updated rate data
    if (foundBreakDurationManagement) {
      foundBreakDurationManagement.requestWorkDiaryHistory(rateData);
    }
  }

  // Initialise data using selected values for isUsingPreviouslyReviewedData and
  // rateSearchEpoch
  public async initialiseRateData() {
    this.prepareData();

    // Mount the rates retriever component
    this.isRequestingJobRateData = true;
    this.rateRetrievalErrors = [];
    const result = await retrieveJobRateDataForPricing(
      this.jobDetails,
      this.rateSearchEpoch,
    );
    this.allRateResponsesReceived(result);
    this.isRequestingJobRateData = false;
  }

  public beforeMount() {
    // On mount, set static variables like 'is equipment hire' etc.
    if (!this.jobDetails) {
      return;
    }

    // TODO we will need to revisit this when we add functionality to price job with no puds
    if (this.jobDetails.pudItems && this.jobDetails.pudItems.length < 2) {
      return;
    }

    if (this.readOnlyView) {
      const accountingClone: JobAccountingDetails =
        initialiseJobAccountingDetails(this.jobDetails.accounting);

      this.jobAccountingDetails.additionalCharges =
        accountingClone.additionalCharges
          ? accountingClone.additionalCharges
          : new AdditionalChargeList();
      this.jobAccountingDetails.clientRates = accountingClone.clientRates
        ? accountingClone.clientRates
        : [new JobPrimaryRate()];
      this.jobAccountingDetails.fleetAssetRates =
        accountingClone.fleetAssetRates
          ? accountingClone.fleetAssetRates
          : [new JobPrimaryRate()];
      this.jobAccountingDetails.finishedJobData =
        accountingClone.finishedJobData
          ? accountingClone.finishedJobData
          : new FinishedJobData();
      this.jobAccountingDetails.totals = accountingClone.totals
        ? accountingClone.totals
        : new JobAccountingTotals();

      this.allRateResponsesReceived(null);
      this.generateHeaderContent(
        this.jobAccountingDetails.clientRates[0],
        this.jobAccountingDetails.fleetAssetRates[0],
      );
      // this.generateSummaryCardData();
      return;
    }

    // If we are not in read only mode, then check if there are any additional
    // charges in the job that we need to preserve
    if (this.jobDetails.accounting.additionalCharges.chargeList.length > 0) {
      this.jobAccountingDetails.additionalCharges.chargeIdList = deepCopy(
        this.jobDetails.accounting.additionalCharges.chargeIdList,
      );
      this.jobAccountingDetails.additionalCharges.chargeList = deepCopy(
        this.jobDetails.accounting.additionalCharges.chargeList,
      );
    }

    // Check if JobDetails has already been reviewed in the past
    const foundExistingReviewedStatus =
      this.jobDetails.returnSpecifiedEvent('REVIEWED');

    this.jobHasBeenPreviouslyReviewed =
      foundExistingReviewedStatus !== undefined;
    this.isUsingPreviouslyReviewedData =
      foundExistingReviewedStatus !== undefined;
    this.setDefaultsFromJob();
    this.initialiseRateData();
  }
}
