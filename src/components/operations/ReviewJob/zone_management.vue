<template>
  <div class="zone-management">
    <v-layout px-2>
      <v-spacer></v-spacer>
      <v-flex md6 style="padding-left: 12px">
        <v-layout justify-end align-center wrap>
          <v-flex md12>
            <v-layout
              style="border-bottom: 2px solid hsla(0, 0%, 100%, 0.12)"
              class="mb-1"
            >
              <v-flex md5>
                <span
                  class="column-header"
                  style="padding-left: 3px; padding-right: 3px"
                  >Unit</span
                >
              </v-flex>
              <v-flex md3>
                <span class="column-header">Rate</span>
              </v-flex>
              <v-flex md4>
                <v-layout justify-end>
                  <span class="column-header">Amount</span>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 v-if="isClient">
            <v-layout
              row
              wrap
              v-for="(zoneGroup, groupIndex) of displayInformation"
              :key="groupIndex"
            >
              <template v-if="groupIndex !== displayInformation.length - 1">
                <v-flex md12>
                  <v-layout
                    justify-space-between
                    align-center
                    wrap
                    v-for="(pud, pudIndex) of zoneGroup.puds"
                    :key="pudIndex"
                    class="line-item"
                  >
                    <v-flex md5>
                      <v-layout>
                        {{ pud.unit }}
                        <v-tooltip
                          bottom
                          v-if="fuelIsApplied(pud.zoneId, false)"
                        >
                          <template v-slot:activator="{ on }">
                            <v-icon
                              size="6"
                              color="info"
                              v-on="on"
                              style="
                                cursor: pointer;
                                padding-left: 2px;
                                padding-top: 2px;
                              "
                            >
                              fas fa-asterisk
                            </v-icon>
                          </template>
                          <span>Fuel Surcharge Applies</span>
                        </v-tooltip>
                      </v-layout>
                    </v-flex>
                    <v-flex md3> {{ pud.rate }} </v-flex>
                    <v-flex md4>
                      <v-layout justify-end>
                        ${{
                          displayCurrencyValue(pud.freightChargesTotalExclGst)
                        }}
                      </v-layout>
                    </v-flex>
                    <v-flex md12 v-if="demurrageExists(pud.pudId)">
                      <v-layout>
                        <v-flex md5>
                          <v-layout>
                            Demurrage
                            <v-tooltip
                              bottom
                              v-if="fuelIsApplied(pud.zoneId, true)"
                            >
                              <template v-slot:activator="{ on }">
                                <v-icon
                                  size="6"
                                  color="info"
                                  v-on="on"
                                  style="
                                    cursor: pointer;
                                    padding-left: 2px;
                                    padding-top: 2px;
                                  "
                                >
                                  fas fa-asterisk
                                </v-icon>
                              </template>
                              <span>Fuel Surcharge Applies</span>
                            </v-tooltip>
                          </v-layout>
                        </v-flex>
                        <v-flex md3>
                          {{ demurrageRateByZoneId(pud.zoneId, pud.pudId) }}
                        </v-flex>
                        <v-flex md4>
                          <v-layout justify-end>
                            {{ demurrageAmountExlGstByPudId(pud.pudId) }}
                          </v-layout>
                        </v-flex>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-divider class="my-2"></v-divider>
                </v-flex>
              </template>
            </v-layout>

            <v-layout wrap justify-space-between align-center>
              <v-flex md5 class="main-line-item"> Freight Total: </v-flex>

              <v-flex md3> </v-flex>

              <v-flex md4>
                <v-layout justify-end style="padding-right: 3px">
                  ${{ displayCurrencyValue(zoneBreakdown.rate) }}
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-divider class="my-2"></v-divider>
              </v-flex>

              <v-flex md5 class="main-line-item">
                <v-layout>
                  Demurrage Total:

                  <DemurrageDuration
                    class="pl-2"
                    style="padding-top: 2px"
                    :demurrageRateData="demurrageRates"
                    @refreshAccountingTotals="refreshAccountingTotals"
                    :pudItems="pudItems"
                    :readOnly="readOnlyView"
                    :isClient="true"
                    :workDiaryList="workDiaryList"
                  />
                </v-layout>
              </v-flex>
              <v-flex md3> </v-flex>
              <v-flex md4>
                <v-layout justify-end style="padding-right: 3px">
                  <span> {{ demurrageTotalExclGst }}</span>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-divider class="mt-2"></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 v-if="!isClient">
            <v-layout justify-space-between align-center wrap class="line-item">
              <v-flex md5>
                <v-layout>
                  ZONE
                  <v-tooltip bottom v-if="fleetAssetFuelIsApplied">
                    <template v-slot:activator="{ on }">
                      <v-icon
                        size="6"
                        color="info"
                        v-on="on"
                        style="
                          cursor: pointer;
                          padding-left: 2px;
                          padding-top: 2px;
                        "
                      >
                        fas fa-asterisk
                      </v-icon>
                    </template>

                    <span>Fuel Surcharge Applies</span>
                  </v-tooltip>
                </v-layout>
              </v-flex>
              <v-flex md3>
                <v-layout align-center>
                  {{
                    editedZoneRatePercentage
                      ? `${DisplayCurrencyValue(editedZoneRatePercentage)}%`
                      : 'N/A'
                  }}
                  <v-icon
                    size="12"
                    style="position: relative; top: -1px"
                    class="pl-1"
                    v-if="!readOnlyView"
                    :disabled="readOnlyView"
                    @click="showZoneRatePercentageDialog = true"
                    >fal fa-edit</v-icon
                  >
                  <ContentDialog
                    :showDialog.sync="showZoneRatePercentageDialog"
                    title="Edit Zone Rate Percentage"
                    width="40%"
                    contentPadding="pa-0"
                    @cancel="cancelFleetAssetPercentageEdit"
                    @confirm="confirmAndSetZoneRatePercentage"
                    :showActions="true"
                    confirmBtnText="Confirm"
                  >
                    <v-layout>
                      <v-flex md12 class="body-scrollable--75 pa-3">
                        <v-form ref="fleetAssetPercentageForm">
                          <FormFieldRow
                            label="Zone Rate Percentage"
                            :required="true"
                          >
                            <v-text-field
                              type="number"
                              :rules="[validate.required]"
                              v-model.number="editedZoneRatePercentage"
                              persistent-hint
                              class="v-solo-custom"
                              solo
                              flat
                              color="light-blue"
                              hint="The zone rate percentage that will be applied to the fleet asset."
                              label="Zone Rate Percentage"
                              @focus="$event.target.select()"
                            />
                          </FormFieldRow>
                        </v-form>
                      </v-flex>
                    </v-layout>
                  </ContentDialog>
                </v-layout>
              </v-flex>
              <v-flex md4>
                <v-layout justify-end>
                  ${{
                    displayCurrencyValue(
                      zoneBreakdown.zoneCharges[0].freightChargesTotalExclGst,
                    )
                  }}
                </v-layout>
              </v-flex>
            </v-layout>

            <v-layout
              justify-space-between
              align-center
              wrap
              v-for="(pud, lineItemIndex) of fleetAssetDemurrageLineItems"
              :key="pud.pudId || lineItemIndex"
              class="line-item"
            >
              <template v-if="pud.displayLineItem">
                <v-flex md5>
                  <v-layout>
                    {{ pud.unit }}
                    <v-tooltip bottom v-if="pud.demurrageFuelSurchargeApplies">
                      <template v-slot:activator="{ on }">
                        <v-icon
                          size="6"
                          color="info"
                          v-on="on"
                          style="
                            cursor: pointer;
                            padding-left: 2px;
                            padding-top: 2px;
                          "
                        >
                          fas fa-asterisk
                        </v-icon>
                      </template>
                      <span>Fuel Surcharge Applies</span>
                    </v-tooltip>
                  </v-layout>
                </v-flex>
                <v-flex md3> {{ pud.rate }} </v-flex>
                <v-flex md4>
                  <v-layout justify-end> ${{ pud.amount }} </v-layout>
                </v-flex>
              </template>
            </v-layout>

            <v-layout>
              <v-flex md12>
                <v-divider class="my-2"></v-divider>
              </v-flex>
            </v-layout>
            <v-layout wrap justify-space-between align-center>
              <v-flex md5 class="main-line-item"> Freight Total: </v-flex>

              <v-flex md3> </v-flex>

              <v-flex md4>
                <v-layout justify-end style="padding-right: 3px">
                  ${{
                    displayCurrencyValue(
                      zoneBreakdown.zoneCharges[0].freightChargesTotalExclGst,
                    )
                  }}
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-divider class="my-2"></v-divider>
              </v-flex>
              <v-flex md5 class="main-line-item">
                <v-layout>
                  Demurrage Total:

                  <DemurrageDuration
                    class="pl-2"
                    style="padding-top: 2px"
                    :demurrageRateData="demurrageRates"
                    :isClient="false"
                    @refreshAccountingTotals="refreshAccountingTotals"
                    :pudItems="pudItems"
                    :readOnly="readOnlyView"
                    :clientDemurrageRateData="
                      accounting.finishedJobData.clientDemurrageBreakdown
                    "
                    :workDiaryList="workDiaryList"
                  />
                </v-layout>
              </v-flex>
              <v-flex md3> </v-flex>
              <v-flex md4>
                <v-layout justify-end style="padding-right: 3px">
                  <span> {{ demurrageTotalExclGst }}</span>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-divider class="mt-2"></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import FormFieldRow from '@/components/common/ui-elements/form_field_row.vue';
import DemurrageDuration from '@/components/operations/ReviewJob/demurrage_duration/demurrage_duration.vue';
import {
  addPercentageTo,
  DisplayCurrencyValue,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { isZoneRateTypeObject } from '@/helpers/RateHelpers/RateTableItemHelpers';
import {
  fleetAssetZoneBreakDown,
  generateZoneBreakdowns,
} from '@/helpers/RateHelpers/ZoneRateHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import { Validation } from '@/interface-models/Generic/Validation';
import { ZoneBreakDown } from '@/interface-models/Jobs/FinishedJobDetails/ZoneBreakDown';
import ZoneRateData from '@/interface-models/Jobs/FinishedJobDetails/zoneRateData';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { DemurrageRateData } from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  onMounted,
  ref,
  Ref,
  WritableComputedRef,
} from 'vue';

interface FleetAssetDemurrageLineItem {
  pudId?: string;
  unit: string;
  rate: string;
  amount: string;
  demurrageFuelSurchargeApplies: boolean;
  displayLineItem: boolean;
}

interface DisplayInformation {
  zoneName: string;
  puds: ZoneRateData[];
}

const emit = defineEmits<{
  (event: 'refreshAccountingTotals'): void;
}>();

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    zoneRate: RateTableItems;
    accounting: JobAccountingDetails;
    isClient?: boolean;
    clientZoneRate?: RateTableItems;
    readOnlyView?: boolean;
    pudItems: PUDItem[];
    driverRegisteredForGst?: boolean;
    title?: string;
    subtitle?: string;
  }>(),
  {
    isClient: true,
    readOnlyView: false,
    driverRegisteredForGst: false,
    title: '',
    subtitle: '',
    clientZoneRate: undefined,
  },
);

const showZoneRatePercentageDialog: Ref<boolean> = ref(false);
const originalFleetAssetPercentage: Ref<number | null> = ref(null);

const _hiddenZoneRatePercentage: Ref<number | null> = ref(null);
const _adjustedZoneRatePercentage: Ref<number | null> = ref(null);

const fleetAssetPercentageForm: Ref<any> = ref(null);

const displayCurrencyValue = DisplayCurrencyValue;

/**
 * Gets and sets the adjustedZoneRatePercentage, which contains a user-facing
 * value of the zone rate percentage with any rate variations applied. Also sets
 * to _hiddenZoneRatePercentage, which will be used to store the 'original'
 * percentage, with no variation applied.
 */
const editedZoneRatePercentage: WritableComputedRef<number | null> = computed({
  get(): number | null {
    return _adjustedZoneRatePercentage.value;
  },
  set(value: number | null): void {
    const fleetVariationPct =
      props.accounting.clientServiceRateVariations
        ?.fleetAssetAdjustmentPercentage;

    if (!fleetVariationPct) {
      _hiddenZoneRatePercentage.value = value;
      _adjustedZoneRatePercentage.value = value;
      return;
    }
    if (value === null) {
      _hiddenZoneRatePercentage.value = null;
      _adjustedZoneRatePercentage.value = null;
    } else {
      // Reverse the adjustment set in onMounted: hidden = value / (1 + pct/100)
      const calculated =
        fleetVariationPct !== 0 ? value / (1 + fleetVariationPct / 100) : value;
      _hiddenZoneRatePercentage.value = calculated;
      _adjustedZoneRatePercentage.value = value;
    }
  },
});

/**
 * Returns the zone breakdowns for the current job.
 */
const zoneBreakdown: ComputedRef<ZoneBreakDown> = computed(() => {
  const zoneRates = props.zoneRate.rateTypeObject as ZoneRateType[];
  if (props.isClient) {
    return generateZoneBreakdowns(
      props.jobDetails.pudItems,
      zoneRates,
      props.accounting.clientServiceRateVariations?.clientAdjustmentPercentage,
    );
  }
  const clientRate = generateZoneBreakdowns(
    props.jobDetails.pudItems,
    props.clientZoneRate?.rateTypeObject as ZoneRateType[],
    props.accounting.clientServiceRateVariations?.clientAdjustmentPercentage,
  );
  return fleetAssetZoneBreakDown(
    zoneRates,
    clientRate.rate,
    props.driverRegisteredForGst,
    props.accounting.clientServiceRateVariations
      ?.fleetAssetAdjustmentPercentage,
  );
});

/**
 * Groups zone breakdowns by consecutive zoneId.
 */
const groupedZoneBreakDowns: ComputedRef<ZoneRateData[][]> = computed(() => {
  const groupedZones: ZoneRateData[][] = [];
  let currentGroup = 0;
  for (let i = 0; i < zoneBreakdown.value.zoneCharges.length; i++) {
    if (i === 0) {
      groupedZones.push([zoneBreakdown.value.zoneCharges[i]]);
      continue;
    }
    if (
      zoneBreakdown.value.zoneCharges[i].zoneId ===
      zoneBreakdown.value.zoneCharges[i - 1].zoneId
    ) {
      groupedZones[currentGroup].push(zoneBreakdown.value.zoneCharges[i]);
    } else {
      currentGroup++;
      groupedZones.push([zoneBreakdown.value.zoneCharges[i]]);
    }
  }
  return groupedZones;
});

/**
 * Returns the demurrage rates for the current job.
 */
const demurrageRates: ComputedRef<DemurrageRateData[]> = computed(() => {
  return props.isClient
    ? props.accounting.finishedJobData.clientDemurrageBreakdown
    : props.accounting.finishedJobData.fleetAssetDemurrageBreakdown;
});

/**
 * Returns the work diary records for the job.
 */
const workDiaryList: ComputedRef<WorkDiarySummary[]> = computed(() => {
  if (props.isClient) {
    return (
      props.accounting.clientRates[0]?.breakDuration?.breakSummaryList ?? []
    );
  }
  return (
    props.accounting.fleetAssetRates[0]?.breakDuration?.breakSummaryList ?? []
  );
});

/**
 * Determines if fuel surcharge is applied for a zone and demurrage.
 * @param zoneId - Zone identifier.
 * @param isDemurrage - Whether demurrage is being checked.
 */
function fuelIsApplied(zoneId: number, isDemurrage: boolean): boolean {
  const zoneRate: ZoneRateType | undefined = (
    props.zoneRate.rateTypeObject as ZoneRateType[]
  ).find((x: ZoneRateType) => x.zone === zoneId);

  if (!zoneRate) {
    return false;
  }

  const demurrageFuelApplies = zoneRate.demurrage.demurrageFuelSurchargeApplies;

  if (zoneRate.appliedFuelSurchargeId === 1) {
    return isDemurrage ? demurrageFuelApplies : true;
  }
  return false;
}

/**
 * Returns true if fleet asset fuel surcharge is applied.
 */
const fleetAssetFuelIsApplied: ComputedRef<boolean> = computed(() => {
  if (
    props.isClient ||
    !props.accounting.additionalCharges.clientFuelSurcharge
  ) {
    return false;
  }
  const clientFuelSurchargeRate =
    props.accounting.additionalCharges.clientFuelSurcharge
      .appliedFuelSurchargeRate;
  const appliedFuelSurchargeId = (
    props.zoneRate.rateTypeObject as ZoneRateType[]
  )[0].appliedFuelSurchargeId;

  if (appliedFuelSurchargeId === 1) {
    return true;
  }
  if (appliedFuelSurchargeId === 2) {
    return clientFuelSurchargeRate > 0;
  }
  return false;
});

/**
 * Emits an event to refresh accounting totals.
 */
function refreshAccountingTotals(): void {
  emit('refreshAccountingTotals');
}

/**
 * Returns fleet asset demurrage line items for display.
 */
const fleetAssetDemurrageLineItems: ComputedRef<FleetAssetDemurrageLineItem[]> =
  computed(() => {
    if (props.isClient) {
      return [];
    }
    return demurrageRates.value.map((x: DemurrageRateData) => {
      const pud = props.pudItems.find(
        (pudItem: PUDItem) => pudItem.pudId === x.pudId,
      );
      const suburb = pud ? pud.address.suburb : '';
      const unit = 'Demurrage - ' + suburb;
      const rate = demurrageRateByZoneId(x.zoneId ?? -1, x.pudId);
      const amount = DisplayCurrencyValue(x.demurrageChargeExclGst);
      const displayLineItem: boolean = !!x.demurrageChargeTotal;
      return {
        pudId: pud?.pudId,
        unit,
        rate,
        amount,
        demurrageFuelSurchargeApplies: x.demurrageFuelSurchargeApplies,
        displayLineItem,
      };
    });
  });

/**
 * Returns the demurrage total excluding GST as a formatted string.
 */
const demurrageTotalExclGst: ComputedRef<string> = computed(() => {
  if (props.isClient) {
    return (
      '$' +
      DisplayCurrencyValue(
        props.accounting.totals.subtotals.demurrageChargeTotals.client,
      )
    );
  }
  return (
    '$' +
    DisplayCurrencyValue(
      props.accounting.totals.subtotals.demurrageChargeTotals.fleetAsset,
    )
  );
});

/**
 * Returns the demurrage rate by zone ID and PUD ID.
 * @param zoneId - Zone identifier.
 * @param pudId - PUD identifier.
 */
function demurrageRateByZoneId(zoneId: number, pudId: string): string {
  let zoneRate: ZoneRateType | undefined;
  if (props.isClient) {
    zoneRate = (props.zoneRate.rateTypeObject as ZoneRateType[]).find(
      (x: ZoneRateType) => x.zone === zoneId,
    );
  } else {
    zoneRate = (props.zoneRate.rateTypeObject as ZoneRateType[])[0];
  }
  if (!zoneRate) {
    return '-';
  }
  const demurrage: DemurrageRateData | undefined = demurrageRates.value.find(
    (x: DemurrageRateData) => x.pudId === pudId,
  );
  if (!demurrage) {
    return '-';
  }

  const demurrageHours =
    moment.duration(demurrage.demurrageDurationInMilliseconds).hours() > 0
      ? moment.duration(demurrage.demurrageDurationInMilliseconds).hours()
      : 0;
  const demurrageMinutes =
    moment.duration(demurrage.demurrageDurationInMilliseconds).minutes() > 0
      ? moment.duration(demurrage.demurrageDurationInMilliseconds).minutes()
      : 0;
  return (
    '$' +
    DisplayCurrencyValue(demurrage.rate) +
    ' (' +
    demurrageHours +
    'h ' +
    demurrageMinutes +
    'm)'
  );
}

/**
 * Returns true if demurrage exists for a given PUD ID.
 * @param pudId - PUD identifier.
 */
function demurrageExists(pudId: string): boolean {
  const demurrageBreakdown: DemurrageRateData[] =
    props.accounting.finishedJobData.clientDemurrageBreakdown;
  if (!demurrageBreakdown) {
    return false;
  }
  const demurrage: DemurrageRateData | undefined = demurrageBreakdown.find(
    (x: DemurrageRateData) => x.pudId === pudId,
  );
  return !!demurrage?.demurrageChargeTotal;
}

/**
 * Returns the demurrage amount excluding GST for a given PUD ID.
 * @param pudId - PUD identifier.
 */
function demurrageAmountExlGstByPudId(pudId: string): string {
  const demurrageBreakdown: DemurrageRateData[] =
    props.accounting.finishedJobData.clientDemurrageBreakdown;
  if (!demurrageBreakdown) {
    return '-';
  }
  const demurrage: DemurrageRateData | undefined = demurrageBreakdown.find(
    (x: DemurrageRateData) => x.pudId === pudId,
  );
  if (!demurrage) {
    return '-';
  }
  return '$' + DisplayCurrencyValue(demurrage.demurrageChargeExclGst);
}

/**
 * Returns the zone name for a given zone ID.
 * @param zoneId - Zone identifier.
 */
function zoneName(zoneId: number): string {
  const zone = (props.zoneRate.rateTypeObject as ZoneRateType[]).find(
    (x: ZoneRateType) => x.zone === zoneId,
  );
  return zone ? zone.zoneName : '';
}

/**
 * Returns display information for the grouped zone breakdowns.
 */
const displayInformation: ComputedRef<DisplayInformation[]> = computed(() => {
  const displayGroup: DisplayInformation[] = [];
  for (const group of groupedZoneBreakDowns.value) {
    displayGroup.push({
      zoneName: zoneName(group[0].zoneId),
      puds: group,
    });
  }
  return displayGroup;
});

/**
 * Returns validation rules.
 */
const validate: ComputedRef<Validation> = computed(() => validationRules);

/**
 * Confirms and sets the zone rate percentage to the rateTypeObject in the
 * zoneRate prop. Sets the fleet asset percentage with the
 * _hiddenZoneRatePercentage value, which is the percentage edited by the
 * user, less any rateVariations.
 */
function confirmAndSetZoneRatePercentage(): void {
  if (
    !fleetAssetPercentageForm.value.validate() ||
    !_hiddenZoneRatePercentage.value
  ) {
    showNotification(`A percentage must be provided.`);
    return;
  }
  (props.zoneRate.rateTypeObject as ZoneRateType[])[0].percentage =
    RoundCurrencyValue(_hiddenZoneRatePercentage.value);
  showZoneRatePercentageDialog.value = false;
}

/**
 * Cancels editing of the fleet asset percentage.
 */
function cancelFleetAssetPercentageEdit(): void {
  if (
    editedZoneRatePercentage.value === null &&
    editedZoneRatePercentage.value !== 0
  ) {
    editedZoneRatePercentage.value = originalFleetAssetPercentage.value;
  }
  showZoneRatePercentageDialog.value = false;
}

/**
 * Sets original and edited fleet asset percentage values on mount.
 */
onMounted(() => {
  try {
    if (props.isClient) {
      return;
    }
    let fleetZoneRatePct: number = 0;
    let fleetVariationPct: number = 0;
    if (!props.readOnlyView) {
      fleetZoneRatePct = (props.zoneRate.rateTypeObject as ZoneRateType[])[0]
        .percentage;

      // Get rate variations percentage from accounting prop
      fleetVariationPct =
        props.accounting.clientServiceRateVariations
          ?.fleetAssetAdjustmentPercentage ?? 0;
    } else {
      if (
        isZoneRateTypeObject(
          props.jobDetails.accounting?.fleetAssetRates?.[0]?.rate.rateTypeId,
          props.jobDetails.accounting?.fleetAssetRates?.[0]?.rate
            .rateTypeObject,
        )
      ) {
        const rateTypeObject =
          props.jobDetails.accounting?.fleetAssetRates?.[0]?.rate
            .rateTypeObject;
        fleetZoneRatePct = rateTypeObject[0]?.percentage ?? 0;

        // Get rate variations percentage from jobDetails
        fleetVariationPct =
          props.jobDetails.accounting?.clientServiceRateVariations
            ?.fleetAssetAdjustmentPercentage ?? 0;
      } else {
        throw new Error(
          'Fleet Asset rateTypeObject is in unexpected form for zone rate',
        );
      }
    }
    // Set _adjustedZoneRatePercentage with any rate variations applied. This will
    // be the value that the user edits in the dialog when adjusting the zone rate
    // percentage.
    _adjustedZoneRatePercentage.value = addPercentageTo(
      fleetZoneRatePct,
      fleetVariationPct ?? 0,
    );
    // Set original and hidden fields to the original, unadjusted percentage
    originalFleetAssetPercentage.value = fleetZoneRatePct;
    _hiddenZoneRatePercentage.value = fleetZoneRatePct;
  } catch (error) {
    logConsoleError('Error setting zone rate percentage on mount', error);
  }
});
</script>
<style scoped lang="scss">
.zone-management {
  .rate-title {
    font-size: $font-size-small;
    text-transform: uppercase;
    color: rgb(175, 171, 171);
    font-weight: 600;
    display: block;
    line-height: 1;
  }

  .rate {
    font-size: 1.05em;
    text-transform: uppercase;

    color: var(--heading-text-color);
    font-weight: 600;
    line-height: 1;
    padding-right: 10px;
  }

  .rate-value {
    line-height: 1;
    font-weight: 600;
    letter-spacing: 1px;
    display: block;
    color: var(--text-color);
    font-size: 1.15em;
  }

  .rate-container {
    padding: 12px;
    position: absolute;
    bottom: 0px;
    width: 100%;
  }
}

.column-header {
  color: var(--light-text-color);
  text-transform: uppercase;
  font-size: $font-size-13;
  font-weight: 600;
}

.line-item {
  color: #bcbcbe;
  text-transform: uppercase;
  font-size: $font-size-12;
  font-weight: 600;
  padding-left: 3px;
  padding-right: 3px;
}

.main-line-item {
  font-size: $font-size-13;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--light-text-color);
  padding-left: 3px;
  padding-right: 3px;
}
</style>
