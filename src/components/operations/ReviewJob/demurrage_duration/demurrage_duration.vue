<template>
  <v-layout>
    <v-icon
      icon
      small
      text
      class="demurrage-icon"
      @click="dialogIsOpen = true"
      >{{ !readOnly ? 'fal fa-edit' : 'fal fa-search' }}</v-icon
    >
    <!-- <v-icon size="12" v-if="readOnly" class="ml-1" @click="dialogIsOpen = true"
      >fal fa-search</v-icon
    > -->
    <v-dialog
      v-model="dialogIsOpen"
      content-class="v-dialog-custom"
      width="700px"
      persistent
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Demurrage Durations</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="closeDemurrageDuration"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <div class="app-theme__center-content--body dialog-content">
        <v-form ref="demurrageDurationForm">
          <v-layout wrap class="px-4 py-2">
            <h3 class="subheader--light mb-2">Stops</h3>
            <v-flex
              md12
              v-for="(demurrage, demurrageIndex) of demurrageTimes"
              :key="demurrageIndex"
            >
              <v-layout>
                <div :class="pudLegType(demurrage.pudId)"></div>
                <h6>{{ pudInformation(demurrage.pudId) }}</h6>
                <v-spacer></v-spacer>
                <span
                  v-if="
                    demurrageOverlapsByPud &&
                    demurrageOverlapsByPud.get(demurrage.pudId)
                  "
                  class="break-txt"
                >
                  * Includes
                  <span class="accent-text--primary" v-if="demurrage.pudId">
                    {{
                      returnDurationFromMilliseconds(
                        demurrageOverlapsByPud.get(demurrage.pudId) ?? 0,
                      )
                    }}
                  </span>
                  of Break
                </span>
              </v-layout>
              <v-layout wrap class="pb-2 pt-2">
                <v-flex md6 class="pr-2">
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    persistent-hint
                    v-model="demurrageTimes[demurrageIndex].startTime"
                    :disabled="readOnly"
                    :rules="[validate.twentyFourHourTime]"
                    label="24-hour time"
                    mask="##:##"
                    @input="checkOverlaps"
                    :error-messages="errorMessages[demurrageIndex].start"
                    :hint="`Arrival (${dateFromEpoch(demurrageIndex, true)})`"
                    @focus="$event.target.select()"
                  />
                </v-flex>
                <v-flex md6 class="pl-2">
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    v-model="demurrageTimes[demurrageIndex].endTime"
                    :disabled="readOnly"
                    :rules="[validate.twentyFourHourTime]"
                    label="24-hour time"
                    @input="checkOverlaps"
                    mask="##:##"
                    persistent-hint
                    :error-messages="errorMessages[demurrageIndex].end"
                    :hint="`Departure (${dateFromEpoch(
                      demurrageIndex,
                      false,
                    )})`"
                    @focus="$event.target.select()"
                  />
                </v-flex>
                <v-flex
                  md12
                  v-if="demurrageIndex !== demurrageTimes.length - 1"
                >
                  <v-divider class="my-1" />
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
          <v-layout
            row
            wrap
            v-if="!readOnly && isUnitRate"
            class="unit-rate-container px-4"
          >
            <v-flex md6 class="pr-2">
              <h3 class="subheader--light">Client Demurrage Rate</h3>
              <v-text-field
                solo
                flat
                class="v-solo-custom pt-2"
                persistent-hint
                label="Client Demurrage Rate (per hour)"
                hint="Client Demurrage Rate (per hour)"
                :prefix="'$'"
                type="number"
                :disabled="readOnly"
                v-model.number="clientDemurrageUnitRate"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-flex>
            <v-flex md6 class="pl-2">
              <h3 class="subheader--light">Fleet Demurrage Rate</h3>
              <v-text-field
                solo
                flat
                class="v-solo-custom pt-2"
                persistent-hint
                label="Fleet Demurrage Rate (per hour)"
                hint="Fleet Demurrage Rate (per hour)"
                :prefix="'$'"
                type="number"
                :disabled="readOnly"
                v-model.number="fleetDemurrageUnitRate"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-flex>
          </v-layout>
          <v-divider class="mt-2" v-if="!readOnly"></v-divider>

          <v-layout v-if="!readOnly" align-center justify-end>
            <v-btn depressed color="info" @click="updateDemurrageTimes">
              Finish
            </v-btn>
          </v-layout>
        </v-form>
      </div>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import { returnDurationFromMilliseconds } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { computeDemurrageBreakOverlap } from '@/helpers/RateHelpers/DemurrageHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { DemurrageRateData } from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, Ref, ref } from 'vue';

interface DemurrageTimes {
  pudId: string;
  startTime: string;
  endTime: string;
  breakOverlapDurationInMilliseconds: number;
}

interface ErrorMessage {
  start: string;
  end: string;
}

/**
 * When isClient props is True, `demurrageRateData` is ClientDemurrages else demurrageRateData is FleetDemurrages
 * when `demurrageRateData` is ClientDemurrage, `fleetDemurrageRateData` is passed with FleetDemurrages
 * when `demurrageRateData` is FleetDemurrage, `clientDemurrageRateData` is passed with ClientDemurrages
 */
const props = withDefaults(
  defineProps<{
    demurrageRateData: DemurrageRateData[];
    pudItems: PUDItem[];
    readOnly?: boolean;
    isClient?: boolean;
    unitRates?: UnitRate[];
    workDiaryList?: WorkDiarySummary[];
    fleetDemurrageRateData?: DemurrageRateData[];
    clientDemurrageRateData?: DemurrageRateData[];
  }>(),
  {
    readOnly: true,
    isClient: false,
    unitRates: () => [],
    workDiaryList: () => [],
    fleetDemurrageRateData: () => [],
    clientDemurrageRateData: () => [],
  },
);

const emit = defineEmits<{
  (e: 'refreshAccountingTotals'): void;
}>();

const validate = validationRules;

const companyDetailsStore = useCompanyDetailsStore();

const demurrageTimes: Ref<DemurrageTimes[]> = ref([]);
const dialogIsOpened: Ref<boolean> = ref(false);
const clientDemurrageUnitRate: Ref<number> = ref(0.0);
const fleetDemurrageUnitRate: Ref<number> = ref(0.0);

const errorMessages: Ref<ErrorMessage[]> = ref([]);
const demurrageDurationForm: Ref<any> = ref(null);

const isUnitRate = computed(() => {
  return props.unitRates.length > 0;
});

const dialogIsOpen = computed({
  get: () => dialogIsOpened.value,
  set: (value: boolean) => {
    dialogIsOpened.value = value;
    if (value) {
      setInitialDemurrageTimes();
      setDemurrageUnitRate();
    }
  },
});

// sync demurrage unit rate from client/fleet demurrage unit rate input in dialog
function setDemurrageUnitRate(): void {
  if (props.clientDemurrageRateData.length > 0) {
    clientDemurrageUnitRate.value = props.clientDemurrageRateData[0].rate;
    fleetDemurrageUnitRate.value = props.demurrageRateData[0].rate;
  } else if (props.fleetDemurrageRateData.length > 0) {
    fleetDemurrageUnitRate.value = props.fleetDemurrageRateData[0].rate;
    clientDemurrageUnitRate.value = props.demurrageRateData[0].rate;
  }
}

// grab values from accounting demurrageRateData and set local state so user can edit values
function setInitialDemurrageTimes(): void {
  // set error message array. These error messages are used to show error messages when demurrage times overlap.
  for (let i = 0; i < props.demurrageRateData.length; i++) {
    errorMessages.value.push({ start: '', end: '' });
  }

  demurrageTimes.value = props.demurrageRateData.map((x: DemurrageRateData) => {
    return {
      pudId: x.pudId,
      startTime: moment(x.startTimeInEpoch)
        .tz(companyDetailsStore.userLocale)
        .format('HH:mm'),
      endTime: moment(x.endTimeInEpoch)
        .tz(companyDetailsStore.userLocale)
        .format('HH:mm'),
      breakOverlapDurationInMilliseconds: x.breakOverlapDurationInMilliseconds
        ? x.breakOverlapDurationInMilliseconds
        : 0,
    };
  });
  checkOverlaps();
}

// When the dialog is open, return a map of each pudIds and its respective break overlap
const demurrageOverlapsByPud: ComputedRef<Map<string, number> | null> =
  computed(() => {
    if (!dialogIsOpen.value || !demurrageTimes.value || !props.workDiaryList) {
      return null;
    }
    const overlapMap: Map<string, number> = new Map<string, number>();
    demurrageTimes.value.forEach((demurrage, index) => {
      const arrivalDate: string = moment(
        props.demurrageRateData[index].startTimeInEpoch,
      )
        .tz(companyDetailsStore.userLocale)
        .format('DDMMYYYY');

      const arrivalInputDate = moment
        .tz(
          arrivalDate + ' ' + demurrage.startTime,
          'DDMMYYYY HHmm',
          companyDetailsStore.userLocale,
        )
        .valueOf();

      const departureInputDate = moment
        .tz(
          arrivalDate + ' ' + demurrage.endTime,
          'DDMMYYYY HHmm',
          companyDetailsStore.userLocale,
        )
        .valueOf();
      let totalDur = 0;
      props.workDiaryList.forEach((w) => {
        if (props.isClient && !w.chargeClient) {
          totalDur += w.returnOverlapMillis(
            arrivalInputDate,
            departureInputDate,
          );
        }
        if (!props.isClient && !w.payFleetAsset) {
          totalDur += w.returnOverlapMillis(
            arrivalInputDate,
            departureInputDate,
          );
        }
      });
      overlapMap.set(demurrage.pudId, totalDur);
    });
    return overlapMap;
  });

function dateFromEpoch(index: number, isArrival: boolean) {
  const epoch = isArrival
    ? props.demurrageRateData[index].startTimeInEpoch
    : props.demurrageRateData[index].endTimeInEpoch;
  return moment(epoch).tz(companyDetailsStore.userLocale).format('DD/MM/YYYY');
}

const overlapExists = computed(() => {
  let overlapExists: boolean = false;
  for (let i = 0; i < errorMessages.value.length; i++) {
    overlapExists =
      errorMessages.value[i].start !== '' || errorMessages.value[i].end !== '';

    if (overlapExists) {
      break;
    }
  }
  return overlapExists;
});

// method that calls our overlap validation. This will validate against all fields and is called when a time input is updated.
function checkOverlaps() {
  for (let i = 0; i < demurrageTimes.value.length; i++) {
    overlapValidation(demurrageTimes.value[i].startTime.toString(), i, true);
    overlapValidation(demurrageTimes.value[i].endTime.toString(), i, false);
  }
}

// checks if there are overlapping times entered. If there are it will set our errorMessage array.
function overlapValidation(value: string, index: number, isStartTime: boolean) {
  // if the time entered does not match 24hr time we should not check overlapValidation. We also do not apply validation against the very first and very last time.
  if (!value || value.length < 4 || (index === 0 && isStartTime)) {
    return;
  }

  // convert inputed date to Epoch
  const input24HrTime = value;
  const inputDateFormatted = moment(
    props.demurrageRateData[index].startTimeInEpoch,
  )
    .tz(companyDetailsStore.userLocale)
    .format('DDMMYYYY');
  const inputDate = moment
    .tz(
      inputDateFormatted + ' ' + input24HrTime,
      'DDMMYYYY HHmm',
      companyDetailsStore.userLocale,
    )
    .valueOf();

  // convert the previous time to check against to Epoch
  const previous24HrTime: string =
    isStartTime && index !== 0
      ? demurrageTimes.value[index - 1].endTime
      : isStartTime && index === 0
        ? demurrageTimes.value[index].startTime
        : demurrageTimes.value[index].startTime;

  const previousDateFormatted =
    isStartTime && index !== 0
      ? moment(props.demurrageRateData[index].endTimeInEpoch)
          .tz(companyDetailsStore.userLocale)
          .format('DDMMYYYY')
      : moment(props.demurrageRateData[index].startTimeInEpoch)
          .tz(companyDetailsStore.userLocale)
          .format('DDMMYYYY');

  const previousDate = moment
    .tz(
      previousDateFormatted + ' ' + previous24HrTime,
      'DDMMYYYY HHmm',
      companyDetailsStore.userLocale,
    )
    .valueOf();

  const next24HrTime =
    !isStartTime && index !== demurrageTimes.value.length - 1
      ? demurrageTimes.value[index + 1].startTime
      : !isStartTime && index === demurrageTimes.value.length - 1
        ? demurrageTimes.value[index].endTime
        : demurrageTimes.value[index].endTime;

  const nextDateFormatted = isStartTime
    ? moment(props.demurrageRateData[index].endTimeInEpoch)
        .tz(companyDetailsStore.userLocale)
        .format('DDMMYYYY')
    : moment(props.demurrageRateData[index].startTimeInEpoch)
        .tz(companyDetailsStore.userLocale)
        .format('DDMMYYYY');

  const nextDate = moment
    .tz(
      nextDateFormatted + ' ' + next24HrTime,
      'DDMMYYYY HHmm',
      companyDetailsStore.userLocale,
    )
    .valueOf();

  if (inputDate < previousDate || inputDate > nextDate) {
    const message: string = 'Time overlap not allowed.';
    if (isStartTime) {
      errorMessages.value[index].start = message;
    } else {
      errorMessages.value[index].end = message;
    }
  } else {
    if (isStartTime) {
      errorMessages.value[index].start = '';
    } else {
      errorMessages.value[index].end = '';
    }
  }
}
// Updates our demurrageRageData with the new edited values. Finishes with
// refreshing accounting totals
function updateDemurrageTimes(): void {
  if (!demurrageDurationForm.value.validate() || overlapExists.value) {
    showNotification(
      'Please fill in all required fields and verify no time overlaps exist.',
      { title: 'Add/Update Demurrage' },
    );
    return;
  }

  if (isUnitRate.value) {
    for (const unitRate of props.unitRates) {
      if (props.isClient) {
        unitRate.demurrage.rate = clientDemurrageUnitRate.value;
      } else {
        unitRate.demurrage.rate = fleetDemurrageUnitRate.value;
      }
    }
  }
  // we set the inputted value and calculate the duration
  demurrageTimes.value.forEach((x, index) => {
    const startDate = moment(props.demurrageRateData[index].startTimeInEpoch)
      .tz(companyDetailsStore.userLocale)
      .format('DDMMYYYY');

    const endDate = moment(props.demurrageRateData[index].endTimeInEpoch)
      .tz(companyDetailsStore.userLocale)
      .format('DDMMYYYY');

    const startEpochDateToSet = moment
      .tz(
        startDate + ' ' + x.startTime,
        'DDMMYYYY HHmm',
        companyDetailsStore.userLocale,
      )
      .valueOf();
    const endEpochDateToSet = moment
      .tz(
        endDate + ' ' + x.endTime,
        'DDMMYYYY HHmm',
        companyDetailsStore.userLocale,
      )
      .valueOf();

    props.demurrageRateData[index].startTimeInEpoch = startEpochDateToSet;
    props.demurrageRateData[index].endTimeInEpoch = endEpochDateToSet;

    props.demurrageRateData[index].breakOverlapDurationInMilliseconds =
      computeDemurrageBreakOverlap(
        startEpochDateToSet,
        endEpochDateToSet,
        props.workDiaryList,
        props.isClient ? RateEntityType.CLIENT : RateEntityType.FLEET_ASSET,
      );

    // Check isClient Prop and Set respective Demurrage Rate for client and fleet unit rate input
    if (isUnitRate.value) {
      if (props.isClient) {
        props.demurrageRateData[index].rate = clientDemurrageUnitRate.value;
        props.fleetDemurrageRateData[index].rate = fleetDemurrageUnitRate.value;
      } else {
        props.demurrageRateData[index].rate = fleetDemurrageUnitRate.value;
        props.clientDemurrageRateData[index].rate =
          clientDemurrageUnitRate.value;
      }
    }
  });
  emit('refreshAccountingTotals');
  closeDemurrageDuration();
}

function closeDemurrageDuration() {
  demurrageTimes.value = [];
  errorMessages.value = [];
  dialogIsOpen.value = false;
}

function pudInformation(pudId: string): string {
  const demurrage: DemurrageRateData | undefined = props.demurrageRateData.find(
    (x: DemurrageRateData) => x.pudId === pudId,
  );

  const graceTimeAllowed: string = demurrage
    ? moment.duration(demurrage.graceDurationInMilliseconds).hours() +
      'h ' +
      moment.duration(demurrage.graceDurationInMilliseconds).minutes() +
      'm'
    : '';
  const pudDetails: PUDItem | undefined = props.pudItems.find(
    (x: PUDItem) => x.pudId === pudId,
  );
  let legNumber: number = props.pudItems.findIndex(
    (x: PUDItem) => x.pudId === pudId,
  );
  legNumber = legNumber !== -1 ? legNumber + 1 : 0;
  return pudDetails
    ? pudDetails.address.suburb.toLowerCase().charAt(0).toUpperCase() +
        pudDetails.address.suburb.slice(1).toLowerCase() +
        ' (' +
        'Leg: ' +
        legNumber +
        ') ' +
        graceTimeAllowed +
        ' grace applied'
    : '-';
}

// returns the leg type as string from pudId
function pudLegType(pudId: string): string {
  const pudDetails: PUDItem | undefined = props.pudItems.find(
    (x: PUDItem) => x.pudId === pudId,
  );
  return pudDetails?.legTypeFlag ?? '';
}
</script>

<style lang="scss" scoped>
.P {
  height: 12px;
  width: 3px;
  margin-right: 6px;
  margin-top: 6px;
  border-radius: 50px;
  background-color: $pickup !important;
}
.D {
  height: 12px;
  width: 3px;
  margin-right: 6px;
  margin-top: 6px;
  border-radius: 50px;
  background-color: $drop-highlight !important;
}

.break-txt {
  font-weight: 400;

  .accent-text--primary {
    font-weight: 600;
  }
}

.unit-rate-container {
  margin-top: 12px;
  background-color: var(--background-color-300);
  border-top: 1px solid $translucent;
  padding: 14px 0;
}

.demurrage-icon {
  margin-left: 18px;
  border-radius: 100px;
  background-color: var(--background-color-200);
  color: $warning !important;
  transition: all 0.3s;

  &:hover {
    scale: 1.4;
  }
}
</style>
