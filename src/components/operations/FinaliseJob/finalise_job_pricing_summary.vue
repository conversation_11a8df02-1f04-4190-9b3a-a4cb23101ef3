<template>
  <v-layout pt-2 mb-2>
    <v-flex md12>
      <table class="pricing-summary-table">
        <thead>
          <tr class="cfc-row">
            <th class="cfc-column1">Category</th>
            <th class="cfc-column2">Item</th>
            <th class="cfc-column3">Qty</th>
            <th class="cfc-column3"></th>
            <th class="cfc-column4">Calculation</th>
            <th class="cfc-column5">Amount ($)</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in tableData"
            :key="`${item.service}-${index}`"
          >
            <td>
              <span>{{ item.service }}</span>
            </td>
            <td>
              <span>{{ item.zone }}</span>
              <span
                v-if="item.zoneRequired"
                class="error-chip accent-text--card error-type"
              >
                Required
              </span>
            </td>
            <td>
              <span>
                {{ item.rate }}
              </span>
              <v-tooltip bottom v-if="item.isFuelSurchargeApplied">
                <template v-slot:activator="{ on }">
                  <v-icon
                    size="10"
                    color="info"
                    v-on="on"
                    style="
                      cursor: pointer;
                      padding-left: 2px;
                      padding-bottom: 2px;
                    "
                  >
                    fa-regular fa-gas-pump
                  </v-icon>
                </template>

                <span>Fuel Surcharge Applied</span>
              </v-tooltip>
            </td>
            <td>
              <span>
                {{ item.quantity }}
              </span>
            </td>
            <td>{{ item.calculation }}</td>
            <td>
              {{ item.amount }}
            </td>
          </tr>
        </tbody>
      </table>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
interface TableData {
  service: string;
  zone: string;
  zoneRequired?: boolean;
  rate: string;
  quantity: string;
  quantityValue: number;
  additionalChargeId?: string;
  allowIncrement?: boolean;
  calculation: string;
  amount: string;
  isFuelSurchargeApplied?: boolean;
}

import {
  addPercentageTo,
  DisplayCurrencyValue,
  getPercentageOf,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import {
  computeTollChargeItemSubtotal,
  reorderAdditionalChargeItems,
} from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import {
  getAdjustedDistanceRateValues,
  returnRangeRateSummary,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import { isFuelSurchargeApplicable } from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import {
  isDistanceRateTypeObject,
  isZoneToZoneRateTypeObject,
} from '@/helpers/RateHelpers/RateTableItemHelpers';
import {
  timeRateDescription,
  timeRateMinCharge,
} from '@/helpers/RateHelpers/TimeRateHelpers';
import { returnZoneToZoneName } from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { returnServiceTypeLongNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { AdditionalChargeApplicationType } from '@/interface-models/AdditionalCharges/AdditionalChargeApplicationType';
import type { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeRateBasis } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import type { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { DistanceRateData } from '@/interface-models/Jobs/FinishedJobDetails/DistanceRate/DistanceRateData';
import { UnitRateData } from '@/interface-models/Jobs/FinishedJobDetails/UnitRateData';
import ZoneRateData from '@/interface-models/Jobs/FinishedJobDetails/zoneRateData';
import { ZoneToZoneRateData } from '@/interface-models/Jobs/FinishedJobDetails/ZoneToZoneRate/ZoneToZoneRateData';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { demurrageAppliesLabel } from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { DistanceRateType } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { MinChargeBasis } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/MinChargeBasis';
import { TimeRateType } from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import { UnitRate } from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { ZoneToZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateType';
import { useRootStore } from '@/store/modules/RootStore';
import moment from 'moment-timezone';
import { ComputedRef, Ref, computed, toRef } from 'vue';

const props = withDefaults(
  defineProps<{
    serviceTypeId: number;
    pudItems: PUDItem[];
    accounting: JobAccountingDetails;
    entityType?: EntityType | undefined;
  }>(),
  {
    entityType: EntityType.CLIENT,
  },
);

const jobAccountingDetails: Ref<JobAccountingDetails> = toRef(
  props,
  'accounting',
);

// set rates based on entity type
const entityRates: ComputedRef<JobPrimaryRate[]> = computed(() =>
  props.entityType === EntityType.CLIENT
    ? jobAccountingDetails.value.clientRates
    : jobAccountingDetails.value.fleetAssetRates,
);

const freightCharges = computed(() =>
  props.entityType === EntityType.CLIENT
    ? jobAccountingDetails.value.totals.subtotals.freightCharges.client
    : jobAccountingDetails.value.totals.subtotals.freightCharges.fleetAsset,
);

const fleetAssetTotal = DisplayCurrencyValue(
  props.accounting.totals.subtotals.freightChargeTotals.fleetAsset ?? 0,
);
const clientTotal = DisplayCurrencyValue(
  props.accounting.totals.subtotals.freightChargeTotals.client ?? 0,
);

const fuelSurcharge = computed(() =>
  props.entityType === EntityType.CLIENT
    ? props.accounting.additionalCharges.clientFuelSurcharge
        ?.appliedFuelSurchargeRate
    : props.accounting.additionalCharges.fleetAssetFuelSurcharge
        ?.appliedFuelSurchargeRate,
);

// checks if fuel surcharged is applied
function fuelIsApplied(appliedFuelSurchargeId: number): boolean {
  if (!fuelSurcharge.value) {
    return false;
  }

  const clientFuelSurcharge =
    props.accounting.additionalCharges.clientFuelSurcharge
      ?.appliedFuelSurchargeRate ?? 0;

  return isFuelSurchargeApplicable(appliedFuelSurchargeId, clientFuelSurcharge);
}

/**
 * Returns the first row of the table, containing 1 or more rows describing
 * current selected rate.
 */
function returnClientRateTableRow(
  accounting: JobAccountingDetails,
): TableData[] {
  let rows: TableData[] = [];

  try {
    if (!entityRates.value || entityRates.value.length === 0) {
      return [
        {
          service: 'Select pricing information',
          zone: '',
          rate: '',
          quantity: '',
          quantityValue: 0,
          calculation: '',
          amount: 'TBD',
          isFuelSurchargeApplied: false,
        },
      ];
    }
    const rateTableItem = entityRates.value[0].rate;
    const rateTypeId: number = rateTableItem.rateTypeId;
    const serviceCode = returnServiceTypeLongNameFromId(props.serviceTypeId);
    const rateData = computed(() =>
      props.entityType === EntityType.CLIENT
        ? accounting.finishedJobData.clientRateData
        : accounting.finishedJobData.fleetAssetRateData,
    );

    const clientVariancePct =
      accounting.clientServiceRateVariations?.clientAdjustmentPercentage;

    switch (rateTypeId) {
      case JobRateType.TIME: // TIME
        rows = returnTableRowsFromTimeRateData(
          props.pudItems,
          accounting,
          serviceCode,
        );
        break;
      case JobRateType.ZONE: // ZONE
        const zoneRateData = rateData.value as ZoneRateData[];
        rows = returnTableRowsFromZoneRateData(
          props.pudItems,
          zoneRateData,
          serviceCode,
          rateTableItem,
        );
        break;
      case JobRateType.DISTANCE: // DISTANCE
        const distanceRateData = rateData.value as DistanceRateData;
        rows = returnTableRowsFromDistanceRateData(
          rateTableItem,
          distanceRateData,
          serviceCode,
          clientVariancePct,
        );
        break;
      case JobRateType.UNIT: // UNIT
        const unitRateData = accounting.finishedJobData
          .clientRateData as UnitRateData;
        rows = returnTableRowsFromUnitRateData(
          props.pudItems,
          unitRateData,
          serviceCode,
          rateTableItem,
        );
        break;
      case JobRateType.TRIP: // TRIP/Quoted
        rows.push({
          service: 'Quoted Rate',
          zone: '',
          rate: '',
          quantity: '1',
          quantityValue: 1,
          calculation: rateTableItem.fuelSurcharge
            ? 'Fuel inclusive'
            : 'Fuel exclusive',
          amount: `$${DisplayCurrencyValue(freightCharges.value)}`,
          isFuelSurchargeApplied: rateTableItem.fuelSurcharge,
        });
        break;
      case JobRateType.ZONE_TO_ZONE: // ZONE TO ZONE
        const zoneToZoneRateData = rateData.value as ZoneToZoneRateData;
        rows = returnTableRowsFromZoneToZoneRateData(
          rateTableItem,
          zoneToZoneRateData,
          serviceCode,
          clientVariancePct,
        );
        break;
    }
  } catch (error) {
    // console.error('Error in returnClientRateTableRow:', error);
    rows = [
      {
        service: 'Error',
        zone: '',
        rate: '',
        quantity: '',
        quantityValue: 0,
        calculation: '',
        amount: error instanceof Error ? error.message : 'Unknown error',
        isFuelSurchargeApplied: false,
      },
    ];
  }

  return rows;
}

/**
 * Returns the rows for the table based on the time rate data. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param pudItems - The PUD items from the job
 * @param accounting - The job accounting info
 * @param serviceCode - The service type id
 */
function returnTableRowsFromTimeRateData(
  pudItems: PUDItem[],
  accounting: JobAccountingDetails,
  serviceCode: string,
): TableData[] {
  const demurrage = computed(() =>
    props.entityType === EntityType.CLIENT
      ? accounting.finishedJobData?.clientDurations
      : accounting.finishedJobData?.fleetAssetDurations,
  );
  const standbyChargeTotals = computed(() =>
    props.entityType === EntityType.CLIENT
      ? accounting.totals?.subtotals?.standbyChargeTotals?.client
      : accounting.totals?.subtotals?.standbyChargeTotals?.fleetAsset,
  );
  const clientRate = entityRates.value[0];
  const timeRateType = clientRate.rate.rateTypeObject as TimeRateType;
  const estimatedDuration = returnCorrectDuration(
    demurrage.value?.actualBilledDuration ?? 0,
  );
  const readableBilledDuration = demurrage.value?.readableBilledDuration;

  // Get the percentage discount/surcharge applied to this rate and service
  const variancePercentage =
    accounting.clientServiceRateVariations?.clientAdjustmentPercentage ?? 0;

  const rateDescription = timeRateDescription({
    rate: timeRateType.rate,
    multiplier: timeRateType.rateMultiplier,
    variancePct: variancePercentage,
  });

  const standbyDuration = demurrage.value?.readableStandbyDuration;
  const standbyDescription = timeRateDescription({
    rate: timeRateType.standbyRate,
    multiplier: timeRateType.standbyMultiplier,
    variancePct: variancePercentage,
  });
  const rows: TableData[] = [];
  rows.push({
    service: serviceCode,
    zone: '',
    rate: rateDescription,
    quantity: estimatedDuration
      ? `${estimatedDuration} - (Min. ${timeRateMinCharge(timeRateType)})`
      : 'N/A',
    quantityValue: 1,
    calculation: `${rateDescription} x ${readableBilledDuration}`,
    amount: `$${DisplayCurrencyValue(freightCharges.value)}`,
    isFuelSurchargeApplied: fuelIsApplied(timeRateType.appliedFuelSurchargeId),
  });
  if (standbyChargeTotals.value) {
    const standbyStops = pudItems
      .map((pudItem, idx) => {
        return pudItem.isStandbyRate ? `Stop #${idx + 1}` : '';
      })
      .filter((stop) => stop !== '')
      .join(', ');
    rows.push({
      service: 'Standby Charge',
      zone: standbyStops,
      rate: standbyDescription,
      quantity: standbyDuration ? `${standbyDuration} standby time` : 'N/A',
      quantityValue: 1,
      calculation: `${standbyDescription} x ${standbyDuration}`,
      amount: `$${DisplayCurrencyValue(standbyChargeTotals.value)}`,
      isFuelSurchargeApplied: timeRateType.standbyFuelSurchargeApplies,
    });
  }
  return rows;
}

/**
 * Returns the rows for the table for a DISTANCE rate type job. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param rateTableItem - The applicable rate table item
 * @param distanceRateData - The distance rate data from job finished data
 * @param serviceCode - The jobs service code
 */
function returnTableRowsFromDistanceRateData(
  rateTableItem: RateTableItems,
  distanceRateData: DistanceRateData,
  serviceCode: string,
  variancePct: number | null | undefined,
): TableData[] {
  const rows: TableData[] = [];

  if (
    !isDistanceRateTypeObject(
      rateTableItem.rateTypeId,
      rateTableItem.rateTypeObject,
    )
  ) {
    return [];
  }

  const distanceRateType: DistanceRateType =
    rateTableItem.rateTypeObject as DistanceRateType;

  if (distanceRateData.rangeSubtotals.length === 1) {
    // If there's only one element, we can assume it's the total charge and use
    // properties on the distanceRateData object. We also don't need to add an
    // additional first row or additional totals row.
    const subtotal = distanceRateData.rangeSubtotals[0];
    const foundRange = distanceRateType.rates.find(
      (rate) => rate.id === subtotal.rangeRateId,
    );
    if (foundRange) {
      let rate = '';
      let quantity = '';

      // Get the rate we will apply per km, applying the variance percentage if
      // provided
      const { rateToApply, minCharge, baseFreightCharge } =
        getAdjustedDistanceRateValues(
          foundRange,
          distanceRateType,
          variancePct,
        );

      if (distanceRateType.minChargeBasis === MinChargeBasis.AMOUNT) {
        rate = `$${rateToApply}/km (Min. $${minCharge})`;
        quantity = `${distanceRateData.editedTravelDistance} km`;
      } else {
        rate = `$${rateToApply}/km`;
        quantity = `${distanceRateData.editedTravelDistance}km (Min. ${minCharge}km)`;
      }

      // Add first summary row
      rows.push({
        service: serviceCode,
        zone: '',
        rate: '',
        quantity: quantity,
        quantityValue: 1,
        calculation: `Pre: ${distanceRateData.preJobTravel.distanceInKm}km, Post: ${distanceRateData.postJobTravel.distanceInKm}km`,
        amount: `Base Rate: $${DisplayCurrencyValue(baseFreightCharge)}`,
        isFuelSurchargeApplied: false,
      });

      rows.push({
        service: '',
        zone: 'All',
        rate: rate,
        quantity: `${distanceRateData.calculatedTotalDistance}km`,
        quantityValue: 1,
        calculation: `$${rateToApply}/km x ${distanceRateData.calculatedTotalDistance}km`,
        amount: `$${DisplayCurrencyValue(distanceRateData.chargeExclGst)}`,
        isFuelSurchargeApplied: fuelIsApplied(
          distanceRateType.appliedFuelSurchargeId,
        ),
      });
    }
  } else {
    let rate = '';
    let quantity = '';

    // Get the rate we will apply per km, applying the variance percentage if
    // provided
    const { minCharge, baseFreightCharge } = getAdjustedDistanceRateValues(
      { rate: 0 },
      distanceRateType,
      variancePct,
    );

    if (distanceRateType.minChargeBasis === MinChargeBasis.AMOUNT) {
      rate = `Various (Min. $${minCharge})`;
      quantity = `${distanceRateData.editedTravelDistance} km`;
    } else {
      rate = `Various`;
      quantity = `${distanceRateData.editedTravelDistance}km (Min. ${minCharge}km)`;
    }

    // Add first summary row
    rows.push({
      service: serviceCode,
      zone: '',
      rate: rate,
      quantity: quantity,
      quantityValue: 1,
      calculation: `Pre: ${distanceRateData.preJobTravel.distanceInKm}km, Post: ${distanceRateData.postJobTravel.distanceInKm}km`,
      amount: `Base Rate: $${DisplayCurrencyValue(baseFreightCharge)}`,
      isFuelSurchargeApplied: false,
    });

    // Iterate over rangeSubtotals and add a row each
    distanceRateData.rangeSubtotals.forEach((rangeSubtotals) => {
      const foundRangeIndex = distanceRateType.rates.findIndex(
        (rate) => rate.id === rangeSubtotals.rangeRateId,
      );
      if (foundRangeIndex === -1) {
        return;
      }
      const foundRange = distanceRateType.rates[foundRangeIndex];

      // Get the rate we will apply per km, applying the variance percentage if
      // provided
      const rateToApply = addPercentageTo(foundRange.rate, variancePct ?? 0);
      rows.push({
        service: '',
        zone: `${returnRangeRateSummary(foundRange, foundRangeIndex)}`,
        rate: `$${rateToApply}/km`,
        quantity: `${rangeSubtotals.chargeableDistance}km`,
        quantityValue: 1,
        calculation: `$${rateToApply}/km x ${rangeSubtotals.chargeableDistance}km`,
        amount: `$${DisplayCurrencyValue(rangeSubtotals.chargeExclGst)}`,
        isFuelSurchargeApplied: fuelIsApplied(
          distanceRateType.appliedFuelSurchargeId,
        ),
      });
    });

    // Add totals row
    rows.push({
      service: '',
      zone: '',
      rate: '',
      quantity: '',
      quantityValue: 1,
      calculation: 'FREIGHT TOTAL',
      amount: `$${distanceRateData.chargeExclGst}`,
      isFuelSurchargeApplied: false,
    });
  }

  return rows;
}
/**
 * Returns the rows for the table for a ZONE rate type job. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param pudItems - The PUD items from the job
 * @param zoneRateData - The zone rate data from job finished data
 * @param serviceCode - The jobs service code
 */
function returnTableRowsFromZoneRateData(
  pudItems: PUDItem[],
  zoneRateData: ZoneRateData[],
  serviceCode: string,
  rateTableItem: RateTableItems,
): TableData[] {
  const isClientEntity = props.entityType === EntityType.CLIENT;

  const zoneRateTableItem: ZoneRateType[] =
    rateTableItem.rateTypeObject as ZoneRateType[];

  if (isClientEntity) {
    return pudItems.map((pudItem, index) => {
      const associatedZone = zoneRateData.find((zoneRate) =>
        pudItem.pudId
          ? zoneRate.pudId === pudItem.pudId
          : zoneRate.pudId === index.toString(),
      );

      if (!associatedZone) {
        return {
          service: index === 0 ? serviceCode : '',
          zone: `N/A`,
          zoneRequired: true,
          rate: '-',
          quantity: '-',
          quantityValue: 0,
          calculation: '-',
          amount: 'N/A',
          isFuelSurchargeApplied: false,
        };
      }
      const puds = pudItems.find((p) => p.pudId === pudItem.pudId);
      const zones = zoneRateTableItem.find(
        (p) => p.zoneName === associatedZone.rate,
      );
      const fuelSurchargeId = zones?.appliedFuelSurchargeId ?? 0;
      const stopLabel = `Stop #${index + 1}`;
      const suburb = puds?.address?.suburb || 'Unknown';

      const zoneRate = `$${DisplayCurrencyValue(
        associatedZone.freightChargesTotalExclGst,
      )}`;
      const zoneName = associatedZone.rate;

      return {
        service: index === 0 ? serviceCode : '',
        zone: `${stopLabel}: ${suburb}`,
        rate: `Zone ${associatedZone.zoneId}`,
        quantity: `${zoneName}`,
        quantityValue: 1,
        calculation: '',
        amount: `${zoneRate}`,
        isFuelSurchargeApplied: fuelIsApplied(fuelSurchargeId),
      };
    });
  }

  // FLEET ASSET ZONE RATE SUMMARY ROW
  const fleetAssetRatePercentage = zoneRateData[0].rate;

  const calculation = `${fleetAssetRatePercentage} x $${clientTotal}`;

  return [
    {
      service: serviceCode,
      zone: 'Zone Rate',
      rate: `${fleetAssetRatePercentage}`,
      quantity: `Client Total: $${clientTotal}`,
      quantityValue: 0,
      calculation,
      amount: `$${fleetAssetTotal}`,
      isFuelSurchargeApplied: fuelIsApplied(
        zoneRateTableItem[0].appliedFuelSurchargeId,
      ),
    },
  ];
}

/**
 * Returns the rows for the table for a unit rate job. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param pudItems - The PUD items from the job
 * @param unitRateData - The unit rate data from job finished data
 * @param serviceCode - The jobs service code
 */
function returnTableRowsFromUnitRateData(
  pudItems: PUDItem[],
  unitRateData: UnitRateData,
  serviceCode: string,
  rateTableItem: RateTableItems,
): TableData[] {
  const isClientEntity = props.entityType === EntityType.CLIENT;
  const unitRateType: UnitRate[] = rateTableItem.rateTypeObject as UnitRate[];

  if (isClientEntity) {
    return pudItems.map((pudItem, index) => {
      const stopNumber = `Stop #${index + 1}`;
      const associatedUnitData = unitRateData.zonedUnitRateData.find(
        (unitRate) =>
          pudItem.pudId
            ? unitRate.pudId === pudItem.pudId
            : unitRate.pudId === index.toString(),
      );

      if (!associatedUnitData) {
        return {
          service: 'N/A',
          zone: `${stopNumber}: `,
          zoneRequired: true,
          rate: '-',
          quantity: '-',
          quantityValue: 0,
          calculation: '-',
          amount: 'N/A',
          isFuelSurchargeApplied: false,
        };
      }

      const unitCount =
        pudItem.legTypeFlag === 'P'
          ? pudItem.rateDetails.unitPickUps || 0
          : pudItem.rateDetails.unitDropOffs || 0;

      // Format load and dangerous goods charges if they exist
      const loadCharges = associatedUnitData.loadChargesTotalExclGst
        ? `$${associatedUnitData.loadChargesTotalExclGst}`
        : '';
      const dgCharges = associatedUnitData.dangerousGoodsChargesTotalExclGst
        ? `$${associatedUnitData.dangerousGoodsChargesTotalExclGst}`
        : '';

      // Construct the zone description, including load charges and dangerous goods charges if applicable
      let zoneDescription = `${stopNumber}: ${associatedUnitData.unitTypeName}`;
      if (loadCharges || dgCharges) {
        const flagfallStr = loadCharges ? `Flagfall ${loadCharges}` : '';
        const dgStr = dgCharges ? `Dangerous Goods ${dgCharges}` : '';
        zoneDescription += ` (${[flagfallStr, dgStr]
          .filter(Boolean)
          .join(', ')})`;
      }

      const perUnitRate = `$${DisplayCurrencyValue(
        associatedUnitData.appliedRangeRate,
      )}`;
      const rateString = `${perUnitRate}/unit`;
      const zoneTotal = `$${DisplayCurrencyValue(
        associatedUnitData.zoneChargeExclGst,
      )}`;

      // Construct the calculation string
      const calculation = `${perUnitRate} x ${unitCount}`;
      const calcParts: string[] = [];
      if (loadCharges) {
        calcParts.push(`${loadCharges} (FF)`);
      }
      if (dgCharges) {
        calcParts.push(`${dgCharges} (DG)`);
      }
      if (loadCharges || dgCharges) {
        calcParts.push(`( ${calculation} )`);
      } else {
        calcParts.push(calculation);
      }
      const combinedCalculationStr = calcParts.join(' + ');

      const zones = unitRateType.find(
        (p) => p.zoneId === associatedUnitData.zoneId,
      );
      const fuelSurchargeId = zones?.appliedFuelSurchargeId ?? 0;

      return {
        service: index === 0 ? serviceCode : '',
        zone: zoneDescription,
        rate: rateString,
        quantity: `x ${unitCount}`,
        quantityValue: unitCount,
        calculation: combinedCalculationStr,
        amount: zoneTotal,
        isFuelSurchargeApplied: fuelIsApplied(fuelSurchargeId),
      };
    });
  }

  // FLEET ASSET UNIT RATE ROW
  const fleetAssetRatePercentage = getFleetAssetRatePercentage();
  const calculation = `$${clientTotal} x ${fleetAssetRatePercentage}%`;

  return [
    {
      service: serviceCode,
      zone: 'Unit Rate',
      rate: `${fleetAssetRatePercentage}%`,
      quantity: `Client Total: $${clientTotal}`,
      quantityValue: 0,
      calculation,
      amount: `$${fleetAssetTotal}`,
      isFuelSurchargeApplied: fuelIsApplied(
        unitRateType[0].appliedFuelSurchargeId,
      ),
    },
  ];
}

// function to return FleetAsset Unit Rate Percentage for Unit Rate row
function getFleetAssetRatePercentage(): string {
  const rateTypeObject =
    props.accounting.fleetAssetRates?.[0]?.rate?.rateTypeObject;
  if (Array.isArray(rateTypeObject) && rateTypeObject.length > 0) {
    const value = (rateTypeObject[0] as UnitRate).fleetAssetPercentage ?? 0;
    return DisplayCurrencyValue(value);
  }
  return '0';
}

/**
 * Returns the rows for the table for a ZONE TO ZONE rate type job. Returns a
 * list of TableData objects, each of which will form a row in the table.
 * @param rateTableItem - The applicable rate table item
 * @param zoneToZoneRateData - The distance rate data from job finished data
 * @param serviceCode - The jobs service code
 */
function returnTableRowsFromZoneToZoneRateData(
  rateTableItem: RateTableItems,
  zoneToZoneRateData: ZoneToZoneRateData,
  serviceCode: string,
  variancePct: number | null | undefined,
): TableData[] {
  const rows: TableData[] = [];

  if (
    !isZoneToZoneRateTypeObject(
      rateTableItem.rateTypeId,
      rateTableItem.rateTypeObject,
    )
  ) {
    return [];
  }

  const zoneToZoneRateType: ZoneToZoneRateType[] =
    rateTableItem.rateTypeObject as ZoneToZoneRateType[];

  zoneToZoneRateType.forEach((rateType, index) => {
    // Calculate the zone rate if any rate variation applies
    const rateWithVariation =
      rateType.rate + getPercentageOf(rateType.rate, variancePct ?? 0);

    rows.push({
      service: index === 0 ? serviceCode : '',
      zone: returnZoneToZoneName(rateType),
      rate: `$${DisplayCurrencyValue(rateWithVariation)}`,
      quantity: demurrageAppliesLabel(rateType.demurrage.appliedDemurrageId),
      quantityValue: 1,
      calculation: `$${DisplayCurrencyValue(rateWithVariation)}`,
      amount: `$${DisplayCurrencyValue(rateWithVariation)}`,
      isFuelSurchargeApplied: fuelIsApplied(rateType.appliedFuelSurchargeId),
    });
  });

  rows.push({
    service: '',
    zone: '',
    rate: '',
    quantity: '',
    quantityValue: 1,
    calculation: 'FREIGHT TOTAL',
    amount: `$${zoneToZoneRateData.chargeExclGst}`,
    isFuelSurchargeApplied: false,
  });

  return rows;
}

/**
 * Returns the rows for the table for any applicable outside metro charges. If
 * outside metro charges were applicable, returns a list containing one element
 * (the outside metro row). If not, returns an empty list.
 * @param accounting - The job accounting info, containing additional charge
 * info
 */
function returnOutsideMetroChargeRows(
  accounting: JobAccountingDetails,
): TableData[] {
  try {
    const outsideMetroChargeTotals =
      props.entityType === EntityType.CLIENT
        ? accounting.totals.subtotals.outsideMetroChargeTotals.client
        : accounting.totals.subtotals.outsideMetroChargeTotals.fleetAsset;

    const fuelSurcharges =
      props.entityType === EntityType.CLIENT
        ? accounting.totals.subtotals.fuelSurcharges.client
        : accounting.totals.subtotals.fuelSurcharges.fleetAsset;

    if (!entityRates.value[0].outsideMetroRate || !outsideMetroChargeTotals) {
      return [];
    }
    const freightChargeTotals = computed(() =>
      props.entityType === EntityType.CLIENT
        ? accounting.totals.subtotals.freightChargeTotals.client
        : accounting.totals.subtotals.freightChargeTotals.fleetAsset,
    );

    const standbyChargeTotals = computed(() =>
      props.entityType === EntityType.CLIENT
        ? accounting.totals.subtotals.standbyChargeTotals.client
        : accounting.totals.subtotals.standbyChargeTotals.fleetAsset,
    );

    const rate = entityRates.value[0].outsideMetroRate;
    const suburbCount = props.pudItems.filter(
      (pudItem) => pudItem.isOutsideMetro,
    ).length;

    const fuelSurchargeValue =
      (freightChargeTotals.value +
        outsideMetroChargeTotals +
        standbyChargeTotals.value) *
      (fuelSurcharges / 100);
    console.log('Fuel', fuelSurchargeValue);
    return [
      {
        service: 'Outside Metro Charge',
        zone: '',
        rate: `$${rate}%`,
        quantity: `${suburbCount} suburb(s)`,
        quantityValue: 1,
        calculation: `$${RoundCurrencyValue(
          freightChargeTotals.value - outsideMetroChargeTotals,
        )} x ${rate}%`,
        amount: `$${outsideMetroChargeTotals}`,
        isFuelSurchargeApplied: fuelSurchargeValue > 0,
      },
    ];
  } catch (e) {
    return [
      {
        service: 'Error',
        zone: 'Outside Metro',
        rate: '',
        quantity: '',
        quantityValue: 0,
        calculation: '',
        amount: e instanceof Error ? e.message : 'Unknown error',
        isFuelSurchargeApplied: false,
      },
    ];
  }
}

/**
 * Returns the rows for the table for any applicable additional charge items.
 * Returns a list of TableData objects, each of which will form a row in the
 * table.
 * @param accounting - The job accounting info, containing additional charge
 * @param types - The types of additional charges to include in the table
 * info
 */
function returnAdditionalChargeRows(
  accounting: JobAccountingDetails,
  types: AdditionalChargeApplicationType[],
): TableData[] {
  try {
    const tollChargeTypeId = useRootStore().tollChargeTypeId;

    const applicableChargeItems =
      accounting.additionalCharges.chargeList.filter((charge) => {
        const entityCharge = computed(() =>
          props.entityType === EntityType.CLIENT
            ? charge.client
            : charge.fleetAsset,
        );
        return (
          types.includes(entityCharge.value.appliesTo) &&
          !!entityCharge.value.charge &&
          entityCharge.value.charge > 0 &&
          charge.quantity > 0
        );
      });
    const sortedApplicableChargeItems = reorderAdditionalChargeItems(
      applicableChargeItems,
      tollChargeTypeId ?? '',
      tollAdminAndHandlingId.value ?? '',
    );

    const rows: TableData[] = sortedApplicableChargeItems.map((charge) => {
      const entityCharge = computed(() =>
        props.entityType === EntityType.CLIENT
          ? charge.client
          : charge.fleetAsset,
      );

      const foundChargeSubtotal =
        accounting.totals.subtotals.additionalChargeItems
          .flatMap((subtotal) => subtotal.items ?? [])
          .find((subtotal) => subtotal.chargeRef === charge._id);

      // Only show increment buttons for FIXED charge types
      const allowIncrement =
        entityCharge.value.chargeBasis === AdditionalChargeRateBasis.FIXED;
      let chargeValue =
        entityCharge.value.chargeBasis === AdditionalChargeRateBasis.FIXED
          ? `$${DisplayCurrencyValue(entityCharge.value.charge)}`
          : `${DisplayCurrencyValue(entityCharge.value.charge)}%`;

      let calculation = `${chargeValue} x ${charge.quantity}`;

      // If the additional charge item is a toll admin and handling charge, we
      // should display the percentage value from the charge in the store in
      // the rate columns
      if (charge._id === tollAdminAndHandlingId.value) {
        const tollAndHandlingItem =
          useRootStore().additionalChargeItemList.find(
            (x: AdditionalChargeItem) =>
              x._id === useRootStore().tollAdminAndHandlingId,
          );

        // Using the tollAdminAndHandlingId and tollChargeTypeId, we can
        // calculate the values that went into the calculations for toll admin
        // and handlin
        if (tollAndHandlingItem?._id && tollChargeTypeId) {
          const tollAndHandlingItemCharge = computed(() =>
            props.entityType === EntityType.CLIENT
              ? tollAndHandlingItem.client
              : tollAndHandlingItem.fleetAsset,
          );
          chargeValue = `${DisplayCurrencyValue(
            tollAndHandlingItemCharge.value.charge,
          )}%`;
          const tollChargeSubtotal = computeTollChargeItemSubtotal(
            applicableChargeItems,
            tollChargeTypeId,
            tollAndHandlingItem?._id,
          );
          calculation = `${chargeValue} x $${DisplayCurrencyValue(
            tollChargeSubtotal,
          )}`;
        }
      }

      const subTotal = computed(() =>
        props.entityType === EntityType.CLIENT
          ? foundChargeSubtotal?.total.client
          : foundChargeSubtotal?.total.fleetAsset,
      );

      return {
        service: charge.longName,
        zone: '',
        rate: chargeValue,
        quantity: `x ${charge.quantity}`,
        quantityValue: charge.quantity,
        additionalChargeId: charge._id,
        allowIncrement: allowIncrement,
        calculation: calculation,
        amount: `$${DisplayCurrencyValue(subTotal.value ?? 0)}`,
        isFuelSurchargeApplied: false,
      };
    });

    return rows;
  } catch (e) {
    return [
      {
        service: 'Error',
        zone: '',
        rate: '',
        quantity: e instanceof Error ? e.message : 'Unknown error',
        quantityValue: 0,
        calculation: '',
        amount: '',
        isFuelSurchargeApplied: false,
      },
    ];
  }
}

// return tool admin and handling id from root store
const tollAdminAndHandlingId: ComputedRef<string | undefined> = computed(() => {
  return useRootStore().tollAdminAndHandlingId;
});

/**
 * Returns the rows for the table for any applicable fuel charge. Returns TableData object, each of which will form a row in the table.
 * @param accounting - The job accounting info, containing fuel levy info
 */
function returnFuelRow(accounting: JobAccountingDetails): TableData {
  try {
    const amount = computed(() =>
      props.entityType === EntityType.CLIENT
        ? accounting.totals.subtotals.fuelSurcharges.client
        : accounting.totals.subtotals.fuelSurcharges.fleetAsset,
    );
    const isFuelApplied = entityRates.value[0].rate.isClientFuelApplied;
    const fuelRate = isFuelApplied
      ? DisplayCurrencyValue(fuelSurcharge.value ?? 0)
      : 0;

    const qty =
      props.entityType === EntityType.CLIENT
        ? `Client Total: $${clientTotal}`
        : `FleetAsset Total: $${fleetAssetTotal}`;
    const qtyValue =
      props.entityType === EntityType.CLIENT ? clientTotal : fleetAssetTotal;
    return {
      service: 'Fuel Surcharge',
      zone: '',
      rate: `${fuelRate}%`,
      quantity: qty,
      quantityValue: 1,
      calculation: isFuelApplied ? `${fuelRate}% x $${qtyValue}` : 'N/A',
      amount: `$${DisplayCurrencyValue(amount.value)}`,
      isFuelSurchargeApplied: false,
    };
  } catch (e) {
    return {
      service: 'Error',
      zone: '',
      rate: '',
      quantity: '',
      quantityValue: 0,
      calculation: '',
      amount: e instanceof Error ? e.message : 'Unknown error',
      isFuelSurchargeApplied: false,
    };
  }
}

/**
 * Returns the rows for the table for a demurrage rates. Returns a list of
 * TableData objects, each of which will form a row in the table.
 * @param pudItems - The PUD items from the job
 * @param accounting - The job accounting info
 */
function returnDemurrageRowsFromBreakdown(
  accounting: JobAccountingDetails,
  pudItems: PUDItem[],
): TableData[] {
  const demurrageRate =
    props.entityType === EntityType.CLIENT
      ? accounting.finishedJobData.clientDemurrageBreakdown
      : accounting.finishedJobData.fleetAssetDemurrageBreakdown;

  return demurrageRate.reduce<TableData[]>((acc, item, index) => {
    const rateNum = Number(item.rate);
    if (!rateNum || rateNum <= 0) {
      return acc;
    }

    const pudItem = pudItems.find((p) => p.pudId === item.pudId);
    const stopLabel = `Stop #${index + 1}`;
    const suburb = pudItem?.address?.suburb || 'Unknown';

    const duration = moment.duration(item.demurrageDurationInMilliseconds);
    const durationText = `${duration.hours()}h ${duration.minutes()}m`;

    const rate = `$${item.rate.toFixed(2)}/ph`;
    const amount = `$${item.demurrageChargeExclGst.toFixed(2)}`;
    const calculation = `${durationText} @ ${rate}`;

    acc.push({
      service: acc.length === 0 ? 'Demurrage' : '',
      zone: `${stopLabel} ${suburb}`,
      rate,
      quantity: durationText,
      quantityValue: item.demurrageDurationInMilliseconds,
      calculation,
      amount,
      isFuelSurchargeApplied: item.demurrageFuelSurchargeApplies,
    });

    return acc;
  }, []);
}

/**
 * Returns the data for the table, including all rows and totals. Combines all
 * the different row types into a single array, as well as adding rows for the
 * totals at the end.
 */
const tableData: ComputedRef<TableData[]> = computed(() => {
  const clientRateRows = returnClientRateTableRow(jobAccountingDetails.value);
  const lessGst = computed(() =>
    props.entityType === EntityType.CLIENT
      ? jobAccountingDetails.value.totals.subtotals.lessGst.client
      : jobAccountingDetails.value.totals.subtotals.lessGst.fleetAsset,
  );
  const finalTotal = computed(() =>
    props.entityType === EntityType.CLIENT
      ? jobAccountingDetails.value.totals.finalTotal.client
      : jobAccountingDetails.value.totals.finalTotal.fleetAsset,
  );
  return [
    ...clientRateRows,
    // returnDemurrageRow(jobAccountingDetails.value),
    ...returnDemurrageRowsFromBreakdown(
      jobAccountingDetails.value,
      props.pudItems,
    ),
    ...returnAdditionalChargeRows(jobAccountingDetails.value, [
      AdditionalChargeApplicationType.FREIGHT,
    ]),
    ...returnOutsideMetroChargeRows(jobAccountingDetails.value),
    returnFuelRow(jobAccountingDetails.value),
    ...returnAdditionalChargeRows(jobAccountingDetails.value, [
      AdditionalChargeApplicationType.NON_FREIGHT,
      AdditionalChargeApplicationType.CUSTOM,
    ]),
    {
      service: 'Total Ex GST',
      zone: '',
      rate: '',
      quantity: '',
      quantityValue: 1,
      calculation: 'Includes fuel',
      amount: `$${DisplayCurrencyValue(lessGst.value)}`,
      isFuelSurchargeApplied: false,
    },
    {
      service: 'Total Inc GST',
      zone: '',
      rate: '',
      quantity: '',
      quantityValue: 1,
      calculation: '',
      amount: `$${DisplayCurrencyValue(finalTotal.value)}`,
      isFuelSurchargeApplied: false,
    },
  ].filter((item) => item.service !== 'Error');
});
</script>

<style scoped lang="scss">
.pricing-summary-table {
  margin-top: -8px;
  width: 100%;
  font-family: $font-sans;
  color: var(--text-color);
  background-color: var(--background-color-300);
  border-collapse: collapse;

  th {
    font-weight: 500;
    font-size: $font-size-15;
    color: #eca31c;
    line-height: 1.5;
    padding: 12px;
    text-transform: uppercase;
    background-color: var(--background-color-400);
    border-bottom: 2px solid var(--background-color-600);
  }
  .cfc-row {
    .cfc-column1 {
      text-align: left;
    }

    .cfc-column2 {
      text-align: left;
    }

    .cfc-column3 {
      text-align: left;
    }

    .cfc-column4 {
      text-align: left;
    }
    .cfc-column5 {
      text-align: right;
    }
  }

  tbody {
    tr {
      position: relative;

      &:last-child {
        td {
          color: var(--warning);
        }
        td:first-child {
          color: var(--warning);
        }
      }

      &:hover {
        .tooltip-container {
          opacity: 1;
        }
      }
      td {
        padding: 8px;
        border-bottom: 1px solid var(--background-color-600);
        vertical-align: middle;
        font-weight: 500;
        letter-spacing: 0.5px;
        font-size: 1em;
        color: var(--light-text-color);

        &:first-child {
          letter-spacing: 0px;
          font-weight: 500;
          color: var(--text-color);
        }

        .error-chip {
          background-color: #ff4d4d;
          color: var(--text-color);
          padding: 2px 6px;
          border-radius: 4px;
          text-transform: uppercase;
          font-size: $font-size-12;
          font-weight: 600;
        }
      }

      .tooltip-container {
        opacity: 0;
        position: absolute;
        right: 0px;
        top: 50%;
        transform: translateY(-50%) translateX(90%);
      }

      .pl-2 {
        padding-left: 8px;
      }
    }
  }

  td {
    &:last-child {
      letter-spacing: 1px;
      font-weight: 500;
      text-align: right;
      font-size: 1em;
    }
  }
}
</style>
