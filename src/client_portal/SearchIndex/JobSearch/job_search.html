<div>
  <v-layout
    wrap
    pt-2
    class="job-search"
    :id="componentId"
    :class="isClientPortal ? '' : 'pa-4'"
  >
    <v-flex md12 class="job-search-header-section pa-2">
      <v-layout wrap class="mb-2">
        <v-flex md12>
          <v-form ref="jobSearchParametersForm">
            <v-layout wrap>
              <v-flex md2 pr-2 grow>
                <v-select
                  solo
                  flat
                  label="Search By"
                  v-model="selectedSearchByMenuId"
                  :items="searchByOptions"
                  item-value="id"
                  item-text="longName"
                  class="v-solo-custom"
                  hint="Search by"
                  persistent-hint
                  :disabled="viewingJobReport"
                  @change="selectOptionChanged"
                  color="orange"
                ></v-select>
              </v-flex>
              <v-flex md4 pr-2 grow>
                <v-select
                  v-if="selectedSearchByMenuId === 1"
                  solo
                  flat
                  label="Job Progress"
                  hint="Job Progress"
                  v-model="progressTypeId"
                  :items="progressMenuItems"
                  item-value="id"
                  item-text="longName"
                  :rules="[validate.required]"
                  class="v-solo-custom"
                  :disabled="viewingJobReport"
                  persistent-hint
                >
                </v-select>
                <v-layout v-if="selectedSearchByMenuId === 2" align-center>
                  <v-text-field
                    solo
                    flat
                    label="Job ID"
                    :class="!isClientPortal ? 'v-solo-custom' : ''"
                    :rules="[validate.required]"
                    v-model="searchJobRequest.jobId"
                    :disabled="viewingJobReport"
                  >
                  </v-text-field>
                </v-layout>

                <v-autocomplete
                  id="client-job-select"
                  v-if="selectedSearchByMenuId === 7"
                  solo
                  flat
                  v-model.sync="searchJobRequest.clientId"
                  :items="clientSelectList"
                  item-value="clientId"
                  item-text="clientDisplayName"
                  label="Client Select"
                  hint="Client Select"
                  class="v-solo-custom"
                  browser-autocomplete="off"
                  :allow-overflow="false"
                  hide-selected
                  color="orange"
                  cache-items
                  clearable
                  persistent-hint
                  :disabled="viewingJobReport"
                >
                  <template slot="item" slot-scope="{ item }">
                    <div style="display: flex; justify-content: space-between">
                      <span>{{ item.clientDisplayName }}</span>
                      <span v-if="item.clientStatus">
                        &nbsp |&nbsp
                        <span :style="{ color: item.color }"
                          >{{ item.clientStatus }}</span
                        >
                      </span>
                    </div>
                  </template>
                </v-autocomplete>

                <v-text-field
                  solo
                  :class="!isClientPortal ? 'v-solo-custom' : ''"
                  flat
                  hint="Reference"
                  persistent-hint
                  v-if="selectedSearchByMenuId === 3"
                  label="Reference"
                  :rules="[validate.required]"
                  v-model="searchJobRequest.jobReference"
                  :disabled="viewingJobReport"
                >
                </v-text-field>

                <v-text-field
                  solo
                  flat
                  hint="Dispatcher Name"
                  persistent-hint
                  :class="!isClientPortal ? 'v-solo-custom' : ''"
                  label="Dispatcher Name"
                  v-if="selectedSearchByMenuId === 4"
                  :rules="[validate.required]"
                  v-model="searchJobRequest.dispatcherName"
                  :disabled="viewingJobReport"
                >
                </v-text-field>

                <v-text-field
                  solo
                  flat
                  persistent-hint
                  :class="!isClientPortal ? 'v-solo-custom' : ''"
                  hint="Customer Name"
                  v-if="selectedSearchByMenuId === 5"
                  label="Customer Name"
                  :rules="[validate.required]"
                  v-model="searchJobRequest.customerDeliveryName"
                  :disabled="viewingJobReport"
                >
                </v-text-field>
                <v-text-field
                  solo
                  flat
                  persistent-hint
                  :class="!isClientPortal ? 'v-solo-custom' : ''"
                  hint="Invoice Number"
                  v-if="selectedSearchByMenuId === 8"
                  label="Invoice Number"
                  :rules="[validate.required]"
                  v-model.trim="searchJobRequest.invoiceId"
                  :disabled="viewingJobReport"
                >
                </v-text-field>
              </v-flex>
              <v-flex md3 pr-2 grow>
                <DatePickerBasic
                  solo
                  flat
                  :key="selectedSearchByMenuId"
                  @setEpoch="setStartDate"
                  :soloInput="true"
                  :labelName="'Start Date'"
                  :formDisabled="viewingJobReport || selectedSearchByMenuId === 2"
                  :yearOnly="false"
                  :clearable="true"
                  :epochTime="searchJobRequest.startEpoch"
                  :isClientPortal="isClientPortal"
                />
              </v-flex>
              <v-flex md3 pr-2 grow>
                <DatePickerBasic
                  solo
                  flat
                  :key="selectedSearchByMenuId"
                  @setEpoch="setEndDate"
                  :soloInput="true"
                  :labelName="'End Date'"
                  :clearable="true"
                  :formDisabled="viewingJobReport || selectedSearchByMenuId === 2"
                  :yearOnly="false"
                  :epochTime="searchJobRequest.endEpoch"
                  :isClientPortal="isClientPortal"
                />
              </v-flex>
            </v-layout>
            <v-layout wrap>
              <v-flex md3 pr-2 grow>
                <SelectEntity
                  solo
                  flat
                  v-if="selectedSearchByMenuId === 7"
                  :entityTypes="[entityType.FLEET_ASSET]"
                  :id.sync="searchJobRequest.fleetAssetId"
                  :disabled="viewingJobReport"
                />
              </v-flex>
              <v-flex md2 pr-2 grow>
                <v-text-field
                  solo
                  flat
                  hint="Reference"
                  persistent-hint
                  :class="!isClientPortal ? 'v-solo-custom' : ''"
                  v-if="selectedSearchByMenuId === 7"
                  label="Reference"
                  v-model="searchJobRequest.jobReference"
                  :disabled="viewingJobReport"
                >
                </v-text-field>
              </v-flex>
              <v-flex md2 pr-2 grow>
                <v-text-field
                  solo
                  flat
                  persistent-hint
                  hint="Customer Name"
                  v-if="selectedSearchByMenuId === 7"
                  label="Customer Name"
                  :class="!isClientPortal ? 'v-solo-custom' : ''"
                  v-model="searchJobRequest.customerDeliveryName"
                  :disabled="viewingJobReport"
                >
                </v-text-field>
              </v-flex>
              <v-flex md2 pr-2 grow>
                <v-text-field
                  solo
                  flat
                  persistent-hint
                  hint="Suburb"
                  v-if="selectedSearchByMenuId === 7"
                  label="Suburb"
                  :class="!isClientPortal ? 'v-solo-custom' : ''"
                  v-model="searchJobRequest.suburbName"
                  :disabled="viewingJobReport"
                >
                </v-text-field>
              </v-flex>
              <v-flex pr-4 mr-2>
                <v-btn
                  block
                  @click="runQuery"
                  v-if="!viewingJobReport"
                  :disabled="viewingJobReport || !allDataLoaded"
                  class="view-details-button"
                >
                  <v-icon class="pr-2" size="18">search</v-icon>
                  Search Job</v-btn
                >
                <p class="btn-hint" v-if="selectedSearchByMenuId === 7">
                  Press Enter or click to search.
                </p>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>

        <v-flex md12>
          <v-divider></v-divider>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md2>
              <v-btn
                solo
                flat
                outline
                class="v-btn-custom"
                @click="cancelJobReportView"
                v-if="viewingJobReport"
              >
                <v-icon class="px-2">arrow_back</v-icon
                ><span>Return to Results</span>
              </v-btn>
            </v-flex>
            <v-flex md2> </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex md12 v-show="!viewingJobReport && allDataLoaded">
      <v-data-table
        :headers="tableHeaders"
        :class="!isClientPortal ? 'accounting-data-table' : ''"
        :items="jobResultTableData"
        :loading="isLoadingTableData"
        :search="tableSearch"
        hide-actions
      >
        <v-progress-linear
          v-slot:progress
          color="#ffa000"
          indeterminate
        ></v-progress-linear>
        <template v-slot:items="props">
          <tr
            class="job-search-table-row"
            @click="viewJobDetails(props.item.jobId)"
            style="cursor: pointer"
          >
            <td>{{props.item.displayId}}</td>
            <td>{{formatDate(props.item.date, 'DD/MM/YY hh:mm a')}}</td>
            <td v-if="!isClientPortal">
              {{ props.item.clientName }} ({{props.item.clientId}})
            </td>
            <td>{{props.item.clientService}}</td>
            <td>{{props.item.reference}}</td>
            <td>
              {{props.item.from}}
              <template v-if="props.item.numberOfLegs > 2">
                → {{ ' +' + Math.max(0,props.item.numberOfLegs - 2) }}
              </template>
              <template v-if="props.item.to"> → {{ props.item.to }} </template>
            </td>
            <td>{{props.item.dispatcherName.toUpperCase()}}</td>
            <td v-if="!isClientPortal">{{props.item.driverName}}</td>
            <td v-if="!isClientPortal">{{props.item.csrAssignedId}}</td>

            <td v-if="!isClientPortal">{{ props.item.driverService }}</td>
            <td
              :class="props.item.workStatus === WorkStatus.REVIEWED ? 'warning-text-color' : ''"
            >
              {{ getBilledDuration(props.item.billedDuration,
              props.item.workStatus)}}
            </td>
            <td
              :class="props.item.workStatus === WorkStatus.REVIEWED ? 'warning-text-color' : ''"
            >
              {{showJobCharges(props.item.workStatus) ? "$" +
              displayCurrencyValue(props.item.clientCharge) : "-"}}
            </td>
            <td
              v-if="!isClientPortal"
              :class="props.item.workStatus === WorkStatus.REVIEWED ? 'warning-text-color' : ''"
            >
              {{props.item.driverPay > 0 ? "$" +
              displayCurrencyValue(props.item.driverPay) : "-"}}
            </td>
            <td>
              {{props.item.status}}
              <span class="red--text text--lighten-1" v-if="!isClientPortal"
                >{{props.item.serviceFailure ? '(SF)' : ""}}</span
              >
            </td>
          </tr>
        </template>
      </v-data-table>

      <v-layout justify-center>
        <Pagination
          @pageIncrement="pageIncrement"
          :pagination="pagination"
          @change="searchJob"
          :rowsPerPage.sync="rowsPerPage"
        />
      </v-layout>
    </v-flex>

    <v-flex md12 v-if="viewingJobReport && selectedJobDetails && allDataLoaded">
      <ClientAccessJobReport
        :jobDetails="selectedJobDetails"
        :clientDetails="clientDetails"
        :driverList="driverList"
        :fleetAssetList="fleetAssetList"
        :interfaceColors="interfaceColors"
      ></ClientAccessJobReport>
    </v-flex>
    <v-flex v-if="!allDataLoaded">
      <v-layout justify-center pa-4>
        <v-progress-circular
          :size="50"
          :color="interfaceColors.accent"
          indeterminate
        ></v-progress-circular>
      </v-layout>
    </v-flex>
    <job-details-dialog
      v-if="showJobDetailsDialog && selectedJobDetails"
      :jobDetails="selectedJobDetails"
      :showJobDetailsDialog="showJobDetailsDialog"
      :isJobSearchScreen="true"
    ></job-details-dialog>
  </v-layout>
</div>
